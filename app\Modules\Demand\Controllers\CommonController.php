<?php

namespace App\Modules\Demand\Controllers;


use App\Http\Controllers\Controller;
use App\Modules\Demand\Models\Code;
use App\Modules\Demand\Models\Employee;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Session;


class CommonController extends Controller
{
    protected $company_id;
    protected $user_id;

    public function __construct()
    {
        $this->user_id = Session::get('employee_id');
        $this->company_id = Session::get('CompanyId');
    }

    public static function fetchEmployees($includeTrashed = false)
    {
        $employees = Employee::where('company_id', Session::get('CompanyId'))
            ->with('orgs:org_unit_members.employee_id,org_units.id,org_units.payload->name as org_name')
            ->select('id', 'payload->name as name', 'payload->job_title as job_title', 'payload->employee_number as employee_number')
            ->when($includeTrashed, function ($query) {
                return $query->withTrashed();
            })
            ->get();

        return $employees->map(function ($employee) {
            return [
                'id' => $employee->id,
                'job_title' => $employee->job_title,
                'name' => $employee->name,
                'org_id' => $employee->orgs->pluck('id')->first(),
                'org_name' => $employee->orgs->pluck('org_name')->first(),
                'employee_number' => $employee->employee_number,
            ];
        });
    }

    //*分頁器
    public function arrayPaginator($array, $request)
    {
        $page = isset($request->page) ? $request->page : 1;
        $perPage = isset($request->per) ? intval($request->per) : 10;
        $offset = ($page * $perPage) - $perPage;

        return new LengthAwarePaginator(
            array_slice($array, $offset, $perPage),
            count($array),
            $perPage,
            $page,
            [
                'path' => $request->url(),
                'query' => array($request->query())
            ]
        );
    }

    public function settingEmployeeAgent(Request $request)
    {
        $agent = intval($request->id);
        $user = Employee::where('id', $this->user_id);
        $q = $user->first();
        $payload = $q->payload->put('agent', $agent);

        $user->update([
            'company_id' => $q->company_id,
            'payload' => $payload,
            'metadata' => $q->metadata,
        ]);
        return $payload;
    }
}
