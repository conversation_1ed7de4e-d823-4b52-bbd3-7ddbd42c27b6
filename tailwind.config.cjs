const colors = require("tailwindcss/colors");

module.exports = {
    mode: "jit",
    purge: [
        "./resources/**/*.blade.php",
        "./resources/**/*.js",
        "./resources/**/*.vue",
    ],
    darkMode: false, // or 'media' or 'class'
    theme: {
        extend: {
            backgroundColor: (theme) => ({
                primary: "#4F46E5",
                purple: colors.purple,
                // ...
            }),
            textColor: (theme) => ({
                primary: "#4F46E5",
                explorerPrimary: "#1D2939",
                // ...
            }),
            borderColor: (theme) => ({
                primary: "#4F46E5",
                warn: "#F0A9A7",
                explorerPrimary: "#1D2939",
                // ...
            }),
            screens: {
                xs: "376px",
            },
            height: {
                content: "720px",
            },
            minWidth: {
                table: "720px",
            },
            minHeight: {
                auto: "auto",
            },
            maxWidth: {
                filename: "712px",
            },
        },
    },
    variants: {
        extend: {
            backgroundColor: ["even"],
            display: ["last", "group-hover"],
            borderWidth: ["last"],
            borderRadius: ["first", "last"],
            margin: ["first", "last"],
            padding: ["first", "last"],
            width: ["first", "last"],
            flexGrow: ["first", "last"],
            cursor: ["active"],
        },
    },
    plugins: [],
};
