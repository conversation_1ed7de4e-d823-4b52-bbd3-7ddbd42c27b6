<?php

namespace App\Jobs;

use App\Modules\Demand\Models\Notification;
use App\Modules\par\Models\Reserve;
use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Session;

class MoveReserveToLog implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        try {
            DB::beginTransaction();

            $this->moveToLog();
            $this->cleanNotification();

            DB::commit();
        } catch (Exception $e) {
            DB::rollBack();
            Log::error($e);
        }
    }
    public function moveToLog()
    {
        $select = Reserve::withTrashed()
            ->select('company_id', 'employee_id', 'pa_id', 'type', 'payload', 'metadata', 'created_by', 'updated_by', 'created_at', 'updated_at', 'deleted_at')
            ->where('payload->date', '<', today(Session::get('timezone'))->toISOString());
        DB::table('reserve_logs')->insertUsing(
            [
                'company_id',
                'employee_id',
                'pa_id',
                'type',
                'payload',
                'metadata',
                'created_by',
                'updated_by',
                'created_at',
                'updated_at',
                'deleted_at',
            ],
            $select
        );
        $select->forceDelete();
    }
    public function cleanNotification()
    {
        Notification::where('employee_id', 0)
            ->where('payload->method', 'email')
            ->where('payload->end', '<', today()->toISOString())
            ->delete();
    }
}
