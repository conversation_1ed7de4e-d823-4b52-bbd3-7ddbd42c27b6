<?php

namespace App\Modules\Demand\Controllers;

use App\Events\DemandUpdated;
use App\Handlers\RefreshMaterializedViewDemandQuery;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Arr;
use App\Http\Controllers\Controller;
use App\Modules\Demand\Models\Demand;
use App\Modules\Demand\Models\Code;
use App\Modules\Demand\Models\CustomList;
use App\Modules\Demand\Models\DemandLayout;
use App\Modules\Demand\Models\DemandLog;
use App\Modules\Demand\Models\Employee;
use App\Modules\Demand\Models\OrgUnitMember;
use App\Modules\Demand\Services\SigningService;
use App\Traits\FormatDate;
use App\Traits\GetColumnValue;
use Carbon\Carbon;
use Exception;




class SigningController extends Controller
{

    use FormatDate;
    use GetColumnValue;
    public $company_id;
    public $user_id;
    public $codeTable;
    public $Submit;
    public $Common;
    protected $timezone;

    public function __construct()
    {
        $this->company_id = Session::get('CompanyId');
        $this->user_id = Session::get('employee_id');
        $this->timezone = Session::get('timezone');
        $this->codeTable = Code::where('code_kind', 'AA')->get();
        $this->Submit = new SubmitController;
        $this->Common = new CommonController;
    }

    public function fetchSignOffListsToPage(Request $request)
    {
        $demands = $this->fetchSignOffLists($request);
        return $demands;
    }

    public function fetchSignOffLists(Request $request)
    {
        $codeTable = $this->codeTable;
        $Submit = $this->Submit;
        $user = intval($this->user_id);
        $page = (int) $request->input('page') ?? 1;
        $per = $request->input('per') ?? 10;
        $keyword = $request->get('keyword') ?? false;

        $signOffPersonId = [
            'role_id' => $user,
            'apply_status' => 1,
        ];
        $signOffPersonProxyId = [
            'proxy_id' => $user,
            'apply_status' => 1,
        ];
        //撈出是否為代理人
        $representIds = Employee::withTrashed()->with('represents:id,payload')->find($user)->represents()->withTrashed()->pluck('id');
        // 撈取離職人員的部門
        $orgUnitMember = OrgUnitMember::withTrashed()->with('orgUnit')->get();
        $employees = CommonController::fetchEmployees($includeTrashed = true);
        $demands = Demand::with([
            'employee' => function ($q) {
                return $q->withTrashed();
            },
            'employee.orgs'
        ])
            ->whereIn('payload->status', [1, 4])
            ->where('payload->company_id', $this->company_id)
            ->where(function ($demand) use ($signOffPersonId, $signOffPersonProxyId) {
                $demand->whereJsonContains('payload->sign_roles', [$signOffPersonId])
                    ->orWhereJsonContains('payload->sign_roles', [$signOffPersonProxyId]);
            })
            ->when($keyword, function ($query) use ($keyword) {
                return  $query->where(function ($query) use ($keyword) {
                    $query->where('payload->layout_name', 'like', '%' . $keyword . '%')
                        ->orWhere('no', 'like', '%' . $keyword . '%')
                        ->orWhere(DB::raw("TO_CHAR(created_at,'YYYY/MM/DD')"), 'like', '%' . $keyword . '%')
                        ->orWhere('payload->applicant->name', 'like', '%' . $keyword . '%')
                        ->orWhere('payload->applicant->dep', 'like', '%' . $keyword . '%')
                        ->orWhere('payload->summaryValue', 'like', '%' . $keyword . '%');
                });
            })
            ->when($representIds, function ($query) use ($representIds, $user, $keyword) {
                $query->orWhere(function ($query) use ($representIds, $user) {
                    foreach ($representIds as $representId) {
                        $signOffPersonId = [
                            'role_id' => $representId,
                            'apply_status' => 1,
                        ];
                        $signOffPersonProxyId = [
                            'proxy_id' => $representId,
                            'apply_status' => 1,
                        ];
                        $query->orWhereJsonContains('payload->sign_roles', [$signOffPersonId])
                            ->orWhereJsonContains('payload->sign_roles', [$signOffPersonProxyId]);
                    }
                })
                    ->when($keyword, function ($query) use ($keyword) {
                        return  $query->where(function ($query) use ($keyword) {
                            $query->where('payload->layout_name', 'like', '%' . $keyword . '%')
                                ->orWhere('no', 'like', '%' . $keyword . '%')
                                ->orWhere(DB::raw("TO_CHAR(created_at,'YYYY/MM/DD')"), 'like', '%' . $keyword . '%')
                                ->orWhere('payload->applicant->name', 'like', '%' . $keyword . '%')
                                ->orWhere('payload->applicant->dep', 'like', '%' . $keyword . '%')
                                ->orWhere('payload->summaryValue', 'like', '%' . $keyword . '%');
                        });
                    });
            });

        $demands = $demands->orderByDesc("updated_at")->paginate($per, ['*'], 'page', $page);
        $demands->getCollection()
            ->transform(function ($demand) use ($codeTable, $Submit, $employees, $representIds, $orgUnitMember) {
                //流程設定
                $signRoles = [];
                $roles = $demand->payload['sign_roles'];
                $roleNames = collect($employees)->whereIn('id', collect($roles)->pluck('role_id'))->values();
                foreach ($roles as  $role) {
                    $emp = $roleNames->where('id', $role['role_id'])->first();
                    $role['name'] = $emp ? $emp['name'] : '';
                    if (isset($role['isRepresent']) && $role['isRepresent']) {
                        $role['name'] .= '(代)';
                    }
                    if ($role['apply_status'] >= 1) {
                        array_push($signRoles, $role);
                    }
                }

                $demand->org_name = $demand->employee->orgs->pluck('payload.name')->first() ?? $orgUnitMember->firstWhere('employee_id', $demand->employee->id)->orgUnit->payload['name'];

                $org_name = $demand->employee->orgs->pluck('payload.name')->first() ?? $orgUnitMember->firstWhere('employee_id', $demand->employee->id)->orgUnit->payload['name'];


                return [
                    'id' => $demand->id,
                    'name' => $demand->payload['layout_name'],
                    // 摘要
                    'summaryValue' =>  $demand->payload->get('summaryValue'),
                    'no' => $demand->no,
                    'sign_roles' =>  $signRoles,
                    'createdBy' => $demand->employee->payload['name'],
                    'created_title' => $demand->employee->payload['job_title'],
                    'org_name' => $org_name,
                    'created' => $Submit->signTimestamp(Carbon::parse($demand->created_at), $codeTable, ''),
                    'status' => $demand->payload['status'],
                    'is_child' =>  isset($demand->payload['is_child']) ? $demand->payload['is_child'] : false,
                    'applicant' => $demand->payload->get('applicant'),
                    'applicant_id' => $employees->firstWhere('id', $demand->created_by)['employee_number']
                ];
            });
        return $demands;
    }

    public function SignOffDetail(Request $request)
    {
        $codeTable = $this->codeTable;
        $Submit = $this->Submit;
        $user = intval($this->user_id);
        $id = $request->id;

        //撈出是否為代理人
        $representIds = Employee::withTrashed()->with('represents:id,payload')->find($user)->represents()->withTrashed()->pluck('id');

        $employees =  CommonController::fetchEmployees($includeTrashed = true);
        $demand = Demand::with([
            'customList',
            'layout' => function ($q) {
                return $q->withTrashed()->select('id', 'name', 'payload');
            },
            'employee' => function ($q) {
                return $q->withTrashed();
            },
            'employee.orgs'
        ])
            ->find($id);
        //流程設定
        $signRoles = [];
        $roles = $demand->payload['sign_roles'];
        $roleNames = collect($employees)->whereIn('id', collect($roles)->pluck('role_id'))->values();
        foreach ($roles as  $role) {
            $emp = $roleNames->where('id', $role['role_id'])->first();
            $role['name'] = $emp ? $emp['name'] : '';
            if (isset($role['isRepresent']) && $role['isRepresent']) {
                $role['name'] .= '(代)';
            }
            if ($role['apply_status'] >= 1) {
                array_push($signRoles, $role);
            }
        }
        //獲取該節點簽核設定
        $roleSet = collect($signRoles)->first(function ($value, $key) use ($representIds) {
            return $value['apply_status'] == 1 && ($value['role_id'] == $this->user_id || $representIds->contains($value['role_id']));
        });

        $child = $roleSet['child'];
        $document = $roleSet['document'];
        $countersigned = $roleSet['countersigned'];
        $canFallback = (isset($roleSet['to_counter']) && $roleSet['to_counter'] == 1) ? 0 : ((isset($roleSet['to_counter']) && $roleSet['to_counter'] == 2) ? 1 : 2);

        $forms = $demand->payload['forms'];
        $forms = collect($forms)->map(function ($form, $formIndex) use ($codeTable, $Submit, $demand) {
            $form['columns'] = collect($form['columns'])->map(function ($column, $columnIndex) use ($codeTable, $Submit, $demand, $formIndex) {

                if ($column['type'] == 'customList') {
                    $customList =  $demand->customList->findList($formIndex, $columnIndex);
                    $column['form_setting'] = $customList  ? $customList->payload->get('form_setting') : [];
                    $column['custom_list'] = $customList  ? $customList->list : [];
                }
                // if (isset($column['type']) && ($column['type'] == 'date' || $column['type'] == 'time') && isset($column['value'])) {
                //     try {
                //         $column['value'] = $Submit->signTimestamp(Carbon::parse($column['value']), $codeTable, '');
                //     } catch (Exception $e) {
                //         Log::error($column);
                //         Log::error($e);
                //     }
                // }
                return $column;
            });
            return $form;
        });

        $remarks = isset($demand->layout->payload['remarks']) ? $demand->layout->payload['remarks'] : null;

        return [
            'id' => $demand->id,
            'forms' => $forms,
            'child' => $child,
            'document' => $document,
            'countersigned' => $countersigned,
            'canFallback' => $canFallback,
            'remarks' => $remarks,
        ];
    }

    //簽核完
    public function submit(Request $request)
    {
        $user = intval($this->user_id);
        if (!$request->has('id', 'remark', 'status')) {
            return 0;
        }
        try {
            DB::beginTransaction();
            //forms:[{index:2,columns:[{id:1,value:1},{id:2,value:2}]},{3:[{id:1,value:1},{id:2,value:2}]}]
            $demand = Demand::where('id', $request->get('id'))->first();
            $layout = DemandLayout::where('id', $demand->layout_id)->withTrashed()->first();
            $layoutName = $demand->payload['layout_name'];
            $singRoles = $demand->payload['sign_roles'];
            $filterRoles = $demand->payload['filter_roles'] ?? [];
            $Notify = new NotificationController;
            $statusName = Code::where('code_kind', 'AI')->where('code_id', 'signed')->first();
            $statusName = $statusName ? $statusName->nm_zh_tw : '';
            //撈出是否為代理人
            $representIds = Employee::withTrashed()->with('represents:id,payload')->find($user)->represents()->withTrashed()->pluck('id');

            $forms = collect($demand->payload->get('forms'));
            $demandNo =  $demand->no;
            $requestForms = $request->get('forms');
            //欄位回填
            if ($request->has('forms') && count($request->get('forms')) > 0) {
                // $forms = collect($demand->payload->get('forms'));
                // $requestForms = $request->get('forms');

                // $customList = CustomList::where('demand_id',  $demand->id)->get();
                // $forms = $forms->map(function ($form, $index) use ($requestForms, &$customList) {
                //     foreach ($requestForms as $requestForm) {
                //         if ($requestForm['index'] == $index) {
                //             $form['columns'] = collect($form['columns'])->map(function ($c, $cIndex) use ($requestForm, $index, &$customList) {
                //                 foreach ($requestForm['columns'] as $column) {
                //                     if ($c['type'] == 'customList' && $c['id'] == $column['id']) {
                //                         $customList->findList($index, $cIndex)
                //                             ->setAttribute('list', $column['custom_list'])
                //                             ->save();
                //                     } else if ($c['id'] == $column['id']) {
                //                         $c['value'] = $column['value'];
                //                     }
                //                 }
                //                 return $c;
                //             });
                //         }
                //     }
                //     return $form;
                // });

                $forms = $demand->payload->get('forms');
                $requestForms = $request->get('forms');
                $customList = CustomList::where('demand_id',  $demand->id)->get();
                foreach ($requestForms as $requestForm) {
                    foreach ($requestForm['columns'] as $requestColumn) {
                        if ($requestColumn['type'] == 'customList') {
                            // 更新表單 custom_list
                            $colIndex = array_search($requestColumn['id'], array_column($forms[$requestForm['index']]['columns'], 'id'));
                            if($customList->findList($requestForm['index'], $colIndex)) {
                                $customList->findList($requestForm['index'], $colIndex)
                                ->setAttribute('list', $requestColumn['custom_list'])
                                ->save();
                            }
                        } else {
                            // 更新表單 values
                            foreach ($forms[$requestForm['index']]['columns'] as &$form_col) {
                                if ($form_col['id'] === $requestColumn['id']) {
                                    $form_col['value'] = $requestColumn['value'];
                                    break;
                                }
                            }
                        }
                    }
                }

                $demand->setAttribute('payload->forms', $forms);

                // 審核人金額判斷
                $tempstage = $this->moneyAddRoles($representIds, $requestForms, $singRoles, $filterRoles);
                $singRoles = $tempstage['singRoles'];
                // $filterRoles = $tempstage['filterRoles'];
                $demand->setAttribute('payload->filter_roles', $tempstage['filterRoles']);
                unset($tempstage);

                // 更新摘要
                $summaryColId = $layout->payload->get('summaryColId');
                if (isset($summaryColId)) {
                    $summaryColumn = null;
                    foreach ($forms[0]['columns'] as $col) {
                        if ($col["id"] === $summaryColId) {
                            $summaryColumn = $col;
                            break;
                        }
                    }
                    $summaryValue = $summaryColumn ?  $this->getValue($summaryColumn) : "";
                } else $summaryValue = '';
                $demand->setAttribute('payload->summaryValue', $summaryValue);
            }

            $count = count($singRoles);
            $FilesHadMoved = false;
            //審核關卡更新
            sameRole:
            foreach ($singRoles as $key => $role) {
                if (($role['role_id'] == $user || $representIds->contains($role['role_id'])) && $role['apply_status'] == 1) {
                    $singRoles[$key]['apply_status'] = $request->get('status');
                    $singRoles[$key]['isRepresent'] = $representIds->contains($role['role_id']) ? true : false;
                    $singRoles[$key]['role_id'] = $user;
                    $singRoles[$key]['remark'] = $request->get('remark');
                    $singRoles[$key]['timestamp'] = $this->Submit->signTimestamp(Carbon::now(), $this->codeTable, $statusName);
                    $singRoles[$key]['raw_time'] = Carbon::now();
                    if ($request->has('files') && count($request->get('files')) > 0) {
                        $Upload = new UploadController;
                        $files = $request->get('files');
                        foreach ($files as $k => $file) {
                            $lastPath = '/demand/upload/' . $demand->no . '/' . $file['base_name'];
                            $uploadResult = $Upload->moveFiles($lastPath, $file['base_name']);
                            if ($uploadResult || $FilesHadMoved) {
                                $FilesHadMoved = true;
                                $files[$k]['URL'] = $lastPath;
                                $files[$k]['path'] = '/' . $demand->no . '/' . $file['base_name'] . '/' . $file['name'];
                            }
                        }
                        $singRoles[$key]['document_info'] = $files;
                    }
                    //不是最後或駁回pa給下一關
                    if ($key + 1 < $count && $request->get('status') != 3) {

                        //如果是會簽就不是單純給下一關
                        if ($demand->payload->has('counterRoleIds') && in_array($user, $demand->payload->get('counterRoleIds'))) {
                            $counterRoleIds = $demand->payload->get('counterRoleIds');
                            //判斷其他會簽是不是簽完再回到申請人
                            $filtered = Arr::where($singRoles, function ($singRole, $key) use ($counterRoleIds) {
                                return in_array($singRole['role_id'], $counterRoleIds) && $singRole['apply_status'] == 1;
                            });
                            if (count($filtered) == 0) {
                                $index = null;
                                $first = Arr::first($singRoles, function ($singRole, $key) use (&$index) {
                                    if ($singRole['apply_status'] == 0)
                                        $index = $key;
                                    return $singRole['apply_status'] == 0;
                                });
                                $singRoles[$index]['apply_status'] = 1;
                            }
                        } else
                            $singRoles[$key + 1]['apply_status'] = 1;
                    }
                    //如果下一關也是同個人就一起簽
                    if (
                        $key + 1 < $count && $singRoles[$key + 1]['role_id'] == $role['role_id'] &&
                        (!isset($singRoles[$key + 1]['fill_column_ids']) || !count($singRoles[$key + 1]['fill_column_ids']))
                    ) {
                        goto sameRole;
                    }
                    if ($key + 1 < $count && $request->get('status') != 3) {
                        //發通知
                        $Notify->createNotify($singRoles[$key + 1]['role_id'], $layoutName, 1);
                    }
                    break;
                    // Log::debug($key);
                    // Log::debug($singRoles);
                    // Log::debug($role['role_id']);
                }
            }

            $lastStatus = $singRoles[$count - 1]['apply_status'];
            //判斷是否有中間駁回的簽核關卡
            $filtered = Arr::where($singRoles, function ($value, $key) {
                return $value['apply_status'] == 3;
            });
            //如果有中間駁回的簽核關卡 外層簽核狀態改顯示駁回
            if (Count($filtered) > 0 || $lastStatus == 3) {
                $demand->setAttribute('payload->status', 3);
                //發通知
                $Notify->createNotify($demand->created_by, $layoutName, 3);
            } else if ($lastStatus == 2) {
                //最後一關同意的話 外層改顯示同意
                $demand->setAttribute('payload->status', 2);
                //發通知
                $Notify->createNotify($demand->created_by, $layoutName, 2);
            };
            $demand->setAttribute('payload->sign_roles', $singRoles);

            $demand->save();
            //若是自動結案 要move到DemandLogTd
            $dl = DemandLayout::withTrashed()->where('id', $demand->layout_id)->first();
            if ($lastStatus == 2 && $dl->auto_finished) {
                $s = new SubmitController();
                $s->closeCase($request);
            }
            event(new DemandUpdated($demand));

            DB::commit();
            return 1;
        } catch (Exception $e) {
            DB::rollBack();
            Log::error($request->all());
            Log::error($e);
            return 0;
        } finally {
            RefreshMaterializedViewDemandQuery::handle();
        }
    }

    // 填寫金額 大於 金額判斷的金額 就把 節點加到流程，反之移除
    // 有金額判斷的流程，會再上一個節點留下紀錄作為塞節點的依據，若有連續的判斷則會塞在同一個節點中
    // ex: 流程 => 1 $ 3 $ $ 6  => 3 會有多個$ 在裡面
    private function moneyAddRoles($representIds, $requestForms, $singRoles, $filterRoles)
    {
        $tempSingRoles = [];
        $tempFilterRoles = [];
        foreach ($singRoles as $key => $role) {
            try {
                if (array_key_exists('raw_time', $role)) {
                    // 歷史記錄
                    array_push($tempSingRoles, $role);
                }
                // 審核人自己的節點 || (代理人 && 申請狀態為 1)
                else if (($role['role_id'] == $this->user_id || $representIds->contains($role['role_id'])) && $role['apply_status'] == 1) {

                    // 流程沒有金額判斷的欄位
                    if (!array_key_exists('dollar_col_id', $role)) {
                        array_push($tempSingRoles, $role);
                        continue;
                    }
                    // 判斷有沒有要填寫的欄位，且屬於金額判斷的欄位，沒有跳過
                    if (count($role['fill_column_ids']) == 0 || !in_array($role['dollar_col_id'], $role['fill_column_ids'])) {
                        array_push($tempSingRoles, $role);
                        continue;
                    }

                    // 回填欄位有沒有金額判斷欄位 沒有跳過
                    $requestColumns = collect($requestForms[0]['columns']);
                    if (!$requestColumns->contains('id', $role['dollar_col_id'])) {
                        array_push($tempSingRoles, $role);
                        continue;
                    }

                    $columnValue = $requestColumns->firstWhere('id', $role['dollar_col_id']);
                    $currentRoleId = $role['id'];
                    // 如果回填的form中有金額判斷的欄位，目前節點以下的節點都要跑
                    // 確認是否需要插入或移除節點
                    $moneyFilterIndex = 0;
                    foreach ($singRoles as $role_key => $role_value) {
                        if ($currentRoleId > $role_value['id']) continue;
                        // 若money_filters有值 檢查需不需要插入節點
                        if (array_key_exists('money_filters', $role_value)) {

                            // money_filter節點
                            array_push($tempSingRoles, $role_value);
                            // index使用
                            $moneyFilterIndex = count($tempSingRoles) - 1;
                            if (count($role_value['money_filters']) == 0) continue;

                            foreach ($role_value['money_filters'] as $money_key => $money_value) {

                                $filterArr = Arr::where($filterRoles, function ($item) use ($money_value, $columnValue) {
                                    //  填寫金額小於金額判斷
                                    if ($columnValue['value'] < $item['dollar']) return false;
                                    if (!array_key_exists('dollar', $item)) return false;
                                    return $item['id'] === $money_value['id'] && $item['self_name'] === $money_value['self_name'];
                                }, []);

                                if (count($filterArr) == 0) continue;

                                $filterArr = Arr::collapse($filterArr);
                                // 若是多組且不是第一個的節點先暫存，到下一節點再push
                                if (!array_key_exists('money_sort', end($tempSingRoles)) && $filterArr['money_sort'] > 1) {
                                    //  暫存金額
                                    array_push($tempFilterRoles, $filterArr);
                                } else {
                                    //  金額
                                    array_push($tempSingRoles, $filterArr);
                                }
                                unset($filterRoles[array_key_first($filterArr)]);
                                unset($tempSingRoles[$moneyFilterIndex]['money_filters'][$money_key]);
                            }
                        } else if ($role_value['limit_dollar']) {
                            // 輸入金額 > 金額判斷 的節點
                            if ($columnValue['value'] > $role_value['dollar']) {
                                array_push($tempSingRoles, $role_value);
                                // 若有順序不等於1的節點
                                $tempFilterRoles = array_values(Arr::sort($tempFilterRoles, function ($value) {
                                    return $value['money_sort'];
                                }));

                                // 多組按照流程順序排列
                                if (count($tempFilterRoles) > 1 && $tempFilterRoles[0]['money_sort'] - end($tempSingRoles)['money_sort'] == 1) {
                                    foreach ($tempFilterRoles as $key => $value) {
                                        array_push($tempSingRoles, $value);
                                    }
                                    $tempFilterRoles = [];
                                }
                                continue;
                            } else {
                                // 小於金額判斷的節點
                                // 要刪除的節點紀錄資訊
                                array_push(
                                    $tempSingRoles[$moneyFilterIndex]['money_filters'],
                                    ['id' => $role_value['id'], 'self_name' => $role_value['self_name']]
                                );
                                array_push($filterRoles, $role_value);
                            }
                        } else {
                            //  不相干
                            array_push($tempSingRoles, $role_value);
                        }
                    }
                    break;
                } else {
                    // 沒有金額判斷欄位且尚未審核過
                    array_push($tempSingRoles, $role);
                }
            } catch (\Exception $th) {
                LOG::error($key);
                LOG::error($th);
            }
        }
        $num = 0;
        foreach ($tempSingRoles as $key => &$value) {
            $num++;
            $value['id'] = $num;
        }
        return [
            'singRoles' => $tempSingRoles,
            'filterRoles' => array_values($filterRoles)
        ];
    }
    public function allAgree(Request $request)
    {
        if (!$request->has('ids') || count($request->get('ids')) <= 0) {
            return 0;
        }
        foreach ($request->get('ids') as $id) {
            $result = [];
            $request->request->add(['id' => $id]);
            $request->request->add(['remark' => '']);
            $request->request->add(['status' => 2]);
            array_push($result, $this->submit($request));
        }
        if (in_array(0, $result))
            return 0;
        return 1;
    }
    //退回
    public function fallback(Request $request)
    {
        $user = intval($this->user_id);
        if (!$request->has('id', 'status', 'role_id')) {
            return 0;
        }
        try {
            $demand = Demand::where('id', $request->get('id'))->first();
            $singRoles = $demand->payload['sign_roles'];
            $columns =  $demand->payload['forms'][0]['columns'];
            $Notify = new NotificationController;
            $layoutName = $demand->payload['layout_name'];
            //該節點
            // $thisUserRank = 0;
            $thisUserIndex = 0;
            $singRoleId = null;
            $backForCreated =  $request->input('role_id') == 0 ? true : false;
            $codeStatus = Code::where('code_kind', 'AI')
                ->when(
                    $backForCreated,
                    function ($query) {
                        return $query->firstwhere('code_id', 'backForCreated');
                    },
                    function ($query) {
                        return $query->firstwhere('code_id', 'fallback');
                    }
                );
            $statusName = $codeStatus ? $codeStatus->nm_zh_tw : '';

            //撈出是否為代理人
            $representIds = Employee::withTrashed()->with('represents:id,payload')->find($user)->represents()->withTrashed()->pluck('id');

            //變更當前節點資訊
            foreach ($singRoles as $key => $role) {
                if (($role['role_id'] == $user || $representIds->contains($role['role_id'])) && $role['apply_status'] == 1) {
                    $singRoleId = $role['id'];
                    $singRoles[$key]['role_id'] = $user;
                    $singRoles[$key]['isRepresent'] = $representIds->contains($role['role_id']) ? true : false;
                    $singRoles[$key]['apply_status'] = $backForCreated ? 7 : 6;
                    $singRoles[$key]['remark'] = $request->has('remark') ? $request->get('remark') : '';
                    $singRoles[$key]['timestamp'] = $this->Submit->signTimestamp(Carbon::now(), $this->codeTable, $statusName);
                    $singRoles[$key]['raw_time'] = Carbon::now();
                    $thisUserIndex = $key;
                    break;
                }
            }
            //查找後續流程
            $afterRoles = array_values(collect($demand->payload['sign_roles'])->where('id', '>=', $request->get('role_id'))->toArray());
            // 若先前有退回，先濾掉先前產生的節點，避免重複
            $afterRoles = array_filter($afterRoles, function ($role) {
                return !isset($role['isBack']);
            });
            if ($backForCreated) {
                //退回到申請人時，只抓原節點人跟會簽人
                // dd($columns);
                $afterRoles = array_values(
                    array_filter($afterRoles, function ($role) use ($columns) {
                        // 金額判斷
                        if (isset($role['limit_dollar']) && $role['limit_dollar'] && $columns[$role['dollar_col_id'] - 1]['value'] < $role['dollar']) return false;
                        return array_key_exists("raw_sign_off", $role) || (array_key_exists("to_counter", $role) &&  $role['to_counter'] == 1);
                    })
                );
            }
            //將退回節點區間印戳記
            $tempRoles = [];
            foreach ($afterRoles as $key => &$role) {
                if ($singRoleId >= $role['id'] && $request->get('role_id') <= $singRoleId) {

                    if (array_key_exists("raw_sign_off", $role))  unset($role['raw_sign_off']);

                    $role['apply_status'] = 0;
                    $role['remark'] = "";
                    $role['isBack'] = 1;
                    unset($role['raw_time']);
                    unset($role['timestamp']);
                }
                // 金額判斷
                if (isset($role['limit_dollar']) && $role['limit_dollar'] && $columns[$role['dollar_col_id'] - 1]['value'] < $role['dollar'])
                    continue;
                array_push($tempRoles, $role);
            }
            $afterRoles = $tempRoles;
            unset($tempRoles);

            if ($backForCreated) {
                $demand->setAttribute('payload->status', 7);
                $Notify->createNotify($demand->created_by, $layoutName, 4);
            } else {
                $afterRoles[0]['apply_status'] = 1;
                $Notify->createNotify($singRoles[($request->role_id) - 1]['role_id'], $layoutName, 1);
            }

            array_splice($singRoles, $thisUserIndex + 1, Count($singRoles) - ($thisUserIndex + 1), $afterRoles);
            foreach ($singRoles as $index => $singRole) {
                $singRoles[$index]['id'] = $index + 1;
            };
            $demand->setAttribute('payload->sign_roles', $singRoles);
            $demand->save();
            return 1;
        } catch (Exception $e) {
            Log::error($request->all());
            Log::error($e);
            return 0;
        } finally {
            RefreshMaterializedViewDemandQuery::handle();
        }
    }
    //會簽出去
    public function countersigned(Request $request)
    {
        $user = intval($this->user_id);
        if (!$request->has('id', 'counterRemark', 'counterRoles')) {
            return 0;
        }
        try {
            $demand = Demand::with(['employee.orgs'])->where('id', $request->get('id'))->first();
            $singRoles = $demand->payload['sign_roles'];
            //該節點
            // $thisUserRank = 0;
            $thisUserSett = [];
            $thisUserIndex = 0;
            $statusName = Code::where('code_kind', 'AI')->where('code_id', 'counter')->first();
            $statusName = $statusName ? $statusName->nm_zh_tw : '';
            //撈出是否為代理人
            $representIds = Employee::withTrashed()->with('represents:id,payload')->find($user)->represents()->withTrashed()->pluck('id');
            foreach ($singRoles as $key => $role) {
                if (($role['role_id'] == $user || $representIds->contains($role['role_id'])) && $role['apply_status'] == 1) {
                    $singRoles[$key]['role_id'] = $user;
                    $singRoles[$key]['isRepresent'] = $representIds->contains($role['role_id']) ? true : false;
                    $singRoles[$key]['apply_status'] = 5;
                    $singRoles[$key]['remark'] = $request->get('counterRemark');
                    $singRoles[$key]['timestamp'] = $this->Submit->signTimestamp(Carbon::now(), $this->codeTable, $statusName);
                    $singRoles[$key]['raw_time'] = Carbon::now();
                    $thisUserSett = [
                        'child' => $role['child'],
                        'type' => $role['type'],
                        'document' => $role['document'],
                        'self_name' => $role['self_name'],
                        'fill_column_ids' => $role['fill_column_ids'] ?? [],
                        'countersigned' => $role['countersigned'],
                        'role_id' => intval($this->user_id),
                        'remark' => '',
                        'timestamp' => '',
                        'apply_status' => 0,
                        'to_counter' => 2
                    ];
                    $thisUserIndex = $key;
                    break;
                }
            }
            //加入會簽流程
            $counterRoles = $request->get('counterRoles');
            foreach ($counterRoles as $index => $counterRole) {
                $counterRoles[$index]['apply_status'] = 1;
            }
            $demand->setAttribute('payload->counterRoleIds', Arr::pluck($counterRoles, 'role_id'));
            array_push($counterRoles, $thisUserSett);
            //插入會簽節點
            array_splice($singRoles, $thisUserIndex + 1, 0, $counterRoles);
            //重新排序ID
            foreach ($singRoles as $index => $singRole) {
                $singRoles[$index]['id'] = $index + 1;
            };
            $demand->setAttribute('payload->sign_roles', $singRoles);
            $demand->save();
            return 1;
        } catch (Exception $e) {
            Log::error($request->all());
            Log::error($e);
            return 0;
        } finally {
            RefreshMaterializedViewDemandQuery::handle();
        }
    }

    //審核紀錄
    public function fetchSignHistoryLists(Request $request, SigningService $signingService)
    {
        if (!$request->get('start') || !$request->get('end')) {
            return [];
        }

        $pageDemands = $signingService->fetchSignHistoryLists($request);

        return  $pageDemands;
    }


    //撈Demand未結案的表
    public function fetchDemandNotFinshedLog($review, $roleParamas, $request)
    {
        $key_word = $request->get('key_word');
        $demands = Demand::with([
            // 'layout'=> function ($q) {
            //      $q->withTrashed()->select('id','name','payload');
            // },
            'customList',
            'employee' => function ($q) {
                return $q->withTrashed();
            },
            'employee.orgs'
        ])
            ->where('payload->company_id', $this->company_id)
            ->when(
                $key_word,
                function ($q) use ($key_word) {
                    return  $q->where(function ($q) use ($key_word) {
                        $q->where('payload', 'like', '%' . $key_word . '%')
                            ->orwhere('no', 'like', $key_word . '%');
                    });
                }
            );
        if ($request->get('layout_id')) {
            $demands = $demands->where('payload->layout_original_id', $request->get('layout_id'));
        }

        if ($review == 6) {
            $demands->where(function ($demand) use ($roleParamas) {
                $demand->whereJsonContains('payload->sign_roles', [$roleParamas['role6']])
                    ->orWhereJsonContains('payload->sign_roles', [$roleParamas['proxy6']])
                    ->orWhereJsonContains('payload->sign_roles', [$roleParamas['role7']])
                    ->orWhereJsonContains('payload->sign_roles', [$roleParamas['proxy7']]);
            });
        } elseif ($review != -1) {
            $demands->where(function ($demand) use ($roleParamas, $review) {
                $demand->whereJsonContains('payload->sign_roles', [$roleParamas['role' . $review]])
                    ->orWhereJsonContains('payload->sign_roles', [$roleParamas['proxy' . $review]]);
            });
        } else {
            $demands->where(function ($demand) use ($roleParamas) {
                foreach ($roleParamas as $roleParama) {
                    $demand->orwhereJsonContains('payload->sign_roles', [$roleParama]);
                }
            });
        }
        $demands = $demands->get();
        $statusName = Code::where('code_kind', 'AD')->where('code_id', 'open')->first();
        $statusName = $statusName ? $statusName->nm_zh_tw : '';

        return $demands->each(function ($demand) use ($statusName) {
            $demand->type = $statusName;
        });
    }
    //撈DemandLog結案的表
    public function fetchDemandFinshedLog($review, $roleParamas, $request)
    {
        $key_word = $request->get('key_word');
        $demands = DemandLog::with([
            // 'layout'=> function ($q) {
            //     $q->withTrashed()->select('id','name','payload');
            // },
            'customList',
            'employee' => function ($q) {
                return $q->withTrashed();
            },
            'employee.orgs'
        ])
            ->where('payload->company_id', $this->company_id)
            ->when(
                $key_word,
                function ($q) use ($key_word) {
                    return  $q->where(function ($q) use ($key_word) {
                        $q->where('payload', 'like', '%' . $key_word . '%')
                            ->orwhere('no', 'like', $key_word . '%');
                    });
                }
            );;


        if ($request->get('layout_id')) {
            $demands = $demands->where('payload->layout_original_id', $request->get('layout_id'));
        }
        if ($review == 6) {
            $demands->where(function ($demand) use ($roleParamas) {
                $demand->whereJsonContains('payload->sign_roles', [$roleParamas['role6']])
                    ->orWhereJsonContains('payload->sign_roles', [$roleParamas['proxy6']])
                    ->orWhereJsonContains('payload->sign_roles', [$roleParamas['role7']])
                    ->orWhereJsonContains('payload->sign_roles', [$roleParamas['proxy7']]);
            });
        } elseif ($review != -1) {
            $demands->where(function ($demand) use ($roleParamas, $review) {
                $demand->whereJsonContains('payload->sign_roles', [$roleParamas['role' . $review]])
                    ->orWhereJsonContains('payload->sign_roles', [$roleParamas['proxy' . $review]]);
            });
        } else {
            $demands->where(function ($demand) use ($roleParamas) {
                foreach ($roleParamas as $roleParama) {
                    $demand->orwhereJsonContains('payload->sign_roles', [$roleParama]);
                }
            });
        }

        $demands = $demands->get();
        $statusName = Code::where('code_kind', 'AD')->where('code_id', 'closed')->first();
        $statusName = $statusName ? $statusName->nm_zh_tw : '';
        return $demands->each(function ($demand) use ($statusName) {
            $demand->type = $statusName;
        });
    }
}
