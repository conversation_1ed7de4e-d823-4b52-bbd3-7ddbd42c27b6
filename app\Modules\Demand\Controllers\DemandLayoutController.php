<?php

namespace App\Modules\Demand\Controllers;


use App\Modules\Demand\Models\DemandCache;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Session;
use App\Http\Controllers\Controller;
use App\Modules\Demand\Models\Code;
use App\Modules\Demand\Models\DataAuth;
use App\Modules\Demand\Models\Database;
use App\Modules\Demand\Models\Demand;
use App\Modules\Demand\Models\DemandLayout;
use App\Modules\Demand\Models\DemandLog;
use App\Modules\Demand\Models\ListLayout;
use App\Modules\Demand\Models\Employee;
use App\Modules\Demand\Services\ImportDemandService;
use App\Modules\Demand\Services\ListLayoutService;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;

class DemandLayoutController extends Controller
{
    protected $company_id;
    protected $user_id;
    protected $ListLayoutService;
    protected $ImportDemandService;

    public function __construct(
        ListLayoutService $ListLayoutService,
        ImportDemandService $ImportDemandService
    )
    {
        $this->company_id = Session::get('CompanyId');
        $this->user_id = Session::get('employee_id');
        $this->ListLayoutService = $ListLayoutService;
        $this->ImportDemandService = $ImportDemandService;
    }

    public function changeLayoutSwitch(Request $request)
    {
        if ($request->get('id') && $request->has('open')) {
            $layout = DemandLayout::where('id', $request->get('id'))->first();
            $layout->setAttribute('open', $request->get('open'));
            $layout->save();
            return 1;
        }
        return 0;
    }

    public function fetchOriginalLayout(Request $request)
    {
        if (!$request->input('layout_id')) return 0;
        $request->merge(["id" => $request->input('layout_id')]);
        return $this->fetchLayoutSetting($request, true);
    }

    public function fetchLayoutSetting(Request $request, $withTrashed = false)
    {
        if (is_null($layoutId = $request->get('id', null))) {
            return 0;
        }

        try {
            $layout = DemandLayout::query()
                ->when($withTrashed, function ($q) {
                    return $q->withTrashed();
                })
                ->with('lists:demand_layout_id,payload->columns as columns,list_layouts.id,payload->column_id as column_id')
                ->where('payload->original_id', $layoutId)
                ->orWhere('id', '=', $layoutId)
                ->orderBy('id', 'desc')
                ->first([
                    'id',
                    'name',
                    'open',
                    'sign_role',
                    'payload->columns as columns',
                    'payload->flow_setting_type as flow_setting_type',
                    'payload->sort as sort',
                    'payload->import_from_others as import',
                    'payload->remarks as remarks',
                    'payload->templete as file',
                    'payload->original_id as original_id',
                    'payload->version as version',
                    'payload->summaryColId as summaryColId',
                    'sign_role',
                    'can_multi',
                    'auto_finished',
                    'payload->setting_code as setting_code',
                    'priv_id',
                    'deleted_at',
                ]);

            $layout->columns = json_decode($layout->columns);
            $layout->flow_setting_type = isset($layout['flow_setting_type']) ? (int)$layout->flow_setting_type : 0;
            $layout->import = json_decode($layout->import);
            $layout->sort = isset($layout['sort']) ? intval($layout->sort) : null;
            $layout->remarks = isset($layout['remarks']) ? json_decode($layout['remarks']) : [];
            $layout->file = isset($layout['file']) ? json_decode($layout['file']) : null;
            $layout->original_id = (int)$layout->original_id;
            $layout->version = (int)$layout->version;
            $layout->summaryColId = $layout->summaryColId;
            // $layout->sign_role = $layout->sign_role->map(function ($item, $key) {
            //     return [
            //         'id'=>$item['id'],
            //         'role_id'=>isset($item['role_id'])?$item['role_id']:null,
            //         'self_applicant'=>$item['self_applicant'],
            //     ];
            // });
            $columns = $layout->columns;
            $listRows = $layout->lists->each(function ($list) {
                $list->columns = json_decode($list['columns']);
                $list->column_id = json_decode($list['column_id']);
            });

            $columns = collect($columns)->each(function ($column) use ($listRows) {

                if ($column->type == 'list') {
                    $row = collect($listRows)->where('column_id', $column->id)->first();
                    if ($row) {
                        $column->list = $row->columns;
                        $column->list_id = $row->id;
                    }
                }
                //
                if ($column->type == 'database') {
                    $dbData = Database::where('id', $column->selected->db_id)->first();
                    if ($dbData) {
                        $dbColumn = collect($dbData->payload->get('columns'))->where('id', $column->selected->id)->first();
                        $column->name = $dbColumn['name'];
                        $column->selected->data = $dbColumn['data'];
                        $column->selected->fname = $dbColumn['name'];
                    }
                }
            });
            unset($layout->lists);
            return $layout;
        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $th) {
            Log::error(__CLASS__ . '@' . __LINE__ . ' 找不到資料: ' . $th->getMessage());
            return response('找不到資料', 404);
        } catch (\Throwable $th) {
            Log::error(__CLASS__ . '@' . __LINE__ . ' 異常: ' . $th->getMessage());
            return response($th->getMessage(), 400);
        }
    }

    //欄位的層級下拉
    public function fetchColumnTypeCascadeSelect()
    {
        $databases = Database::where(
            'payload->company_id',
            $this->company_id
        )->select('payload->columns as columns', 'id', 'name')->get();
        $databases->each(function ($db) use (&$selects) {
            $columns = collect(json_decode($db['columns']));
            $columns->each(function ($column, $key) use (&$db) {
                $column->key = $key;
                $column->type = 'database';
                $column->db_id = $db['id'];
                $column->fname = $column->name;
                $column->data = $column->data;
                unset($column->name);
                // unset($column->data);
            });
            $db->forms = $columns;
            unset($db['columns']);
        });

        $codes = Code::where('code_kind', 'AB')
            ->orderBy('sort_order', 'asc')
            ->select('nm_zh_tw as fname', 'code_id as type', 'sort_order as id')
            ->get();

        $codes->each(function ($code) use ($databases) {
            if ($code->type == 'database') {
                $code->states = $databases;
                $code->name = $code->fname;
            };
        });
        return $codes;
    }
    public function validateSettingCode(Request $request)
    {
        $dl = DemandLayout::where('payload->setting_code', $request->get('code'))
            // ->whereHas('group', function ($q) {
            // $q->where('company_id', $this->company_id);
            // })
            ->first();
        if ($dl) {
            return [0, '需求單編號已重複'];
        }
        return [1];
    }
    public function createLayout(Request $request)
    {
        if ($request->get('group_id')) {
            //判斷需求單編號是否重複
            $dl = DemandLayout::where('payload->setting_code', $request->get('settingCode'))->whereHas('group', function ($q) {
                $q->where('company_id', $this->company_id);
            })->first();
            if ($dl) {
                return 0;
            }
            $columns = $request->get('columns');

            foreach ($columns as $key => $col) {
                //日期欄位需保留預設是否為當天的日期或多天以後的日期value 和 有無預設
                if ($col['type'] !== 'date' && !(isset($col['preset']) && $col['preset'] == 1)) {
                    unset($columns[$key]['value']);
                };
            }
            $payload = (object)[
                // 因為加了退回到申請人的功能，所以加上版本，方便前端抓取原始欄位設定，不然下拉會沒有資料
                'version' => 0,
                'columns' => $columns,
                'remarks' => $request->get('remarks'),
                'sort' => $request->get('sort'),
                'setting_code' => $request->get('settingCode'),
                'import_from_others' => $request->get('import_from_others'),
                'summaryColId' => $request->input('summaryColId'),
            ];
            $LayoutModel = DemandLayout::create(
                [
                    'group_id' => $request->get('group_id'),
                    'name' => $request->get('name'),
                    'open' => $request->get('open'),
                    'payload' => $payload,
                    'sign_role' => null,
                    'can_multi' => $request->get('can_multi'),
                    'auto_finished' => $request->get('auto_finished'),
                    'created_by' => $this->user_id,
                    'updated_by' => $this->user_id
                ]
            );
            // 紀錄id ，檔案操作使用
            $LayoutModel->setAttribute('payload->original_id', $LayoutModel->id);

            //新增資料權限
            DataAuth::create([
                'layout_id' => $LayoutModel->id,
                'payload' => (object)[
                    'user_list' => [(int)$this->user_id]
                ],
                'metadata' => (object)[
                    'created_by' => (int)$this->user_id
                ],
            ]);
            //搬檔案
            $Upload = new UploadController;
            $file = $request->get('file');
            $remarks = $request->get('remarks');

            $ownFile = (bool) $file;
            foreach ($remarks as $index => $remark) {
                if (isset($remark['file'])) {
                    $ownFile = true;
                    break;
                }
            }

            if ($ownFile) {
                // 搬匯出檔案
                if (!empty($file)) {
                    foreach ($file as $key => &$value) {
                        $lastPath =  $lastPath = '/demand/upload/demandLayout/' . $LayoutModel->id . '/' . $value['base_name'];
                        $uploadResult = $Upload->moveFiles($lastPath, $value['base_name']);
                        if ($uploadResult) {
                            $value['URL'] = '/storage' . $lastPath;
                        }
                    }
                }
                $LayoutModel->setAttribute('payload->templete', $file)->save();

                // 搬備註檔案
                foreach ($remarks as &$remark) {
                    if (!isset($remark['file'])) {continue;}

                    $lastPath = '/demand/upload/demandLayout/' . $LayoutModel->id . '/' . $remark['file']['base_name'];
                    $uploadResult = $Upload->moveFiles($lastPath, $remark['file']['base_name']);
                    if ($uploadResult) {
                        $remark['file']['URL'] = '/storage' . $lastPath;
                    }
                }
                $LayoutModel->setAttribute('payload->remarks', $remarks)->save();
            }
           
            return $LayoutModel;
        }
        return 0;
    }

    public function updateLayout(Request $request)
    {

        if (!$request->get('id'))  return $this->createLayout($request);

        $layout = DemandLayout::find($request->get('id'));
        if (!$layout) return 0;

        if ($request->get('roles')) {
            // 更新流程
            $layout->setAttribute('sign_role', $request->get('roles'));
            $layout->setAttribute('payload->flow_setting_type', $request->get('flow_setting_type'));
            $layout->save();
            return $layout;
        } else {
            try {
                DB::beginTransaction();
                // 欄位資訊
                $columns = $request->get('columns');

                $listIds = [];
                foreach ($columns as $key => $col) {
                    //日期欄位需保留預設是否為當天的日期或多天以後的日期value 和 有無預設
                    if ($col['type'] !== 'date' && !(isset($col['preset']) && $col['preset'] == 1)) {
                        unset($columns[$key]['value']);
                    };

                    if (isset($col['list_id'])) {
                        array_push($listIds, $col['list_id']);
                    };
                }

                $existListIds = collect($layout->payload['columns'])->whereNotIn('list_id', $listIds)->pluck('list_id');
                foreach ($existListIds as $listId) {
                    $ListModel = ListLayout::find($listId);
                    if ($ListModel) {
                        $ListModel->delete();
                    };
                };
                $originalId = $layout->payload['original_id'];
                $payload = (object)[
                    'original_id' => $originalId,
                    'version' => $layout->payload['version'] + 1,
                    'group_id' => $layout->group_id,
                    'columns' => $columns,
                    'remarks' => $request->get('remarks'),
                    'sort' => $request->get('sort'),
                    'setting_code' => $request->get('settingCode'),
                    'import_from_others' => $request->get('import_from_others'),
                    'summaryColId' => $request->input('summaryColId'),
                    // 專案設定用，例如:財務ACC
                    'unique_key' =>  $layout->payload->get('unique_key') ?? ''
                ];
                $LayoutModel = DemandLayout::create(
                    [
                        'group_id' => $layout->group_id,
                        'name' => $request->get('name'),
                        'open' => $request->get('open'),
                        'payload' => $payload,
                        'sign_role' => $layout->sign_role,
                        'can_multi' => $request->get('can_multi'),
                        'auto_finished' => $request->get('auto_finished'),
                        'created_by' => $layout->created_by,
                        'updated_by' => $this->user_id
                    ]
                );
                // 修正權限
                DataAuth::where('layout_id', $layout->id)->update(['layout_id' => $LayoutModel->id]);

                // 搬檔案
                $Upload = new UploadController;
                $file = $request->get('file');
                $remarks = $request->get('remarks');
                $currentFiles = []; 

                $ownFile = (bool) $file;
                foreach ($remarks as $index => $remark) {
                    if (isset($remark['file'])) {
                        $ownFile = true;
                        break;
                    }
                }

                if ($ownFile) {
                    // 搬匯出檔案
                    if(!empty($file)) {
                        foreach ($file as $key => &$value) {
                            if (empty($value)) continue;
                            $currentFiles[] = $value;
                            $lastPath = '/demand/upload/demandLayout/' . $originalId . '/' . $value['base_name'];
                            $uploadResult = $Upload->moveFiles($lastPath, $value['base_name']);
                            if ($uploadResult) {
                                $value['URL'] = '/storage' . $lastPath;
                            }
                         
                        }
                        $LayoutModel->setAttribute('payload->templete', $file);
                    }

                    // 搬備註檔案
                    foreach ($remarks as &$remark) {
                        if (!isset($remark['file'])) {continue;}
                        $currentFiles[] = $remark['file'];

                        $lastPath = '/demand/upload/demandLayout/' . $originalId . '/' . $remark['file']['base_name'];
                        $uploadResult = $Upload->moveFiles($lastPath, $remark['file']['base_name']);
                        if ($uploadResult) {
                            $remark['file']['URL'] = '/storage' . $lastPath;
                        }
                    }
                    $LayoutModel->setAttribute('payload->remarks', $remarks)->save();
                    
                    $Upload->cleanUpUnusedFiles($originalId, $currentFiles);

                } else {
                    // request 中沒有任何 file 便可移除 directory移除 directory
                    $LayoutModel->setAttribute('payload->templete', $file);
                    Storage::disk('public')->deleteDirectory('/demand/upload/demandLayout/' . $originalId . '/');
                }

                $LayoutModel->save();
                $layout->delete();

                // update list and import
                $newRequest = new Request(array_merge($request->all(), [
                    'layout_id' => $LayoutModel->id
                ]));
                $this->ListLayoutService->updateList($newRequest);
                $this->ImportDemandService->createOrUpdate($newRequest);

                DB::commit();

                $LayoutModel->update(['priv_id' => $layout->id]);
                return $LayoutModel;
            } catch (\Exception $e) {
                DB::rollback();
                Log::error($e);
                return 0;
            }

            // //欄位過濾(不要value) && 交集listTable 沒有就要刪掉list_id
            // $columns = $request->get('columns');

            // $listIds = [];
            // foreach ($columns as $key => $col) {
            //     //日期欄位需保留預設是否為當天的日期或多天以後的日期value 和 有無預設
            //     if ($col['type'] !== 'date' && !(isset($col['preset']) && $col['preset'] == 1)) {
            //         unset($columns[$key]['value']);
            //     };
            //     if (isset($col['list_id'])) {
            //         array_push($listIds, $col['list_id']);
            //     };
            // }

            // $existListIds = collect($layout->payload['columns'])->whereNotIn('list_id', $listIds)->pluck('list_id');
            // foreach ($existListIds as $listId) {
            //     $ListModel = ListLayout::where('id', $listId)->first();
            //     if ($ListModel) {
            //         $ListModel->delete();
            //     };
            // };

            // $layout->setAttribute('name', $request->get('name'))
            //     ->setAttribute('updated_by', $this->user_id)
            //     ->setAttribute('can_multi', $request->get('can_multi'))
            //     ->setAttribute('payload->setting_code', $request->get('settingCode'))
            //     ->setAttribute('payload->columns', $columns)
            //     // ->setAttribute('payload->templete', $request->get('files'))
            //     ->setAttribute('payload->import_from_others', $request->get('import_from_others'))
            //     ->setAttribute('auto_finished', $request->get('auto_finished'))
            //     ->setAttribute('payload->remarks', $request->get('remarks'))
            //     ->setAttribute('payload->sort', $request->get('sort'));

            // //搬檔案
            // $file = $request->get('file');
            // if (!empty($file)) {
            //     $lastPath = '/demand/upload/demandLayout/' . $layout->id . '/' . $file['base_name'];
            //     $Upload = new UploadController;
            //     $uploadResult = $Upload->moveFiles($lastPath, $file['base_name']);
            //     if ($uploadResult) {
            //         $file['URL'] = '/storage' . $lastPath;
            //         // $file['base_name'] = $file['base_name'];
            //         // $file['name'] = $file['name'];
            //         $layout->setAttribute('payload->templete', $file);
            //     }
            // } else {
            //     $layout->forceFill(['payload->templete' => null]);
            //     Storage::disk('public')->deleteDirectory('/demand/upload/demandLayout/' . $layout->id . '/');
            // }
        }
    }

    public function deleteLayout(Request $request)
    {
        $id = $request['id'];
        if ($id) {
            $ds = Demand::where('layout_id', $id)->first();
            $dls = DemandLog::where('layout_id', $id)->first();
            if ($ds || $dls) {
                return [0, '已有使用的需求單'];
            }
            $layout = DemandLayout::where('id', $id)->first();
            $layout->delete();
            DataAuth::where('layout_id', $id)->delete();
            $lists = ListLayout::where('demand_layout_id', $id)->delete();
            // $lists->each(function ($list) {
            //     return $list->delete();
            // });
            return [1];
        }
        return [0, '刪除失敗'];
    }
    public function fetchColumnDropdown(Request $request)
    {
        if (!$request->has('id'))
            return [];
        $id = $request->get('id');
        $dl = DemandLayout::find($id);
        $columns = collect($dl->payload->get('columns'));
        return $columns->filter(function ($column, $key) {
            return $column['type'] > 2;
        });
    }
    public function fetchManagerRankDropdown()
    {
        $rankRange = Employee::where('company_id', $this->company_id)
            ->whereNotNull('payload->rank')
            ->get();
        $rankRange = $rankRange->pluck('payload.rank')->sort()->unique()->values();

        $codeTable = Code::where('code_kind', 'AJ')->get();
        $rankName = $codeTable->where('code_id', 'rank')->first() ?
            $codeTable->where('code_id', 'rank')->first()->nm_zh_tw : '';
        $managerName = $codeTable->where('code_id', 'manager')->first() ?
            $codeTable->where('code_id', 'manager')->first()->nm_zh_tw : '';
        foreach ($rankRange as $key => $rank) {
            $rankRange[$key] = [
                'name' => $rankName . $rank . $managerName,
                'id' => $rank
            ];
        }
        return $rankRange;
    }

    public function getDemandCache(int $originalLayoutId)
    {
        $data = DemandCache::query()
            ->where('original_layout_id', '=', $originalLayoutId)
            ->where('employee_id', '=', $this->user_id)
            ->where('company_id', '=', $this->company_id)
            ->first();

        if (empty($data)) {
            return response([], 200);
        }
        return response($data->payload, 200);
    }

    public function putDemandCache(int $original_layout_id, Request $request)
    {
        try {
            $rawData = $request->get('data');
            DemandLayout::FindOrFail($rawData['layout_id']);

            if (empty($rawData)) {
                return response(null, 204);
            }

            DemandCache::query()
                ->updateOrCreate([
                    'original_layout_id' => $original_layout_id,
                    'layout_id' => $rawData['layout_id'],
                    'employee_id' => $this->user_id,
                    'company_id' => $this->company_id,
                ], [
                    'payload' => $rawData,
                ]);

            return response(null, 204);
        } catch (\Throwable $th) {
            Log::error("存快取有問題: {$th->getMessage()}");
            throw $th;
        }
    }

    public function delDemandCache(int $originalLayoutId, Request $request)
    {
        try {
            DemandCache::where([
                'original_layout_id' => $originalLayoutId,
                'employee_id' => $this->user_id,
                'company_id' => $this->company_id,
            ])
                ->delete();

            return response(null, 204);
        } catch (\Throwable $th) {
            Log::error("刪快取有問題: {$th->getMessage()}");
            throw $th;
        }
    }

    // 後台設定撈取該單欄位資料
    public function fetchColumnData(Request $request)
    {
        // 撈出該單有使用的資料庫
        $demand_list = DemandLayout::find($request->listId)->payload['columns'];
        $db_list = [];
        foreach ($demand_list as $column) {
            if (isset($column['selected']['db_id'])) {
                array_push($db_list, $column['selected']['db_id']);
            }
        }
        $db_list  = array_unique($db_list);

        // 抓出資料庫的資料
        $demand_setting = collect();
        $data_list = Database::find($db_list);
        $data_list->each(function ($dataBase, $db_index) use (&$demand_setting) {
            // 有幾個DB
            $demand_setting->push(collect());
            // 每個DB的名稱
            $demand_setting[$db_index]->put('db_id', $dataBase['id']);
            $demand_setting[$db_index]->put('db_name', $dataBase['name']);
            $demand_setting[$db_index]->put('db_columns', collect());

            // DB內有幾個欄位
            $db_columns = collect($dataBase->payload['columns']);
            $db_columns->each(function ($db_column) use (&$demand_setting, $db_index) {
                $demand_setting[$db_index]['db_columns']
                    ->push(['column_id' => $db_column['id'], 'column_name' => $db_column['name'], 'db_id' =>  $demand_setting[$db_index]['db_id']]);
            });
        });


        return $demand_setting;
    }

    // 自動填入審核人

    public function fetchDBColumnData(Request $request)
    {
        $dbData = Database::where('id', $request->db_id)->firstOrFail();
        if ($dbData) {
            $dbColumn = collect($dbData->payload->get('columns'))
                ->where('id',  $request->column_id)
                ->first();

            if ($dbColumn) {
                return  $dbColumn['data'];
            }
        }
    }
}
