<?php

namespace App\Modules\par\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Modules\Demand\Models\FuncAuth;
use Illuminate\Support\Facades\Session;

class ReserveSettingController extends Controller
{
    protected $company_id;
    protected $user_id;
    public function __construct()
    {
        $this->company_id = Session::get('CompanyId');
        $this->user_id = Session::get('employee_id');
    }

    public function list()
    {
        $funcAuth = FuncAuth::with('parUsers:id,payload->name as name')
            ->where('company_id', $this->company_id)
            ->where('type', 'par')
            ->whereNotNull('payload->par_list')
            ->first();

        return [
            [
                'user_list' => $funcAuth->payload['par_list'],
                'users' => $funcAuth->parUsers,
            ]
        ];
    }
    public function updateAndCreate(Request $request)
    {
        $parList = $request->get('users');
        $funcAuth = FuncAuth::where('company_id', $this->company_id)
            ->where('type', 'par')
            ->first();


        $funcAuth
            ->forceFill(['payload->par_list' => $parList])
            ->save();

        // $payload = [
        //     'user_list' => $funcAuth->payload["user_list"] ?? [],
        //     'par_list' => $parList,
        // ];

        // FuncAuth::updateOrCreate(
        //     ['company_id' => intval($this->company_id)],
        //     ['payload' => $payload]
        // );

        return 1;
    }
}
