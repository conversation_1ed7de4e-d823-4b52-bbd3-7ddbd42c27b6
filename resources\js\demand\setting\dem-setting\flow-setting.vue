<template>
    <div>
        <!-- 流程列表 -->
        <div v-if="action == 0">
            <div v-for="(item, item_index) in dataList" :key="item_index">
                <div class="demandList">
                    <div class="flex">
                        <p class="mr-4 my-auto font-bold">
                            {{ item.name }}
                        </p>
                    </div>
                </div>

                <div class="my-10 flex flex-wrap">
                    <div
                        v-for="(itemList, itemList_index) in item.layouts"
                        :key="itemList_index"
                        class="flex mr-10 pb-10"
                    >
                        <Button
                            :title="itemList.name"
                            @click="getRoleSetting(item_index, itemList_index)"
                            class=""
                            ><p class="w-36 truncate">
                                {{ itemList.name }}
                            </p></Button
                        >
                    </div>
                </div>
            </div>
        </div>

        <!-- 流程內頁 -->
        <div v-if="action == 1">
            <div class="text-center flex mb-5">
                <div class="flex-1 flex justify-start align-center">
                    <span
                        @click="
                            (action = 0),
                                (sign_roles = []),
                                (todo_roles = []),
                                (listId = 0),
                                ($root.layoutKey = null),
                                ($root.layoutIndex = null),
                                (col_flow.col_tabs = [])
                        "
                        class="cursor-pointer"
                    >
                        <i class="fas fa-arrow-left"></i>
                    </span>
                </div>
                <span class="text-xl font-semibold" style="flex: 2">
                    {{ title }}
                </span>
                <div class="flex-1 text-right">
                    <Button
                        @click="
                            role_type == 0 ? (role_type = 1) : (role_type = 0)
                        "
                        class="p-button-outlined p-button-secondary"
                        :label="
                            role_type == 0 || role_type == null
                                ? '通用流程'
                                : '依選項設定流程'
                        "
                    />
                </div>
            </div>
            <br />
            <!-- ============================= 通用流程 ============================= -->
            <div v-if="role_type == 0 || role_type == null">
                <div class="my-6 flex justify-center demandList">
                    <span class="mr-4 my-auto font-bold text-x2">審核人</span>
                </div>
                <!-- =============================-->
                <draggable v-model="sign_roles">
                    <div
                        v-for="(sign_role, sign_index) in sign_roles"
                        :key="sign_index"
                    >
                        <div class="mb-6 p-6 w-full demandListSelect">
                            <div class="flex flex-wrap justify-between">
                                <InputText
                                    v-model="sign_role.self_name"
                                    class="w-36"
                                ></InputText>
                                <div
                                    :class="
                                        !sign_role.self_applicant
                                            ? ''
                                            : 'text-gray-300'
                                    "
                                >
                                    <RadioButton
                                        class="mb-4"
                                        :value="false"
                                        v-model="sign_role.self_applicant"
                                    />
                                    <Dropdown
                                        :disabled="sign_role.self_applicant"
                                        placeholder="選擇主管職"
                                        :options="managerRanks"
                                        optionLabel="name"
                                        optionValue="id"
                                        data-key="id"
                                        v-model="sign_roles[sign_index].level"
                                        :class="{
                                            'p-invalid':
                                                sign_role.invalid && validated,
                                        }"
                                        @input="
                                            $set(sign_role, 'invalid', false)
                                        "
                                    />
                                </div>
                                <div
                                    class="my-auto"
                                    :class="
                                        sign_role.self_applicant
                                            ? ''
                                            : 'text-gray-300'
                                    "
                                >
                                    <RadioButton
                                        class="mb-1"
                                        :value="true"
                                        v-model="sign_role.self_applicant"
                                    />
                                    <label
                                        class="underline"
                                        :class="
                                            sign_role.self_applicant
                                                ? 'cursor-pointer'
                                                : ''
                                        "
                                        @click="
                                            if (sign_role.self_applicant) {
                                                self_choose_dialog = sign_role;
                                                displayModal4 = true;
                                            }
                                        "
                                        >自行指定人員</label
                                    >
                                    <p class="text-xs ml-4">
                                        {{
                                            sign_role.applicant_choose ?? true
                                                ? "申請人自行選擇"
                                                : "自動帶入人員"
                                        }}
                                    </p>
                                </div>
                                <div>
                                    <Checkbox
                                        v-model="sign_role.must"
                                        class="mb-1 md:mb-4"
                                        :binary="true"
                                    />
                                    <label>審核人填寫</label>
                                    <MultiSelect
                                        class="w-44"
                                        :class="
                                            sign_role.must
                                                ? ''
                                                : 'text-gray-300 important-color pointer-events-none'
                                        "
                                        :options="sign_role.audit_musts"
                                        optionLabel="name"
                                        optionValue="id"
                                        optionDisabled="disabled"
                                        :showToggleAll="false"
                                        @change="selectOpt()"
                                        v-model="sign_role.fill_column_ids"
                                        placeholder="選擇需求單欄位"
                                    />
                                </div>
                                <div>
                                    <MultiSelect
                                        class="w-44"
                                        :class="
                                            sign_role.must
                                                ? ''
                                                : 'text-gray-300 important-color pointer-events-none'
                                        "
                                        :options="sign_role.list_must"
                                        optionLabel="name"
                                        optionValue="id"
                                        optionDisabled="disabled"
                                        :showToggleAll="false"
                                        @change="selectOpt()"
                                        v-model="sign_role.fill_column_list_ids"
                                        placeholder="選擇自訂表單欄位"
                                    />
                                </div>
                                <div class="my-auto">
                                    <Checkbox
                                        v-model="sign_role.countersigned"
                                        class="mb-1"
                                        :binary="true"
                                    />
                                    <label>會簽</label>
                                </div>
                                <div class="my-auto">
                                    <Checkbox
                                        v-model="sign_role.child"
                                        class="mb-1"
                                        :binary="true"
                                    />
                                    <label>申請子單</label>
                                </div>
                                <div class="my-auto">
                                    <Checkbox
                                        v-model="sign_role.document"
                                        class="mb-1"
                                        :binary="true"
                                    />
                                    <label>上傳附件</label>
                                </div>
                                <div class="my-auto">
                                    <Checkbox
                                        v-model="sign_role.limit_dollar"
                                        @change="
                                            limitDollar(0, 0, sign_index, null)
                                        "
                                        class="mb-1"
                                        :binary="true"
                                    />
                                    <label
                                        @click="
                                            role_modal_index = sign_index;
                                            role_modal_type = sign_role.type;
                                            money_col_id =
                                                sign_role.dollar_col_id;
                                            money = sign_role.dollar;
                                            currentModel = sign_role;
                                            displayModal3 = true;
                                        "
                                        class="underline cursor-pointer"
                                        :class="
                                            sign_role.limit_dollar
                                                ? ''
                                                : 'text-gray-300 pointer-events-none'
                                        "
                                        >金額判斷</label
                                    >
                                </div>
                                <button
                                    @click="removeSignPerson(0, 0, sign_index)"
                                    class=""
                                >
                                    <img
                                        src="@images/icon/delete_enable.png"
                                        alt=""
                                    />
                                </button>
                            </div>
                        </div>
                    </div>
                </draggable>

                <!-- =============================-->
                <div
                    @click="addSignPerson(0, 0)"
                    class="w-full h-16 text-current border-2 border-dashed hover:border-indigo-600 hover:bg-funcPurple py-4 text-center rounded-md cursor-pointer"
                >
                    <i class="fa-solid fa-plus"></i>
                    <span class="my-auto inline-block">新增關卡</span>
                </div>

                <div class="my-6 flex justify-center demandList">
                    <span class="mr-4 my-auto font-bold text-x2">經辦人</span>
                </div>
                <!-- =============================-->
                <div v-if="todo_roles">
                    <draggable v-model="todo_roles">
                        <div
                            v-for="(todo_role, todo_index) in todo_roles"
                            :key="todo_index"
                        >
                            <div class="mb-6 p-6 w-full demandListSelect">
                                <div class="flex flex-wrap justify-between">
                                    <InputText
                                        v-model="todo_role.self_name"
                                        class="w-36"
                                    ></InputText>
                                    <div
                                        :class="
                                            !todo_role.self_applicant
                                                ? ''
                                                : 'text-gray-300'
                                        "
                                    >
                                        <RadioButton
                                            class="mb-4"
                                            :value="false"
                                            v-model="todo_role.self_applicant"
                                        />
                                        <Dropdown
                                            placeholder="選擇人員"
                                            :options="getEmployees"
                                            :filter="true"
                                            :disabled="todo_role.self_applicant"
                                            optionLabel="name"
                                            optionValue="id"
                                            data-key="id"
                                            v-model="
                                                todo_roles[todo_index].role_id
                                            "
                                            :class="{
                                                'p-invalid':
                                                    todo_role.invalid &&
                                                    validated,
                                            }"
                                            @input="
                                                $set(
                                                    todo_role,
                                                    'invalid',
                                                    false
                                                )
                                            "
                                            class="w-40"
                                        >
                                            <template #option="slotProps">
                                                <div>
                                                    <span>{{
                                                        slotProps.option
                                                            .org_name
                                                    }}</span>
                                                    <span>{{
                                                        slotProps.option.name
                                                    }}</span>
                                                </div>
                                            </template>
                                        </Dropdown>
                                    </div>
                                    <div
                                        class="my-auto"
                                        :class="
                                            todo_role.self_applicant
                                                ? ''
                                                : 'text-gray-300'
                                        "
                                    >
                                        <RadioButton
                                            class="mb-1"
                                            :value="true"
                                            v-model="todo_role.self_applicant"
                                        />
                                        <label
                                            class="underline"
                                            :class="
                                                todo_role.self_applicant
                                                    ? 'cursor-pointer'
                                                    : ''
                                            "
                                            @click="
                                                if (todo_role.self_applicant) {
                                                    self_choose_dialog =
                                                        todo_role;
                                                    displayModal4 = true;
                                                }
                                            "
                                            >自行指定人員</label
                                        >
                                        <p class="text-xs ml-4">
                                            {{
                                                todo_role.applicant_choose ??
                                                true
                                                    ? "申請人自行選擇"
                                                    : "自動帶入人員"
                                            }}
                                        </p>
                                    </div>
                                    <div>
                                        <Checkbox
                                            v-model="todo_role.must"
                                            class="mb-1 md:mb-4"
                                            :binary="true"
                                        />
                                        <label>審核人填寫</label>
                                        <MultiSelect
                                            class="w-44"
                                            :class="
                                                todo_role.must
                                                    ? ''
                                                    : 'text-gray-300 important-color pointer-events-none'
                                            "
                                            :options="todo_role.audit_musts"
                                            optionLabel="name"
                                            optionValue="id"
                                            optionDisabled="disabled"
                                            :showToggleAll="false"
                                            @change="selectOpt()"
                                            v-model="todo_role.fill_column_ids"
                                            placeholder="選擇填寫欄位"
                                        />
                                    </div>
                                    <div>
                                        <MultiSelect
                                            class="w-44"
                                            :class="
                                                todo_role.must
                                                    ? ''
                                                    : 'text-gray-300 important-color pointer-events-none'
                                            "
                                            :options="todo_role.list_must"
                                            optionLabel="name"
                                            optionValue="id"
                                            optionDisabled="disabled"
                                            :showToggleAll="false"
                                            @change="selectOpt()"
                                            v-model="
                                                todo_role.fill_column_list_ids
                                            "
                                            placeholder="選擇自訂表單欄位"
                                        />
                                    </div>
                                    <div class="my-auto">
                                        <Checkbox
                                            v-model="todo_role.countersigned"
                                            class="mb-1"
                                            :binary="true"
                                        />
                                        <label>會簽</label>
                                    </div>
                                    <div class="my-auto">
                                        <Checkbox
                                            v-model="todo_role.child"
                                            class="mb-1"
                                            :binary="true"
                                        />
                                        <label>申請子單</label>
                                    </div>
                                    <div class="my-auto">
                                        <Checkbox
                                            v-model="todo_role.document"
                                            class="mb-1"
                                            :binary="true"
                                        />
                                        <label>上傳附件</label>
                                    </div>
                                    <div class="my-auto">
                                        <Checkbox
                                            v-model="todo_role.limit_dollar"
                                            @change="
                                                limitDollar(
                                                    0,
                                                    0,
                                                    null,
                                                    todo_index
                                                )
                                            "
                                            class="mb-1"
                                            :binary="true"
                                        />
                                        <label
                                            @click="
                                                role_modal_index = todo_index;
                                                role_modal_type =
                                                    todo_role.type;
                                                money_col_id =
                                                    todo_role.dollar_col_id;
                                                money = todo_role.dollar;
                                                currentModel = todo_role;
                                                displayModal3 = true;
                                            "
                                            class="underline cursor-pointer"
                                            :class="
                                                todo_role.limit_dollar
                                                    ? ''
                                                    : 'text-gray-300 pointer-events-none'
                                            "
                                            >金額判斷</label
                                        >
                                    </div>
                                    <button
                                        @click="
                                            removeTodoPerson(0, 0, todo_index)
                                        "
                                        class=""
                                    >
                                        <img
                                            src="@images/icon/delete_enable.png"
                                            alt=""
                                        />
                                    </button>
                                </div>
                            </div>
                        </div>
                    </draggable>
                    <!-- =============================-->
                    <div
                        @click="addTodoPerson(0, 0)"
                        class="w-full h-16 text-current border-2 border-dashed hover:border-indigo-600 hover:bg-funcPurple py-4 text-center rounded-md cursor-pointer"
                    >
                        <i class="fa-solid fa-plus"></i>
                        <span class="my-auto inline-block">新增關卡</span>
                    </div>
                </div>

                <div
                    class="buttons mt-12 w-full flex justify-between md:justify-end"
                >
                    <Button
                        @click="
                            (action = 0),
                                (sign_roles = []),
                                (todo_roles = []),
                                ($root.layoutKey = null),
                                ($root.layoutIndex = null)
                        "
                        label="取消"
                        class="p-button-outlined p-button-secondary w-28 mr-5"
                    />
                    <Button
                        @click="saveRoleSetting()"
                        label="儲存"
                        class="w-28"
                    />
                </div>
            </div>
            <!-- ============================= 依選項設定流程 ============================= -->
            <div v-else>
                <Button
                    @click="displayModal = true"
                    class="p-button-outlined p-button-secondary mb-5"
                    label="新增流程"
                />
                <div v-if="col_flow.col_tabs.length !== 0" class="w-full">
                    <TabView
                        @tab-change="changeTab($event.index)"
                        :key="forceUpdateTabView"
                    >
                        <TabPanel
                            v-for="(tab, index) in col_flow.col_tabs"
                            :key="index"
                            :header="tab.name"
                        >
                            <div class="flex justify-end">
                                <div>
                                    <span
                                        @click="displayModalEdit(index)"
                                        class="underline cursor-pointer"
                                        >編輯流程</span
                                    >
                                    <span
                                        @click="
                                            displayModal1 = true;
                                            tab_index = index;
                                        "
                                        class="underline cursor-pointer"
                                        >移除流程</span
                                    >
                                </div>
                            </div>
                            <div class="my-6 flex justify-center demandList">
                                <span class="mr-4 my-auto font-bold text-x2"
                                    >審核人</span
                                >
                            </div>
                            <!-- =============================-->
                            <draggable v-model="tab.sign_role_models">
                                <div
                                    v-for="(
                                        sign_model, sign_index
                                    ) in tab.sign_role_models"
                                    :key="'sign' + sign_index"
                                >
                                    <div
                                        class="mb-6 p-6 w-full demandListSelect"
                                    >
                                        <div
                                            class="flex flex-wrap justify-between"
                                        >
                                            <InputText
                                                v-model="sign_model.self_name"
                                                class="w-36"
                                            ></InputText>
                                            <div
                                                :class="
                                                    !sign_model.self_applicant
                                                        ? ''
                                                        : 'text-gray-300'
                                                "
                                            >
                                                <RadioButton
                                                    class="mb-4"
                                                    :value="false"
                                                    v-model="
                                                        sign_model.self_applicant
                                                    "
                                                />
                                                <Dropdown
                                                    placeholder="選擇主管職"
                                                    :options="managerRanks"
                                                    :disabled="
                                                        sign_model.self_applicant
                                                    "
                                                    optionLabel="name"
                                                    optionValue="id"
                                                    data-key="id"
                                                    v-model="sign_model.level"
                                                    :class="{
                                                        'p-invalid':
                                                            sign_model.invalid &&
                                                            validated,
                                                    }"
                                                    @input="
                                                        $set(
                                                            sign_model,
                                                            'invalid',
                                                            false
                                                        )
                                                    "
                                                />
                                            </div>
                                            <div
                                                class="my-auto"
                                                :class="
                                                    sign_model.self_applicant
                                                        ? ''
                                                        : 'text-gray-300'
                                                "
                                            >
                                                <RadioButton
                                                    class="mb-1"
                                                    :value="true"
                                                    v-model="
                                                        sign_model.self_applicant
                                                    "
                                                />
                                                <label
                                                    class="underline"
                                                    :class="
                                                        sign_model.self_applicant
                                                            ? 'cursor-pointer'
                                                            : ''
                                                    "
                                                    @click="
                                                        if (
                                                            sign_model.self_applicant
                                                        ) {
                                                            self_choose_dialog =
                                                                sign_model;
                                                            displayModal4 = true;
                                                        }
                                                    "
                                                    >自行指定人員</label
                                                >
                                                <p class="text-xs ml-4">
                                                    {{
                                                        sign_model.applicant_choose ??
                                                        true
                                                            ? "申請人自行選擇"
                                                            : "自動帶入人員"
                                                    }}
                                                </p>
                                            </div>
                                            <div>
                                                <Checkbox
                                                    v-model="sign_model.must"
                                                    class="mb-1 md:mb-4"
                                                    :binary="true"
                                                />
                                                <label>審核人填寫</label>
                                                <MultiSelect
                                                    class="w-44"
                                                    :class="
                                                        sign_model.must
                                                            ? ''
                                                            : 'text-gray-300 important-color pointer-events-none'
                                                    "
                                                    :options="
                                                        sign_model.audit_musts
                                                    "
                                                    optionLabel="name"
                                                    optionValue="id"
                                                    optionDisabled="disabled"
                                                    :showToggleAll="false"
                                                    @change="
                                                        selectOpt($event.index)
                                                    "
                                                    v-model="
                                                        sign_model.fill_column_ids
                                                    "
                                                    placeholder="選擇填寫欄位"
                                                />
                                            </div>
                                            <div>
                                                <MultiSelect
                                                    class="w-44"
                                                    :class="
                                                        sign_model.must
                                                            ? ''
                                                            : 'text-gray-300 important-color pointer-events-none'
                                                    "
                                                    :options="
                                                        sign_model.list_must
                                                    "
                                                    optionLabel="name"
                                                    optionValue="id"
                                                    optionDisabled="disabled"
                                                    :showToggleAll="false"
                                                    @change="
                                                        selectOpt($event.index)
                                                    "
                                                    v-model="
                                                        sign_model.fill_column_list_ids
                                                    "
                                                    placeholder="選擇自訂表單欄位"
                                                />
                                            </div>
                                            <div class="my-auto">
                                                <Checkbox
                                                    v-model="
                                                        sign_model.countersigned
                                                    "
                                                    class="mb-1"
                                                    :binary="true"
                                                />
                                                <label>會簽</label>
                                            </div>
                                            <div class="my-auto">
                                                <Checkbox
                                                    v-model="sign_model.child"
                                                    class="mb-1"
                                                    :binary="true"
                                                />
                                                <label>申請子單</label>
                                            </div>
                                            <div class="my-auto">
                                                <Checkbox
                                                    v-model="
                                                        sign_model.document
                                                    "
                                                    class="mb-1"
                                                    :binary="true"
                                                />
                                                <label>上傳附件</label>
                                            </div>
                                            <div class="my-auto">
                                                <Checkbox
                                                    v-model="
                                                        sign_model.limit_dollar
                                                    "
                                                    class="mb-1"
                                                    :binary="true"
                                                    @change="
                                                        limitDollar(
                                                            1,
                                                            index,
                                                            sign_index,
                                                            null
                                                        )
                                                    "
                                                />
                                                <label
                                                    @click="
                                                        role_modal_index =
                                                            sign_index;
                                                        role_modal_type =
                                                            sign_model.type;
                                                        tab_index = index;
                                                        money_col_id =
                                                            sign_model.dollar_col_id;
                                                        money =
                                                            sign_model.dollar;
                                                        currentModel =
                                                            sign_model;
                                                        displayModal3 = true;
                                                    "
                                                    class="underline cursor-pointer"
                                                    :class="
                                                        sign_model.limit_dollar
                                                            ? ''
                                                            : 'text-gray-300 pointer-events-none'
                                                    "
                                                    >金額判斷</label
                                                >
                                            </div>
                                            <button
                                                @click="
                                                    removeSignPerson(
                                                        1,
                                                        index,
                                                        sign_index
                                                    )
                                                "
                                                class=""
                                            >
                                                <img
                                                    src="@images/icon/delete_enable.png"
                                                    alt=""
                                                />
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </draggable>
                            <div
                                @click="addSignPerson(1, index)"
                                class="w-full h-16 text-current border-2 border-dashed hover:border-indigo-600 hover:bg-funcPurple py-4 text-center rounded-md cursor-pointer"
                            >
                                <i class="fa-solid fa-plus"></i>
                                <span class="my-auto inline-block"
                                    >新增關卡</span
                                >
                            </div>
                            <div class="my-6 flex justify-center demandList">
                                <span class="mr-4 my-auto font-bold text-x2"
                                    >經辦人</span
                                >
                            </div>
                            <draggable v-model="tab.todo_role_models">
                                <div
                                    v-for="(
                                        todo_model, todo_index
                                    ) in tab.todo_role_models"
                                    :key="'todo' + todo_index"
                                >
                                    <div
                                        class="mb-6 p-6 w-full demandListSelect"
                                    >
                                        <div
                                            class="flex flex-wrap justify-between"
                                        >
                                            <InputText
                                                v-model="todo_model.self_name"
                                                class="w-36"
                                            ></InputText>
                                            <div
                                                :class="
                                                    !todo_model.self_applicant
                                                        ? ''
                                                        : 'text-gray-300'
                                                "
                                            >
                                                <RadioButton
                                                    class="mb-4"
                                                    :value="false"
                                                    v-model="
                                                        todo_model.self_applicant
                                                    "
                                                />
                                                <Dropdown
                                                    placeholder="選擇人員"
                                                    :options="getEmployees"
                                                    :disabled="
                                                        todo_model.self_applicant
                                                    "
                                                    :filter="true"
                                                    optionLabel="name"
                                                    optionValue="id"
                                                    data-key="id"
                                                    v-model="todo_model.role_id"
                                                    :class="{
                                                        'p-invalid':
                                                            todo_model.invalid &&
                                                            validated,
                                                    }"
                                                    @input="
                                                        $set(
                                                            todo_model,
                                                            'invalid',
                                                            false
                                                        )
                                                    "
                                                    class="w-40"
                                                >
                                                    <template
                                                        #option="slotProps"
                                                    >
                                                        <div>
                                                            <span>{{
                                                                slotProps.option
                                                                    .org_name
                                                            }}</span>
                                                            <span>{{
                                                                slotProps.option
                                                                    .name
                                                            }}</span>
                                                        </div>
                                                    </template>
                                                </Dropdown>
                                            </div>
                                            <div
                                                class="my-auto flex flex-col"
                                                :class="
                                                    todo_model.self_applicant
                                                        ? ''
                                                        : 'text-gray-300'
                                                "
                                            >
                                                <div>
                                                    <RadioButton
                                                        class="mb-1"
                                                        :value="true"
                                                        v-model="
                                                            todo_model.self_applicant
                                                        "
                                                    />
                                                    <label
                                                        class="underline"
                                                        :class="
                                                            todo_model.self_applicant
                                                                ? 'cursor-pointer'
                                                                : ''
                                                        "
                                                        @click="
                                                            if (
                                                                todo_model.self_applicant
                                                            ) {
                                                                self_choose_dialog =
                                                                    todo_model;
                                                                displayModal4 = true;
                                                            }
                                                        "
                                                        >自行指定人員</label
                                                    >
                                                </div>
                                                <p class="text-xs ml-4">
                                                    {{
                                                        todo_model.applicant_choose ??
                                                        true
                                                            ? "申請人自行選擇"
                                                            : "自動帶入人員"
                                                    }}
                                                </p>
                                            </div>
                                            <div>
                                                <Checkbox
                                                    v-model="todo_model.must"
                                                    class="mb-1 md:mb-4"
                                                    :binary="true"
                                                />
                                                <label>審核人填寫</label>
                                                <MultiSelect
                                                    class="w-44"
                                                    :class="
                                                        todo_model.must
                                                            ? ''
                                                            : 'text-gray-300 important-color pointer-events-none'
                                                    "
                                                    :options="
                                                        todo_model.audit_musts
                                                    "
                                                    optionLabel="name"
                                                    optionValue="id"
                                                    optionDisabled="disabled"
                                                    :showToggleAll="false"
                                                    @change="
                                                        selectOpt($event.index)
                                                    "
                                                    v-model="
                                                        todo_model.fill_column_ids
                                                    "
                                                    placeholder="選擇填寫欄位"
                                                />
                                            </div>
                                            <div>
                                                <MultiSelect
                                                    class="w-44"
                                                    :class="
                                                        todo_model.must
                                                            ? ''
                                                            : 'text-gray-300 important-color pointer-events-none'
                                                    "
                                                    :options="
                                                        todo_model.list_must
                                                    "
                                                    optionLabel="name"
                                                    optionValue="id"
                                                    optionDisabled="disabled"
                                                    :showToggleAll="false"
                                                    @change="
                                                        selectOpt($event.index)
                                                    "
                                                    v-model="
                                                        todo_model.fill_column_list_ids
                                                    "
                                                    placeholder="選擇自訂表單欄位"
                                                />
                                            </div>
                                            <div class="my-auto">
                                                <Checkbox
                                                    v-model="
                                                        todo_model.countersigned
                                                    "
                                                    class="mb-1"
                                                    :binary="true"
                                                />
                                                <label>會簽</label>
                                            </div>
                                            <div class="my-auto">
                                                <Checkbox
                                                    v-model="todo_model.child"
                                                    class="mb-1"
                                                    :binary="true"
                                                />
                                                <label>申請子單</label>
                                            </div>
                                            <div class="my-auto">
                                                <Checkbox
                                                    v-model="
                                                        todo_model.document
                                                    "
                                                    class="mb-1"
                                                    :binary="true"
                                                />
                                                <label>上傳附件</label>
                                            </div>
                                            <div class="my-auto">
                                                <Checkbox
                                                    v-model="
                                                        todo_model.limit_dollar
                                                    "
                                                    class="mb-1"
                                                    :binary="true"
                                                    @change="
                                                        limitDollar(
                                                            1,
                                                            index,
                                                            null,
                                                            todo_index
                                                        )
                                                    "
                                                />
                                                <label
                                                    @click="
                                                        role_modal_index =
                                                            todo_index;
                                                        role_modal_type =
                                                            todo_model.type;
                                                        tab_index = index;
                                                        money_col_id =
                                                            todo_model.dollar_col_id;
                                                        money =
                                                            todo_model.dollar;
                                                        currentModel =
                                                            todo_model;
                                                        displayModal3 = true;
                                                    "
                                                    class="underline cursor-pointer"
                                                    :class="
                                                        todo_model.limit_dollar
                                                            ? ''
                                                            : 'text-gray-300 pointer-events-none'
                                                    "
                                                    >金額判斷</label
                                                >
                                            </div>
                                            <button
                                                @click="
                                                    removeTodoPerson(
                                                        1,
                                                        index,
                                                        todo_index
                                                    )
                                                "
                                                class=""
                                            >
                                                <img
                                                    src="@images/icon/delete_enable.png"
                                                    alt=""
                                                />
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </draggable>
                            <div
                                @click="addTodoPerson(1, index)"
                                class="w-full h-16 text-current border-2 border-dashed hover:border-indigo-600 hover:bg-funcPurple py-4 text-center rounded-md cursor-pointer"
                            >
                                <i class="fa-solid fa-plus"></i>
                                <span class="my-auto inline-block"
                                    >新增關卡</span
                                >
                            </div>
                        </TabPanel>
                    </TabView>
                    <div
                        class="buttons mt-12 p-5 w-full flex justify-between md:justify-end"
                    >
                        <Button
                            @click="
                                (action = 0),
                                    (sign_roles = []),
                                    (todo_roles = []),
                                    ($root.layoutKey = null),
                                    ($root.layoutIndex = null),
                                    (col_flow.col_tabs = [])
                            "
                            label="取消"
                            class="p-button-outlined p-button-secondary w-28 mr-5"
                        />
                        <Button
                            @click="saveRoleSetting()"
                            label="儲存"
                            class="w-28"
                        />
                    </div>
                </div>
            </div>
        </div>
        <Toast ref="toast" position="top-center" />
        <Dialog
            :visible.sync="displayModal1"
            :closable="false"
            :containerStyle="{
                width: '20vw',
            }"
            :modal="true"
        >
            <div class="pb-8">
                <img class="mx-auto" src="@images/popup_state/warning.png" />
                <br />
                <p class="text-center">確定要刪除嗎?</p>
            </div>
            <template #footer>
                <div class="flex justify-evenly">
                    <Button
                        @click="displayModal1 = false"
                        label="取消"
                        class="p-button-outlined p-button-secondary"
                    />
                    <Button
                        @click="
                            col_flow.col_tabs.splice(tab_index, 1);
                            forceUpdateTabView += 1;
                            displayModal1 = false;
                        "
                        label="確定"
                        class=""
                    />
                </div>
            </template>
        </Dialog>
        <Dialog
            :visible.sync="displayModal2"
            :closable="false"
            :containerStyle="{
                width: '20vw',
            }"
            :modal="true"
            header="編輯選項"
            class="editDialog"
        >
            <div class="pb-8">
                <span>欄位選項</span>
                <br />
                <MultiSelect
                    optionLabel="name"
                    :options="edit_options"
                    v-model="col_edits"
                    class="w-32"
                />
            </div>
            <template #footer>
                <div class="flex justify-end">
                    <Button
                        @click="displayModal2 = false"
                        label="取消"
                        class="p-button-outlined p-button-secondary"
                    />
                    <Button @click="editOption" label="確定" class="" />
                </div>
            </template>
        </Dialog>
        <Dialog
            :visible.sync="displayModal"
            :dismissableMask="true"
            :closable="true"
            :containerStyle="{
                width: '30vw',
            }"
            :modal="true"
            header="新增流程"
            class="heighDialog"
        >
            <div class="pb-8">
                <span>需求單欄位</span>
                <br />
                <Dropdown
                    :options="col_flow.cols"
                    optionLabel="name"
                    v-model="col_flow.col"
                    :disabled="col_flow.col_tabs.length !== 0"
                />
                <br />
                <div v-if="col_flow.col !== null">
                    <span>欄位選項</span>
                    <br />
                    <Dropdown
                        :options="col_flow.col.options"
                        optionLabel="name"
                        @change="changeCol()"
                        v-model="col_flow.op"
                    />
                </div>
            </div>
            <template #footer>
                <div class="flex justify-end">
                    <Button
                        @click="displayModal = false"
                        label="取消"
                        class="p-button-outlined p-button-secondary"
                    />
                    <Button @click="selectCol" label="確定" class="" />
                </div>
            </template>
        </Dialog>
        <Dialog
            :visible.sync="displayModal3"
            :closable="false"
            :containerStyle="{
                width: '60vw',
                maxWidth: '800px',
            }"
            :modal="true"
            header="金額判斷"
            class="amountDialog"
        >
            <div class="pb-8">
                <span>需求單欄位</span>
                <br />
                <Dropdown
                    :options="role_modal_options"
                    optionLabel="name"
                    optionValue="id"
                    v-model="money_col_id"
                />
                <br />
                <span>限制金額</span>
                <br />
                <InputNumber
                    :disabled="
                        money_col_id == null || money_col_id == undefined
                    "
                    v-model="money"
                />
                <span>以上</span>
            </div>

            <section v-if="displayModal3">
                <div>
                    <Checkbox
                        v-model="currentModel.can_advanced_setting"
                        class="mb-1"
                        :binary="true"
                        id="money-setting"
                    />
                    <label for="money-setting">進階設定</label>
                </div>

                <h2
                    class="py-4 font-bold text-xl"
                    v-if="currentModel.can_advanced_setting"
                >
                    不顯示該關卡
                </h2>

                <div v-if="currentModel.can_advanced_setting">
                    <div
                        v-for="(col, index) in skip_level_list"
                        :key="index"
                        class="flex justify-between gap-2 my-8 max-w-3xl"
                    >
                        <section class="flex items-center">
                            <div class="relative">
                                <span
                                    class="absolute -top-6"
                                    :class="{
                                        'text-red-400': col.moneyInvalid,
                                    }"
                                    >金額</span
                                >
                                <InputNumber
                                    v-model="col.money"
                                    placeholder="輸入金額"
                                    :class="{
                                        'p-invalid': col.moneyInvalid,
                                    }"
                                />
                            </div>
                            <span class="ml-2">以下</span>
                        </section>

                        <section class="flex items-center flex-grow">
                            <span class="mr-2">且前關卡為</span>
                            <div class="relative flex-grow flex gap-1">
                                <span
                                    class="absolute -top-6"
                                    :class="{
                                        'text-red-400': col.employeeInvalid,
                                    }"
                                    >人員</span
                                >

                                <MultiSelect
                                    placeholder="選擇人員"
                                    :options="getEmployees"
                                    :filter="true"
                                    optionLabel="name"
                                    optionValue="id"
                                    data-key="id"
                                    v-model="col.employee_ids"
                                    :class="{
                                        'p-invalid': col.employeeInvalid,
                                    }"
                                    class="flex-grow"
                                >
                                    <template #option="slotProps">
                                        <div>
                                            <span>{{
                                                slotProps.option.org_name
                                            }}</span>
                                            <span>{{
                                                slotProps.option.name
                                            }}</span>
                                        </div>
                                    </template>
                                </MultiSelect>
                                <button
                                    @click="remove_skip_level(index)"
                                    class=""
                                >
                                    <img
                                        src="@images/icon/delete_enable.png"
                                        alt=""
                                    />
                                </button>
                            </div>
                        </section>
                    </div>
                    <Button
                        @click="add_skip_level()"
                        label="新增條件"
                        class=""
                    />
                </div>
            </section>
            <template #footer>
                <div class="flex justify-end">
                    <Button
                        @click="
                            displayModal3 = false;
                            money_col_id = null;
                            money = null;
                        "
                        label="取消"
                        class="p-button-outlined p-button-secondary"
                    />
                    <Button @click="setMoneyLimit" label="確定" class="" />
                </div>
            </template>
        </Dialog>
        <!-- 自行指定人員彈窗 -->
        <Dialog
            :visible.sync="displayModal4"
            :dismissableMask="true"
            :closable="true"
            :containerStyle="{
                width: '30vw',
            }"
            :modal="true"
            header="自行指定人員"
        >
            <div class="pb-8">
                <div>
                    <RadioButton
                        class="mb-1"
                        :value="true"
                        v-model="applicant_choose"
                        name="applicant_choose"
                        id="ac1"
                    />
                    <label for="ac1">申請人自行選擇</label>
                </div>
                <br />
                <div
                    :class="
                        this.demand_setting.length
                            ? ''
                            : 'text-gray-300 pointer-events-none'
                    "
                >
                    <RadioButton
                        class="mb-1"
                        :value="false"
                        v-model="applicant_choose"
                        name="applicant_choose"
                        id="ac2"
                    />
                    <label for="ac2">根據資料庫自動帶入人員</label>
                    <small class="text-red-500 block my-2"
                        >*需求單需先設定資料庫欄位才能設定此選項</small
                    >
                    <CascadeSelect
                        v-show="applicant_choose === false"
                        v-model="role_id"
                        :options="demand_setting"
                        optionGroupLabel="db_name"
                        optionLabel="column_name"
                        :optionGroupChildren="['db_columns']"
                        placeholder="選擇資料庫欄位"
                        style="min-width: 14rem"
                        class="absolute"
                    />
                </div>
            </div>
            <template #footer>
                <div class="flex justify-end">
                    <Button
                        @click="displayModal4 = false"
                        label="取消"
                        class="p-button-outlined p-button-secondary"
                    />
                    <Button @click="change_self_choose_dialog()" label="確定" />
                </div>
            </template>
        </Dialog>
    </div>
</template>
<script>
import axios from "axios";
import Button from "primevue/button";
import CascadeSelect from "primevue/cascadeselect";
import Checkbox from "primevue/checkbox";
import ConfirmDialog from "primevue/confirmdialog";
import Dropdown from "primevue/dropdown";
import Dialog from "primevue/dialog";
import InputText from "primevue/inputtext";
import InputNumber from "primevue/inputnumber";
import RadioButton from "primevue/radiobutton";
import MultiSelect from "primevue/multiselect";
import Toast from "primevue/toast";
import TabView from "primevue/tabview";
import TabPanel from "primevue/tabpanel";
import draggable from "vuedraggable";
export default {
    components: {
        Button,
        CascadeSelect,
        Checkbox,
        ConfirmDialog,
        Dropdown,
        Dialog,
        InputText,
        InputNumber,
        RadioButton,
        MultiSelect,
        Toast,
        TabView,
        TabPanel,
        draggable,
    },
    data() {
        return {
            applicant_choose: null,
            self_choose_dialog: null,
            role_id: {},
            demand_setting: [],
            action: 0,
            dataList: [],
            apiURL: "/api/demand/setting/groups",
            title: "",
            listId: 0,
            sign_roles: [],
            todo_roles: [],
            audit_musts: [],
            list_must: [],
            selected_audit_musts: [],
            selected_list_must: [],
            //選擇主管職等下拉
            managerRanks: [],
            getEmployees: [],
            role_type: null,
            displayModal: false,
            displayModal1: false,
            displayModal2: false,
            displayModal3: false,
            displayModal4: false,
            col_flow: {
                cols: [],
                col_tabs: [],
                col_ops: [],
                op: null,
                col: null,
            },
            tab_index: 0,
            option_index: null,
            col_edits: [],
            edit_options: [],
            role_modal_options: [],
            money_col_id: null,
            money: null,
            role_modal_index: null,
            role_modal_type: null,
            can_multi: false,
            forceUpdateTabView: 0,
            validated: false,
            skip_level_list: [],
            currentModel: {},
        };
    },
    watch: {
        "$root.currentTab": function (newValue) {
            if (this.action == 1 && newValue == 0) {
                this.$root.currentTab = 0;
                this.$root.layoutKey = null;
                this.$root.layoutIndex = null;
                this.sign_roles = [];
                this.todo_roles = [];
                this.action = 0;
            }
            if (
                newValue == 1 &&
                !this.getEmployees.length &&
                !this.managerRanks.length
            ) {
                this.getManagerRankDropdown();
                this.getEmployeesDropDown();
            }
            if (newValue == 1) {
                this.getGroups();
            }
        },
        action() {
            this.validated = false;
        },
        // 自行指定人員彈窗
        async displayModal4(newValue) {
            if (newValue) {
                await this.fetchDemandSetting();
                if (!("applicant_choose" in this.self_choose_dialog))
                    this.self_choose_dialog.applicant_choose = true;

                this.applicant_choose =
                    this.self_choose_dialog.applicant_choose;

                if (this.applicant_choose === false) {
                    this.$set(
                        this.role_id,
                        "db_id",
                        this.self_choose_dialog.db_id
                    );
                    this.$set(
                        this.role_id,
                        "column_id",
                        this.self_choose_dialog.column_id
                    );
                    this.$set(
                        this.role_id,
                        "column_name",
                        this.self_choose_dialog.column_name
                    );
                }
            } else {
                this.applicant_choose = null;
                this.role_id = {};
            }
        },
        // 金額判斷彈窗
        displayModal3(newValue) {
            if (newValue) {
                if (this.role_modal_type == 0) {
                    this.skip_level_list =
                        this.col_flow.col_tabs[this.tab_index].sign_role_models[
                            this.role_modal_index
                        ].skip_level_list ?? [];
                } else {
                    this.skip_level_list =
                        this.col_flow.col_tabs[this.tab_index].todo_role_models[
                            this.role_modal_index
                        ].skip_level_list ?? [];
                }
            }
        },
    },
    mounted() {
        this.getGroups();
    },
    methods: {
        getGroups() {
            axios
                .get(this.apiURL)
                .then((response) => {
                    this.dataList = response.data ?? [];
                    this.dataList.forEach((list) => {
                        list.layouts.sort((a, b) => {
                            return (
                                (b.sort != "" && b.sort != null) -
                                    (a.sort != "" && a.sort != null) ||
                                a.sort - b.sort
                            );
                        });
                    });
                    //自動帶入對應的layout
                    if (
                        this.$root.layoutKey !== null &&
                        this.$root.layoutIndex !== null &&
                        this.$root.currentTab == 1
                    ) {
                        this.getRoleSetting(
                            this.$root.layoutKey,
                            this.$root.layoutIndex
                        );
                    }
                })
                .catch((error) => {
                    console.error(error);
                });
        },
        getRoleSetting(key, index) {
            this.action = 1;
            this.$root.layoutKey = key;
            this.$root.layoutIndex = index;
            let row = this.dataList[key].layouts[index];
            this.listId = row.id;
            this.title = row.name;
            this.audit_musts = row.audit_musts.map((must) => ({
                ...must,
                name: `欄位${
                    row.cols.findIndex((col) => col.id === must.id) + 1
                } ${must.name.split(" ").slice(1).join(" ")}`,
            }));
            this.list_must = row.list_must;
            this.can_multi = row.can_multi;
            //需求單流程欄位選項
            this.col_flow.cols = row.cols.filter((col) => {
                return (
                    (col.type == "single" ||
                        (col.type == "dropdown" && col.mul !== 1) ||
                        col.type == "cascade") &&
                    col.must == true
                );
            });
            // 把cascade的選項改成組建可以套用的形式
            this.col_flow.cols = this.col_flow.cols.map((col) => {
                if (col.type == "cascade") {
                    col.options = col.options
                        .map((option) => {
                            if (option.states) {
                                return option.states.map((state) => {
                                    const newOption = {
                                        ...option,
                                        ...state,
                                        col_id: col.id,
                                        id: option.id + state.iname,
                                        name: option.gname + ", " + state.iname,
                                    };
                                    delete newOption.states;
                                    return newOption;
                                });
                            }
                            return option;
                        })
                        .flat();
                }
                return col;
            });
            this.role_modal_options = row.cols.filter((col) => {
                return col.type == "money";
            });
            if (row.sign_role[0].hasOwnProperty("roles")) {
                this.role_type = 1;
                const presentTabIds = row.cols
                    .find((col) => col.id === row.sign_role[0].col_id)
                    .options.map((op) => op.id); // 濾掉上次儲存但已被修改的欄位
                this.col_flow.col_tabs = row.sign_role.filter((tab) =>
                    presentTabIds.includes(tab.id)
                );
                // 修改tab.name為最新
                this.col_flow.col_tabs.forEach((tab) => {
                    const current_tab_option_ids = [tab.id, ...tab.option_ids];
                    tab.name = this.col_flow.cols
                        .find((col) => col.id == tab.col_id)
                        .options.filter((op) =>
                            current_tab_option_ids.includes(op.id)
                        )
                        .map((op) => op.name)
                        .join("、");
                });

                this.col_flow.col_tabs.length !== 0
                    ? this.getColItem(this.col_flow.col_tabs[0].col_id)
                    : null;
                row.sign_role.forEach((flow) => {
                    flow.sign_role_models.forEach((model) => {
                        model.audit_musts = JSON.parse(
                            JSON.stringify(this.audit_musts)
                        );
                        model.list_must = JSON.parse(
                            JSON.stringify(this.list_must)
                        );
                        model.fill_column_ids = model.fill_column_ids.filter(
                            (id) =>
                                model.audit_musts
                                    .map((el) => el.id)
                                    .includes(id)
                        );
                    });
                    flow.todo_role_models.forEach((model) => {
                        model.audit_musts = JSON.parse(
                            JSON.stringify(this.audit_musts)
                        );
                        model.list_must = JSON.parse(
                            JSON.stringify(this.list_must)
                        );
                        model.fill_column_ids = model.fill_column_ids.filter(
                            (id) =>
                                model.audit_musts
                                    .map((el) => el.id)
                                    .includes(id)
                        );
                    });
                });
                this.selectOpt();
            } else {
                this.role_type = 0;
                this.sign_roles = [];
                this.todo_roles = [];
                let roles = row.sign_role;
                if (roles) {
                    roles.forEach((role) => {
                        role.fill_column_ids = role.fill_column_ids.filter(
                            (el) =>
                                this.audit_musts.some((must) => must.id === el)
                        );
                        if (role.fill_column_list_ids) {
                            role.fill_column_list_ids =
                                role.fill_column_list_ids?.filter((el) =>
                                    this.list_must.some(
                                        (must) => must.id === el
                                    )
                                );
                        }
                        role.audit_musts = JSON.parse(
                            JSON.stringify(this.audit_musts)
                        );
                        role.list_must = JSON.parse(
                            JSON.stringify(this.list_must)
                        );
                        if (role.type == 0) {
                            this.sign_roles.push(role);
                        }
                        if (role.type == 1) {
                            this.todo_roles.push(role);
                        }
                    });
                }
                this.selectOpt();
            }
        },
        validate() {
            let error = false;
            let roles = this.sign_roles.concat(this.todo_roles);
            if (this.role_type == 1) {
                this.col_flow.col_tabs.forEach((tabRole) => {
                    let mulRoles = tabRole.sign_role_models.concat(
                        tabRole.todo_role_models
                    );
                    mulRoles.forEach((role) => {
                        if (role.self_applicant == false) {
                            if (role.type == 0) {
                                role.level == 0
                                    ? (error =
                                          true &&
                                          this.$set(role, "invalid", true))
                                    : this.$delete(role, "invalid");
                            } else {
                                !("role_id" in role)
                                    ? (error =
                                          true &&
                                          this.$set(role, "invalid", true))
                                    : this.$delete(role, "invalid");
                            }
                        }
                    });
                });
            } else {
                roles.forEach((role) => {
                    if (role.self_applicant == false) {
                        if (role.type == 0) {
                            role.level == 0
                                ? (error =
                                      true && this.$set(role, "invalid", true))
                                : this.$delete(role, "invalid");
                        } else {
                            !("role_id" in role)
                                ? (error =
                                      true && this.$set(role, "invalid", true))
                                : this.$delete(role, "invalid");
                        }
                    }
                });
            }
            this.validated = true;
            return error;
        },
        saveRoleSetting() {
            if (this.validate() == true) {
                this.$refs.toast.add({
                    severity: "error",
                    summary: "簽核人未填",
                    life: 3000,
                });
                return false;
            }
            let roles = this.sign_roles.concat(this.todo_roles);
            if (this.role_type == 1) {
                let tab_option_length = _.flatten(
                    this.col_flow.col_tabs.map((tab) => tab.option_ids)
                ).length;
                let tab_length =
                    parseInt(this.col_flow.col_tabs.length) +
                    parseInt(tab_option_length);
                if (
                    tab_length !== this.col_flow.col?.options.length ||
                    this.col_flow.col_tabs.some(
                        (tab) =>
                            tab.sign_role_models.length == 0 &&
                            tab.todo_role_models.length == 0
                    )
                ) {
                    this.$refs.toast.add({
                        severity: "error",
                        summary: "尚有選項未設流程，無法儲存",
                        life: 3000,
                    });
                    return false;
                }
                this.col_flow.col_tabs.forEach((tabRole) => {
                    let mulRoles = tabRole.sign_role_models.concat(
                        tabRole.todo_role_models
                    );
                    mulRoles.forEach((role, index) => {
                        role.id = index + 1;
                        tabRole.roles = mulRoles;
                    });
                });
            } else {
                roles.forEach((role, index) => {
                    if (role.self_applicant) {
                        delete role.role_id;
                    }
                    role.id = index + 1;
                });
            }
            let param = {
                id: this.listId,
                roles: this.role_type == 1 ? this.col_flow.col_tabs : roles,
                flow_setting_type: this.role_type,
            };

            axios
                .put("/api/demand/setting/layouts", param)
                .then((response) => {
                    if (response.data) {
                        this.$refs.toast.add({
                            severity: "success",
                            summary: "需求單設定儲存成功",
                            life: 3000,
                        });
                        this.getGroups();
                        this.action = 0;
                        this.sign_roles = [];
                        this.todo_roles = [];
                        this.$root.layoutKey = null;
                        this.$root.layoutIndex = null;
                    } else {
                        this.$refs.toast.add({
                            severity: "error",
                            summary: "儲存失敗!!請重新操作",
                            life: 3000,
                        });
                    }
                })
                .catch((error) => {
                    console.error(error);
                    if (error.response) {
                        this.$refs.toast.add({
                            severity: "error",
                            summary: "儲存失敗!!請重新操作",
                            life: 3000,
                        });
                    }
                })
                .finally(() => {
                    this.col_flow.col_tabs = [];
                });
        },
        addSignPerson(type, index) {
            let role = {
                type: 0,
                child: false,
                level: 0,
                document: false,
                countersigned: false,
                can_advanced_setting: false,
                fill_column_ids: [],
                fill_column_list_ids: [],
                audit_musts: JSON.parse(JSON.stringify(this.audit_musts)),
                list_must: JSON.parse(JSON.stringify(this.list_must)),
                self_name:
                    type == 0
                        ? "審核關卡" + (this.sign_roles.length + 1)
                        : "審核關卡" +
                          (this.col_flow.col_tabs[index].sign_role_models
                              .length +
                              1),
                self_applicant: false,
                limit_dollar: false,
            };
            type == 0
                ? this.sign_roles.push(role)
                : this.col_flow.col_tabs[index].sign_role_models.push(role);
            this.selectOpt();
        },
        addTodoPerson(type, index) {
            let role = {
                type: 1,
                child: false,
                document: false,
                countersigned: false,
                can_advanced_setting: false,
                fill_column_ids: [],
                fill_column_list_ids: [],
                audit_musts: JSON.parse(JSON.stringify(this.audit_musts)),
                list_must: JSON.parse(JSON.stringify(this.list_must)),
                self_name:
                    type == 0
                        ? "審核關卡" + (this.todo_roles.length + 1)
                        : "審核關卡" +
                          (this.col_flow.col_tabs[index].todo_role_models
                              .length +
                              1),
                self_applicant: false,
                limit_dollar: false,
            };
            type == 0
                ? this.todo_roles.push(role)
                : this.col_flow.col_tabs[index].todo_role_models.push(role);
            this.selectOpt();
        },
        removeSignPerson(type, tab_index, sign_index) {
            type == 1
                ? this.col_flow.col_tabs[tab_index].sign_role_models.splice(
                      sign_index,
                      1
                  )
                : this.sign_roles.splice(sign_index, 1);
        },
        removeTodoPerson(type, tab_index, todo_index) {
            type == 1
                ? this.col_flow.col_tabs[tab_index].todo_role_models.splice(
                      todo_index,
                      1
                  )
                : this.todo_roles.splice(todo_index, 1);
        },
        selectOpt() {
            this.selected_audit_musts = [];
            this.selected_list_must = [];
            if (
                this.col_flow.col_tabs.length !== 0 &&
                this.col_flow.col_tabs[0].hasOwnProperty("roles")
            ) {
                let roles = this.col_flow.col_tabs[
                    this.tab_index
                ].sign_role_models.concat(
                    this.col_flow.col_tabs[this.tab_index].todo_role_models
                );
                roles.forEach((role) => {
                    this.selected_audit_musts =
                        this.selected_audit_musts.concat(role.fill_column_ids);
                    this.selected_list_must = this.selected_list_must.concat(
                        role.fill_column_list_ids
                    );
                });
                roles.forEach((role) => {
                    role.audit_musts.forEach((audit_must) => {
                        if (
                            this.selected_audit_musts.includes(audit_must.id) &&
                            !role.fill_column_ids.includes(audit_must.id)
                        ) {
                            audit_must.disabled = true;
                        } else {
                            audit_must.disabled = undefined;
                        }
                    });
                    role.list_must.forEach((must) => {
                        if (
                            this.selected_list_must.includes(must.id) &&
                            !role.fill_column_list_ids.includes(must.id)
                        ) {
                            must.disabled = true;
                        } else {
                            must.disabled = undefined;
                        }
                    });
                });
            } else {
                let roles = this.sign_roles.concat(this.todo_roles);
                roles.forEach((role) => {
                    this.selected_audit_musts =
                        this.selected_audit_musts.concat(role.fill_column_ids);
                    this.selected_list_must = this.selected_list_must.concat(
                        role.fill_column_list_ids
                    );
                });
                roles.forEach((role) => {
                    role.audit_musts.forEach((audit_must) => {
                        if (
                            this.selected_audit_musts.includes(audit_must.id) &&
                            !role.fill_column_ids.includes(audit_must.id)
                        ) {
                            audit_must.disabled = true;
                        } else {
                            audit_must.disabled = undefined;
                        }
                    });
                    role.list_must.forEach((must) => {
                        if (
                            this.selected_list_must.includes(must.id) &&
                            !role.fill_column_list_ids.includes(must.id)
                        ) {
                            must.disabled = true;
                        } else {
                            must.disabled = undefined;
                        }
                    });
                });
            }
            // 強制vue刷新
            this.$forceUpdate();
        },
        changeTab(index) {
            this.tab_index = index;
            this.selectOpt();
        },
        changeRoleType() {
            if (
                (this.role_type == 0 || this.role_type == null) &&
                this.can_multi
            ) {
                this.role_type = 0;
                this.$refs.toast.add({
                    severity: "error",
                    summary: "已設定多筆申請，無法依選項設定流程",
                    life: 3000,
                });
            } else if (this.role_type == 0 || this.role_type == null) {
                this.role_type = 1;
            } else {
                this.role_type = 0;
            }
        },
        getManagerRankDropdown() {
            axios
                .get("/api/demand/manager/rank/dropdown")
                .then((response) => {
                    this.managerRanks = response.data ?? [];
                })
                .catch((error) => {
                    console.error(error);
                });
        },
        getEmployeesDropDown() {
            axios
                .get("/api/demand/employees")
                .then((response) => {
                    this.getEmployees = response.data ?? [];
                })
                .catch((error) => {
                    console.error(error);
                });
        },
        getColItem(id) {
            this.col_flow.cols.forEach((col) => {
                if (id == col.id) {
                    this.col_flow.col = col;
                }
            });
        },
        changeCol() {
            this.col_flow.op = Object.assign({}, this.col_flow.op);
        },
        selectCol() {
            if (this.col_flow.op !== null && this.col_flow.col !== null) {
                // tab 已經存在跳過
                if (
                    this.col_flow.col_tabs.some(
                        (tab) => tab.id == this.col_flow.op.id
                    )
                )
                    return;
                // 已經存在於其他 tab 內跳過
                if (
                    this.col_flow.col_tabs.some((tab) =>
                        tab.option_ids.includes(this.col_flow.op.id)
                    )
                )
                    return;

                this.$set(this.col_flow.op, "sign_role_models", []);
                this.$set(this.col_flow.op, "todo_role_models", []);
                this.$set(this.col_flow.op, "col_id", this.col_flow.col.id);
                this.$set(this.col_flow.op, "option_ids", []);
                this.col_flow.col_tabs.push(this.col_flow.op);
            }
            this.forceUpdateTabView += 1;
            this.displayModal = false;
        },
        displayModalEdit(key) {
            this.displayModal2 = true;
            this.tab_index = key;
            this.col_edits = [];
            if (this.displayModal2) {
                let tab_op_ids = _.flatten(
                    this.col_flow.col_tabs.map((tab) => tab.option_ids)
                );
                this.edit_options = this.col_flow.col.options.filter(
                    ({ id, name }) =>
                        !this.col_flow.col_tabs.some((tab) => tab.id === id) &&
                        !tab_op_ids.some((i) => i === id)
                );
                this.col_edits = this.edit_options.filter((item) => {
                    return (
                        this.col_flow.col_tabs[
                            this.tab_index
                        ].option_ids.indexOf(item.id) > -1
                    );
                });
            }
        },
        editOption() {
            const edits = this.col_edits.map((item) => {
                return "、" + item.name;
            });
            edits.filter((item) => {
                return item !== undefined;
            });
            const opName = this.col_flow.col.options.filter((item) => {
                return item.id == this.col_flow.col_tabs[this.tab_index].id;
            });
            let col_ids = this.col_edits.map((item) => {
                return item.id;
            });
            let tab_op_ids = _.flatten(
                this.col_flow.col_tabs.map((tab) => tab.option_ids)
            );
            let has_id = _.difference(col_ids, tab_op_ids);
            if (has_id.length != 0) {
                this.col_flow.col_tabs[this.tab_index].name =
                    opName[0].name.concat(...edits);
                this.col_flow.col_tabs[this.tab_index].option_ids =
                    this.col_flow.col_tabs[this.tab_index].option_ids.concat(
                        has_id
                    );
            }
            this.displayModal2 = false;
            this.col_edits = [];
        },
        // 儲存金額判斷
        setMoneyLimit() {
            if (this.role_type == 1) {
                this.col_flow.col_tabs[this.tab_index].sign_role_models.forEach(
                    (role) => (role.dollar_col_id = this.money_col_id)
                );
                this.col_flow.col_tabs[this.tab_index].todo_role_models.forEach(
                    (role) => (role.dollar_col_id = this.money_col_id)
                );

                if (this.role_modal_type == 0) {
                    this.col_flow.col_tabs[this.tab_index].sign_role_models[
                        this.role_modal_index
                    ].dollar = this.money;

                    // 依據特定人員及金額跳過簽核關卡(審核人)
                    this.col_flow.col_tabs[this.tab_index].sign_role_models[
                        this.role_modal_index
                    ].skip_level_list = this.skip_level_list;
                } else {
                    this.col_flow.col_tabs[this.tab_index].todo_role_models[
                        this.role_modal_index
                    ].dollar = this.money;

                    // 依據特定人員及金額跳過簽核關卡(經辦人)
                    this.col_flow.col_tabs[this.tab_index].todo_role_models[
                        this.role_modal_index
                    ].skip_level_list = this.skip_level_list;
                }
            } else {
                this.sign_roles.forEach(
                    (role) => (role.dollar_col_id = this.money_col_id)
                );
                this.todo_roles.forEach(
                    (role) => (role.dollar_col_id = this.money_col_id)
                );
                if (this.role_modal_type == 0) {
                    this.sign_roles[this.role_modal_index].dollar = this.money;
                    // 依據特定人員及金額跳過簽核關卡(審核人)
                    this.sign_roles[this.role_modal_index].skip_level_list =
                        this.skip_level_list;
                } else {
                    this.todo_roles[this.role_modal_index].dollar = this.money;
                    // 依據特定人員及金額跳過簽核關卡(經辦人)
                    this.todo_roles[this.role_modal_index].skip_level_list =
                        this.skip_level_list;
                }
            }

            if (!this.check_level_list()) return;

            this.skip_level_list = [];
            this.displayModal3 = false;
            this.money = null;
            this.money_col_id = null;
        },
        limitDollar(type, tab_index, sign_index, todo_index) {
            if (this.can_multi) {
                this.$refs.toast.add({
                    severity: "error",
                    summary: "已設定多筆申請，無法開啟金額判斷",
                    life: 3000,
                });
                if (type == 1 && todo_index == null) {
                    this.col_flow.col_tabs[tab_index].sign_role_models[
                        sign_index
                    ].limit_dollar = false;
                } else if (type == 1 && sign_index == null) {
                    this.col_flow.col_tabs[tab_index].todo_role_models[
                        todo_index
                    ].limit_dollar = false;
                } else if (type == 0 && todo_index == null) {
                    this.sign_roles[sign_index].limit_dollar = false;
                } else if (type == 0 && sign_index == null) {
                    this.todo_roles[todo_index].limit_dollar = false;
                }
            }
        },
        // 撈取需求單欄位設定
        async fetchDemandSetting() {
            if (!this.demand_setting.length) {
                try {
                    const res = await axios.get(
                        "/api/demand/setting/layouts/fetch-list",
                        {
                            params: {
                                listId: this.listId,
                            },
                        }
                    );
                    this.demand_setting = res.data;
                } catch (error) {
                    console.error(error);
                }
            }
        },
        // 修改自行指定人員
        change_self_choose_dialog() {
            if (this.applicant_choose === null) return;

            if (this.applicant_choose === false) {
                if (Object.keys(this.role_id).length === 0) return;
                this.self_choose_dialog.db_id = this.role_id.db_id;
                this.self_choose_dialog.column_id = this.role_id.column_id;
                this.self_choose_dialog.column_name = this.role_id.column_name;
            }
            this.self_choose_dialog.applicant_choose = this.applicant_choose;
            this.applicant_choose = null;
            this.displayModal4 = false;
            this.role_id = {};
        },
        // 新增金額判斷跳過關卡
        add_skip_level() {
            this.skip_level_list.push({
                money: null,
                employee_id: null,
            });
        },
        // 移除金額判斷跳過關卡
        remove_skip_level(index) {
            this.skip_level_list.splice(index, 1);
        },
        // 檢查金額判斷跳過關卡
        check_level_list() {
            let canPass = true;
            this.skip_level_list.forEach((level) => {
                if (!level.money) {
                    this.$set(level, "moneyInvalid", true);
                    canPass = false;
                } else {
                    this.$set(level, "moneyInvalid", false);
                }

                if (!level.employee_ids || !level.employee_ids?.length) {
                    this.$set(level, "employeeInvalid", true);
                    canPass = false;
                } else {
                    this.$set(level, "employeeInvalid", false);
                }
            });
            return canPass;
        },
        // 金額判斷彈窗
        displayModalMoney(sign_index) {
            role_modal_index = sign_index;
            displayModal3 = true;
        },
    },
};
</script>
<style>
.heighDialog .p-dialog-content {
    height: 28rem;
}
.editDialog .p-dialog-content {
    height: 20rem;
    overflow: unset;
}
.p-tabview-nav,
.p-tabview-panels {
    background: rgb(250, 251, 253) !important;
}
.important-color > .p-multiselect-label-container > .p-placeholder {
    color: rgba(212, 212, 212) !important;
}

.p-tabview-nav {
    height: max-content;
    overflow-y: hidden;
}

.p-tabview-nav a {
    margin: 0 !important;
}

.p-tabview .p-tabview-panels {
    padding-left: 0 !important;
    padding-right: 0 !important;
}
</style>
