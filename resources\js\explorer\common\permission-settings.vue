<template>
  <div>
    <!-- 權限管理 -->
    <Dialog
      :visible.sync="computedPermissionSettingsVisible"
      :containerStyle="{ maxWidth: '90%', maxHeight: '90%', width: '1016px' }"
      :modal="true"
      position="left"
      dismissableMask
      class="justify-center"
    >
      <template #header>
        <div
          class="flex flex-col md:flex-row w-full pr-8 justify-start md:justify-between items-start"
        >
          <h1 class="text-3xl font-bold text-explorerPrimary whitespace-nowrap">
            權限管理
          </h1>
          <button
            @click="handleRemoveMembers"
            class="md:px-2 py-1 rounded-lg hover:bg-gray-200 transition mt-4 md:mt-0"
          >
            <div class="flex item-center justify-center">
              <img src="@images/icon/delete.svg" alt="delete-icon" />
              <span class="ml-2">清除人員</span>
            </div>
          </button>
        </div>
      </template>

      <table class="w-full h-full">
        <thead class="text-left font-bold text-explorerPrimary bg-gray-50">
          <tr>
            <th
              v-for="(header, index) in tableHeader"
              :key="index"
              class="first:px-6 py-3"
            >
              {{ header }}
            </th>
          </tr>
        </thead>
        <tbody v-if="tableData">
          <tr
            v-for="data in tableData"
            :key="data.id"
            class="border-b border-gray-200 last:border-0"
          >
            <td class="px-6 py-3 text-left text-explorerPrimary">
              {{ data.name }}
            </td>
            <td class="py-3 text-left text-explorerPrimary">
              <div v-if="data.members?.length">
                <span
                  v-for="(member, index) of data.members.slice(0, 5)"
                  :key="member.id"
                >
                  <template v-if="index > 0">、</template>
                  {{ member.name }}
                </span>
                <template v-if="data.members.length > 5">...</template>
              </div>
              <div v-else>
                <span>-</span>
              </div>
            </td>
            <td>
              <button
                class="hover:bg-gray-200 rounded-full w-6 h-6 transition p-1"
                @click="openEditDialog(data, $event)"
              >
                <img
                  src="@images/icon/edit.svg"
                  alt="edit-icon"
                  class="mx-auto"
                />
              </button>
            </td>
          </tr>
        </tbody>
      </table>
      <template #footer>
        <div></div>
      </template>
    </Dialog>

    <EditMembers
      editType="folder"
      :title="editDialogTitle"
      :isShow="isShowEditDialog"
      :selectedMemberIds="selectedMemberIds"
      @onUpdate="updatePermission"
      @toggleEditDialog="toggleEditDialog"
      @togglePermissionDialog="togglePermissionDialog"
      @updateSelectedMemberIds="updateSelectedMemberIds"
    />
  </div>
</template>

<script>
import Dialog from "primevue/dialog";
import MultiSelect from "primevue/multiselect";
import EditMembers from "./edit-members.vue";
import { permissionMixin } from "@/mixins/permissionMixin";
import {
  getAFolderPermissions,
  updateFolderPermissions,
  removePermission,
} from "@/axios/explorerApi.js";

export default {
  name: "PermissionSettings",
  components: {
    Dialog,
    MultiSelect,
    EditMembers,
  },
  props: {
    isShow: { type: Boolean, required: true },
    editPermissionDialog: { type: Boolean, default: false },
    folderId: { required: true },
  },
  mixins: [permissionMixin],
  data() {
    return {
      tableHeader: ["權限名稱", "權限人員", ""],
      tableData: [],
      isChanged: false,
    };
  },
  methods: {
    fetchTableData: _.debounce(async function () {
      if (typeof this.folderId !== "number") return;
      try {
        const data = await getAFolderPermissions(this.folderId);
        this.tableData = [
          {
            id: 1,
            type: "admin",
            name: "管理",
            members: data?.admin,
          },
          {
            id: 2,
            type: "read",
            name: "瀏覽",
            members: data?.read,
          },
          {
            id: 3,
            type: "download",
            name: "下載",
            members: data?.download,
          },
        ];
      } catch (error) {
        this.$refs.toast.add({
          severity: "error",
          summary: error.message,
          life: 3000,
        });
      }
    }, 500),
    async updatePermission(newMemberIds) {
      try {
        this.isShowEditDialog = false;
        this.computedPermissionSettingsVisible = true;
        await updateFolderPermissions(this.folderId, {
          type: this.editOption,
          users: newMemberIds,
        });
        this.$refs.toast.add({
          severity: "success",
          summary: "編輯成功",
          life: 3000,
        });
        this.selectedMemberIds = [];
        this.isChanged = true;
        this.fetchTableData();
      } catch (error) {
        this.$refs.toast.add({
          severity: "error",
          summary: error.message,
          life: 3000,
        });
      }
    },
    handleRemoveMembers(event) {
      this.$refs.confirmPopup.visible = true;
      this.$refs.confirmPopup.target = event.currentTarget;
      this.$refs.confirmPopup.confirmation = {
        message: "是否確定刪除？",
        acceptLabel: "確定",
        rejectLabel: "取消",
        acceptClass: "font-bold",
        rejectClass:
          "border border-explorerPrimary text-explorerPrimary font-bold bg-white transition",
        accept: async () => {
          try {
            await removePermission(this.folderId);
            this.$refs.toast.add({
              severity: "success",
              summary: "清除成功",
              life: 3000,
            });
            this.isChanged = true;
            this.fetchTableData();
          } catch (error) {
            this.$refs.toast.add({
              severity: "error",
              summary: "清除失敗",
              life: 3000,
            });
          }
        },
      };
    },
  },
  computed: {
    computedPermissionSettingsVisible: {
      get() {
        return this.isShow;
      },
      set(value) {
        this.$emit("togglePermission", value, this.folderId);
      },
    },
  },
  watch: {
    isShow: function () {
      if (this.isShow) this.fetchTableData();
    },
  },
};
</script>
