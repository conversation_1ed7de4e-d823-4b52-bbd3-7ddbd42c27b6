<?php

namespace App\Modules\Demand\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Log;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Arr;
use Carbon\Carbon;
use Exception;
use Illuminate\Support\Facades\DB;
use App\Jobs\CheckSignOffNotification;
use App\Modules\Demand\Models\Company;
use App\Modules\Demand\Models\DemandSetting;

class NotifySettingsController extends Controller
{
    protected $company_id;
    protected $user_id;
    protected $timezone;
    public function __construct()
    {
        $this->user_id = Session::get('employee_id');
        $this->company_id = Session::get('CompanyId');
        $this->timezone = Session::get('timezone');
    }
    public function index()
    {
        return view('demand.setting.notify_setting');
    }
    public function fetchNotification()
    {
        // <a href=\"http://192.168.1.180/Bellerp_GO/BellLogin.aspx?ReturnUrl=%2fbellerp_go%2f\">這裡</a>
        $result = DemandSetting::firstOrCreate(['payload->company_id'=>$this->company_id],
            [
                'payload' => [
                    'company_id'=>$this->company_id,
                    'noticeSend' => false,
                    'noticeFrequency' => 1,
                    'msg' => '親愛的同仁您好，您尚有{x}張需求單尚未簽核，請點<a href=\"https://sso.funcsync.com/richhonour/login\" rel=\"noopener noreferrer\" target=\"_blank\">這裡</a>登入系統更新',

                ],
                'metadata'=>collect([]),
            ]
        );


        $result = [
            "settings" =>  $result->payload
        ];

        return $result;
    }

    public function updateNotification(Request $request)
    {
        $payload = $request->input('settings');
        $payload['company_id']=$this->company_id;
        try {
            DB::beginTransaction();
            DemandSetting::where('payload->company_id', $this->company_id)
                ->update(
                    ['payload' => $payload]
                );
            if ($payload['noticeSend']) {
                // 更新資料就都要重記日期，隔天執行排程
                Company::find($this->company_id)
                    ->forceFill(['metadata->lastNotificationDate->demandSignOff' => now()->addDays(1)->toISOString()])
                    ->save();
            }
            DB::commit();
            return 1;
        } catch (Exception $e) {
            // \Log::error($e);
            DB::rollBack();
            return 0;
        }
    }


}
