<template>
    <div>
        <!-- 資料庫列表 -->
        <div v-if="action == 0">
            <div class="text-right mb-6">
                <Button @click="add()" label="新增" class="w-28"></Button>
            </div>
            <hr class="my-6 text-gray-200" />
            <div v-for="(item, item_index) in dataList" :key="item_index">
                <div
                    @click="fetchDbColumn(item_index)"
                    class="flex justify-between p-6 w-full bg-white rounded-xl shadow cursor-pointer"
                >
                    <div class="flex">
                        <InputText
                            @click.stop
                            v-if="item.edit"
                            v-model="item.name"
                            class="w-24 h-7"
                        ></InputText>
                        <p v-else class="mr-4 my-auto font-bold">
                            {{ item.name }}
                        </p>

                        <button @click.stop="changeIcon(item_index)">
                            <img
                                v-if="item.edit"
                                src="@images/icon/save.png"
                                alt=""
                            />
                            <img
                                v-else
                                src="@images/icon/edit_enable.png"
                                alt=""
                            />
                        </button>
                    </div>
                    <div>
                        <button @click.stop="deleteDatabase(item_index)">
                            <img src="@images/icon/delete_enable.png" alt="" />
                        </button>
                    </div>
                </div>
                <div class="my-4 flex flex-wrap"></div>
            </div>
        </div>
        <!-- 新增/修改資料庫 -->
        <div v-if="action == 1">
            <span @click="cancelColumn" class="cursor-pointer">
                <i class="fas fa-arrow-left"></i>
            </span>
            <div class="text-center">
                <!-- 這裡title 要放資料庫名稱 -->
                <span class="text-xl font-semibold"> {{ name }}</span>
            </div>
            <div class="flex justify-end w-full">
                <img
                    @click="$refs.fileInput.click()"
                    id="file-upload"
                    class="mr-6 cursor-pointer"
                    src="@images/icon/import_enable.png"
                    title="匯入"
                />
                <input
                    type="file"
                    ref="fileInput"
                    @change="inputExcel"
                    class="visuallyhidden"
                    accept=".xlsx,.xls"
                />
                <img
                    @click="outputExcel"
                    class="cursor-pointer"
                    src="@images/icon/export_enable.png"
                    title="匯出"
                />
            </div>

            <div>
                <div
                    style="background: #f8f5ff"
                    class="rounded-t-xl shadow overflow-x-auto"
                >
                    <div class="flex">
                        <div class="border-gray-200 w-10">
                            <button class="w-24 h-7 my-5 mx-10"></button>
                            <div
                                v-for="(data, data_index) in columns[0].data"
                                :key="data_index"
                                class="bg-white border-b-2 border-gray-200"
                            >
                                <button class="w-10 h-7 my-5 cursor-auto">
                                    {{ data_index + 1 }}
                                </button>
                            </div>
                        </div>
                        <div
                            v-for="(column, column_index) in columns"
                            :key="column_index"
                            class="border-l-2 border-gray-200"
                        >
                            <div class="relative">
                                <input
                                    v-model="column.name"
                                    style="background: #f8f5ff"
                                    class="w-24 h-7 p-3 my-5 mx-10"
                                    placeholder="輸入標題"
                                />
                                <button
                                    v-if="columns.length > 1"
                                    @click="deleteRow(column_index)"
                                    class="w-10 h-7 absolute top-4 -right-0.5"
                                >
                                    <img
                                        src="@images/icon/delete_enable.png"
                                        alt=""
                                    />
                                </button>
                            </div>
                            <div
                                v-for="(data, data_index) in column.data"
                                :key="data_index"
                                class="bg-white border-b-2 border-gray-200"
                            >
                                <input
                                    v-model="column.data[data_index]"
                                    class="w-24 h-7 p-3 my-5 mx-10"
                                    placeholder="輸入內容"
                                />
                            </div>
                        </div>
                        <div class="border-l-2 border-gray-200 flex-grow">
                            <button
                                @click="addCol()"
                                class="w-24 h-7 my-5 mx-10"
                            >
                                <i class="fa-solid fa-plus"></i>
                                <span class="my-auto inline-block"
                                    >新增標題</span
                                >
                            </button>
                            <div
                                v-for="(data, data_index) in columns[0].data"
                                :key="data_index"
                                class="bg-white border-b-2 border-gray-200 text-right"
                            >
                                <button
                                    @click="deleteCol(data_index)"
                                    class="h-7 my-5 mx-10"
                                >
                                    <img
                                        v-if="columns[0].data.length > 1"
                                        src="@images/icon/delete_enable.png"
                                        alt=""
                                    />
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <button
                @click="addRow"
                class="rounded-b-xl w-full pt-1.5 h-10 bg-white text-center shadow"
            >
                <i class="fa-solid fa-plus"></i>
                <span class="my-auto inline-block">新增欄位</span>
            </button>
            <div
                class="buttons mt-12 w-full flex justify-between md:justify-end"
            >
                <Button
                    @click="cancelColumn()"
                    label="取消"
                    class="p-button-outlined p-button-secondary w-28 mr-5"
                />
                <Button @click="saveColumn()" label="儲存" class="w-28" />
            </div>
        </div>
        <ConfirmDialog ref="confirmDialog" />
        <Toast ref="toast" position="top-center" />
    </div>
</template>
<script>
import axios from "axios";
import Button from "primevue/button";
import ConfirmDialog from "primevue/confirmdialog";
import InputText from "primevue/inputtext";
import Toast from "primevue/toast";
export default {
    components: {
        Button,
        ConfirmDialog,
        InputText,
        Toast,
    },
    data() {
        return {
            action: 0,
            apiURL: "/api/demand/setting/databases",
            dataList: [],
            //=====資料庫欄位設定=====
            id: 0,
            name: "",
            columns: [{ id: 0 }],
            key: 0,
            columnId: 0,
            //匯出匯入
            formData: new FormData(),
        };
    },
    mounted() {
        this.getDbSetting();
    },
    methods: {
        //=====資料庫欄位設定=====
        fetchDbColumn(key) {
            this.action = 1;
            this.key = key;
            this.id = this.dataList[key].id;
            this.name = this.dataList[key].name;
            this.columns = this.dataList[key].columns;
            let len = this.columns.length;
            this.columnId = this.columns[len - 1].id + 1;
            let max_data_len = this.columns.reduce((prev, col) => {
                return Math.max(prev, col.data.length);
            }, 0);
            this.columns.forEach((col) => {
                col.data.length = max_data_len;
            });
        },
        cancelColumn() {
            this.action = 0;
            this.columns = [];
        },
        saveColumn() {
            this.columns.forEach((col) => {
                col.data = col.data.filter((d) => d != "");
            });
            let param = {
                id: this.id,
                name: this.name,
                columns: JSON.stringify(this.columns),
            };
            this.updateColumn(param, this.key);
        },
        //==============
        add() {
            let len = this.dataList.length;
            let columnsLen = this.columns.length;
            let defaultName = {
                name: "資料庫名稱" + this.dataList.length++,
                edit: false,
                id: 0,
                columns: [
                    {
                        id: this.columns[columnsLen - 1].id + 1,
                        data: [""],
                        name: "",
                    },
                ],
            };
            axios
                .post(this.apiURL, defaultName)
                .then((response) => {
                    if (response.data) {
                        defaultName.id = response.data;
                        this.$set(this.dataList, len, defaultName);
                        this.$refs.toast.add({
                            severity: "success",
                            summary: "新增成功",
                            life: 3000,
                        });
                    } else {
                        this.$refs.toast.add({
                            severity: "error",
                            summary: "新增失敗",
                            life: 3000,
                        });
                    }
                })
                .catch((error) => {
                    console.error(error);
                    this.$refs.toast.add({
                        severity: "error",
                        summary: "新增失敗",
                        life: 3000,
                    });
                });
        },
        deleteDatabase(key) {
            this.$refs.confirmDialog.visible = true;
            this.$refs.confirmDialog.confirmation = {
                message: "確定要刪除嗎?",
                acceptLabel: "確定",
                rejectLabel: "取消",
                accept: () => {
                    axios
                        .delete(this.apiURL + "/" + this.dataList[key].id)
                        .then((response) => {
                            console.log(response.data);
                            if (response.data) {
                                this.$refs.toast.add({
                                    severity: "success",
                                    summary: "刪除成功",
                                    life: 3000,
                                });
                                this.dataList.splice(key, 1);
                            } else {
                                this.$refs.toast.add({
                                    severity: "error",
                                    summary: "刪除失敗",
                                    life: 3000,
                                });
                            }
                        })

                        .catch((error) => {
                            console.error(error);
                            this.$refs.toast.add({
                                severity: "error",
                                summary: "刪除失敗",
                                life: 3000,
                            });
                        });
                },
            };
        },
        getDbSetting() {
            axios
                .get(this.apiURL)
                .then((response) => {
                    let dataRows = response.data;
                    if (dataRows) {
                        dataRows.forEach((value, index) => {
                            dataRows[index].edit = false;
                        });
                        this.dataList = dataRows;
                    }
                })
                .catch((error) => {
                    console.error(error);
                });
        },
        saveName(key) {
            let param = {
                id: this.dataList[key].id,
                name: this.dataList[key].name,
            };
            this.updateColumn(param, key);
        },
        updateColumn(param, key) {
            //  console.log(this.columns);
            axios
                .put(this.apiURL, param)
                .then((response) => {
                    if (response.data) {
                        this.dataList[key].edit = false;
                        this.$refs.toast.add({
                            severity: "success",
                            summary: "修改成功",
                            life: 3000,
                        });
                        if (this.action) {
                            this.action = 0;
                        }
                    } else {
                        this.$refs.toast.add({
                            severity: "error",
                            summary: "修改失敗",
                            life: 3000,
                        });
                    }
                })
                .catch((error) => {
                    console.error(error);
                    if (error.response) {
                        this.$refs.toast.add({
                            severity: "error",
                            summary: "修改失敗",
                            life: 3000,
                        });
                    }
                });
        },
        changeIcon(key) {
            let icon = this.dataList[key].edit;
            if (!icon) {
                this.dataList[key].edit = true;
            } else {
                this.saveName(key);
            }
        },

        //=====資料庫設定=====
        addCol() {
            let defaultCol = {
                id: this.columnId,
                name: "",
                data: [],
            };
            // console.log(this.columns[0].data);
            for (let i = 0; i < this.columns[0].data.length; i++) {
                defaultCol.data.push("");
            }
            this.$set(this.columns, this.columns.length, defaultCol);
        },
        addRow() {
            this.columns.forEach((col) => {
                col.data.push("");
            });
        },
        deleteCol(data_index) {
            this.columns.forEach((col) => {
                col.data.splice(data_index, 1);
            });
        },
        deleteRow(column_index) {
            this.columns.splice(column_index, 1);
        },
        //====匯入匯出
        outputExcel() {
            window.open(this.apiURL + "/export?id=" + this.id);
        },
        inputExcel(e) {
            this.formData.append("file", e.target.files[0]);
            this.formData.append("id", this.id);
            axios
                .post(this.apiURL + "/import", this.formData)
                .then((response) => {
                    if (response.data.success) {
                        this.$refs.toast.add({
                            severity: "success",
                            summary: "匯入成功",
                            life: 3000,
                        });
                        this.columns = response.data.cols;
                        this.dataList[this.key].columns = response.data.cols;
                    } else {
                        this.$refs.toast.add({
                            severity: "error",
                            summary: response.data.errors[0], //後端做錯誤訊息
                            life: 3000,
                        });
                    }
                });
        },
    },
};
</script>
<style>
.p-dialog-header {
    justify-content: end;
}
.p-dialog {
    width: 30rem;
}
.visuallyhidden {
    border: 0;
    clip: rect(0 0 0 0);
    height: 1px;
    margin: -1px;
    overflow: hidden;
    padding: 0;
    position: absolute;
    width: 1px;
}
</style>
