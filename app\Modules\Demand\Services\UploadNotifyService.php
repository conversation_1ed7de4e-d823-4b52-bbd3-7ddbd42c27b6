<?php

namespace App\Modules\Demand\Services;

use App\Modules\Demand\Models\Notification;

class UploadNotifyService
{

    public function updateUploadResponse($patchNo, $status, $fileOriginalName): void
    {
        // \Log::error('class :' . __CLASS__ . ' function :' . __FUNCTION__);
        // 用上傳編號抓取
        $notify = Notification::query()->where('payload->patc_no', $patchNo)
            ->where('payload->is_finished', 0)
            ->first();
        if (!$notify)
            return;

        $success = $notify->payload->get('success');
        $failed = $notify->payload->get('failed');

        // 根據傳來的狀態確認成功失敗，回寫到相對應組別
        if ($status) {
            array_push($success, $fileOriginalName);
            $notify->setAttribute('payload->success', $success);
            // $notify->forceFill(['payload->success' => $success]);
        } else {
            array_push($failed, $fileOriginalName);
            $notify->setAttribute('payload->failed', $failed);
            // $notify->forceFill(['payload->failed' => $failed]);
        }

        // 若數量對齊後，將user_id 回寫
        if (count($notify->payload->get('file_names')) == count($success) + count($failed)) {
            $notify->setAttribute('employee_id', $notify->payload->get('employee_id'))
                ->setAttribute('payload->is_finished', 1)
                ->setAttribute('payload->code', 200)
                ->setAttribute('payload->time', now()->toISOString());
            // $notify->forceFill([
            //     'payload->is_finished' => 1,
            //     'payload->code' => 200,
            //     'payload->time' => now()->toISOString(),
            // ]);
        }

        $notify->save();
    }


    public function updateUploadResponseUnauthorized($patchNo): void
    {
        \Log::error('class :' . __CLASS__ . ' function :' . __FUNCTION__);
        $notify = Notification::query()
            ->where('payload->patc_no', $patchNo)
            ->where('payload->is_finished', 0)
            ->first();
        if (!$notify) {
            return;
        }

        $notify->setAttribute('employee_id', $notify->payload->get('employee_id'))
            ->setAttribute('payload->code', 401)
            ->setAttribute('payload->msg', '無管理權限，無法上傳檔案!')
            ->setAttribute('payload->is_finished', 1)
            ->setAttribute('payload->time', now()->toISOString())
            ->save();
    }
}
