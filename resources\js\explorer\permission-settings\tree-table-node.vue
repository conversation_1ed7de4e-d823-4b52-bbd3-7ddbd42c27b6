<template>
  <div>
    <div
      v-if="data"
      class="border-b border-gray-200 w-full flex items-center justify-between"
    >
      <div
        class="pl-6 py-7 text-left font-bold text-explorerPrimary w-64 lg:w-auto lg:flex-grow lg:max-w-filename"
      >
        <div
          class="flex items-center truncate"
          :style="{ paddingLeft: padding + 'px' }"
        >
          <span class="truncate">{{ data.name }}</span>
          <button
            v-if="data.children"
            class="hover:bg-gray-200 rounded-full w-7 h-7 transition ml-2"
            @click="handleToggleExpand(data)"
          >
            <img
              v-if="data.isExpand"
              src="@images/icon/arrow-down.svg"
              class="w-6 mx-auto"
            />
            <img
              v-else
              src="@images/icon/arrow-right.svg"
              class="w-6 mx-auto"
            />
          </button>
        </div>
      </div>
      <div class="py-7 text-left font-bold text-explorerPrimary w-20 lg:w-56">
        <div
          v-if="data.admin?.length"
          class="flex flex-col content-start lg:flex-row lg:items-center lg:justify-start flex-wrap"
        >
          <span
            v-for="(member, index) in data.admin.slice(0, 5)"
            :key="member.id"
            >{{ member.name
            }}<template v-if="index < data.admin.slice(0, 5).length - 1"
              >、</template
            >
            <template v-else-if="index === 4">...</template>
          </span>
        </div>
        <div v-else>
          <span>-</span>
        </div>
      </div>
      <div class="py-7 text-left font-bold text-explorerPrimary w-20 lg:w-56">
        <div
          v-if="data.read?.length"
          class="flex flex-col content-start lg:flex-row lg:items-center lg:justify-start flex-wrap"
        >
          <span
            v-for="(member, index) in data.read.slice(0, 5)"
            :key="member.id"
            >{{ member.name
            }}<template v-if="index < data.read.slice(0, 5).length - 1"
              >、</template
            >
            <template v-else-if="index === 4">...</template>
          </span>
        </div>
        <div v-else>
          <span>-</span>
        </div>
      </div>
      <div class="py-7 text-left font-bold text-explorerPrimary w-20 lg:w-56">
        <div
          v-if="data.download?.length"
          class="flex flex-col content-start lg:flex-row lg:items-center lg:justify-start flex-wrap"
        >
          <span
            v-for="(member, index) in data.download.slice(0, 5)"
            :key="member.id"
            >{{ member.name
            }}<template v-if="index !== data.download.slice(0, 5).length - 1"
              >、</template
            >
            <template v-else-if="index === 4">...</template>
          </span>
        </div>
        <div v-else>
          <span>-</span>
        </div>
      </div>
      <div class="px-7 w-16">
        <div class="flex items-center justify-end">
          <div>
            <button
              v-if="data.parentId !== rootId"
              class="hover:bg-gray-200 rounded-full w-6 transition p-1"
              @click="handleInheritPermissions(data.explorerId, $event)"
            >
              <img
                src="@images/icon/inherit.svg"
                alt="inherit"
                class="mx-auto w-full"
              />
            </button>
          </div>
          <div>
            <button
              class="hover:bg-gray-200 rounded-full w-6 transition p-1 ml-6"
              @click="togglePermissionSettings(true, data.explorerId)"
            >
              <img
                src="@images/icon/edit.svg"
                alt="edit"
                class="mx-auto w-full"
              />
            </button>
          </div>
        </div>
      </div>
    </div>

    <template v-for="item in data.children">
      <TreeTableNode
        v-if="data && data.isExpand"
        :key="item.id"
        :data="item"
        :rootId="rootId"
        :padding="computedPadding"
        @togglePermission="togglePermissionSettings"
      />
    </template>
  </div>
</template>

<script>
import { inheritPermission } from "@/axios/explorerApi.js";

export default {
  name: "TreeTableNode",
  props: {
    data: { type: Object, required: true },
    padding: { type: Number, default: 0 },
    rootId: { required: true },
  },
  inject: ["fetchFolderPermissionData"],
  data() {
    return {
      selectedFolder: null,
    };
  },
  computed: {
    computedPadding() {
      return this.padding + 12;
    },
  },
  methods: {
    handleToggleExpand(data) {
      data.isExpand = !data.isExpand;
    },
    handleInheritPermissions(folderId, event) {
      this.$refs.confirmPopup.visible = true;
      this.$refs.confirmPopup.target = event.currentTarget;
      this.$refs.confirmPopup.confirmation = {
        message: "是否繼承上層資料夾權限？",
        acceptLabel: "確定",
        rejectLabel: "取消",
        rejectClass: "font-bold",
        rejectClass:
          "border border-explorerPrimary text-explorerPrimary font-bold bg-white hover:bg-gray-200 transition",
        accept: async () => {
          try {
            await inheritPermission(folderId);
            this.$refs.toast.add({
              severity: "success",
              summary: "繼承權限成功",
              life: 3000,
            });
            this.fetchFolderPermissionData();
          } catch (error) {
            this.$refs.toast.add({
              severity: "error",
              summary: "繼承權限失敗",
              life: 3000,
            });
          }
        },
      };
    },
    togglePermissionSettings(isShow, folderId) {
      this.$emit("togglePermission", isShow, folderId);
    },
  },
};
</script>
