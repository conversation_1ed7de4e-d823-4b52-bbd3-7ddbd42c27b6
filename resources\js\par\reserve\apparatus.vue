<template>
    <div>
        <div v-show="$root.action == 0">
            <SearchBar
                :begin_time="begin_time"
                :end_time="end_time"
                :selected_date="form.date"
                :selected_begin="selected_begin"
                :selected_end="selected_end"
                :searchInput="{ app_id, date_search }"
                :todayMidnight="todayMidnight"
                @disableButton="(val) => (if_search = val)"
                @selectedId="(val) => (app_id = val)"
                @selectedDate="
                    (val) => {
                        form.date = val;
                        selected_begin = null;
                        selected_end = null;
                    }
                "
                @updateSelectedBegin="(val) => (selected_begin = val)"
                @updateSelectedEnd="(val) => (selected_end = val)"
                @search="
                    fetchDataAndCallReserved(app_id, form.date);
                    displayReservesInfo = true;
                "
            />
            <hr class="my-6 text-gray-200" />
            <!-- 未搜尋前的圖片 -->
            <div
                :style="{
                    height: `calc(100vh - ${search_photo_y}px )`,
                }"
                ref="search_photo"
                class="flex flex-col justify-center items-center"
                v-if="!displayReservesInfo"
            >
                <img src="@images/page_state/search.png" alt="" />
                <p class="text-center mt-4">選擇您要查詢的日期與時間</p>
            </div>
            <!-- 為了RWD -->
            <div class="flex flex-wrap m-4" v-if="displayReservesInfo">
                <div
                    v-for="(list, listIndex) in data_lists"
                    :key="list.id"
                    class="w-full p-4 box-border lg:w-1/2"
                >
                    <ReserveItem
                        ref="reserveItem"
                        :isCatalog="true"
                        :data="list"
                        :dataIndex="listIndex"
                        :displayReserves="1"
                        :selectedBegin="selected_begin"
                        :selectedEnd="selected_end"
                        @selectedDuration="
                            (val) =>
                                (form.time_ids = val.res_id) &&
                                (form.pa_id = val.list_id) &&
                                (form.name = val.list_name)
                        "
                        @formCheckedTime="formCheckedTime()"
                    />
                </div>
            </div>
        </div>
        <!-- 表單 -->
        <div v-if="$root.action == 1">
            <div class="text-center relative">
                <span
                    class="absolute left-0 mt-0.5 cursor-pointer"
                    @click="
                        $root.action = 0;
                        fetchDataAndCallReserved(app_id, date);
                    "
                >
                    <i class="fas fa-arrow-left"></i>
                </span>
                <h2 class="text-2xl font-semibold">設備預約</h2>
            </div>
            <ReserveItem
                :data="
                    edit_data == undefined ? data_lists[list_index] : edit_item
                "
                :dataIndex="list_index"
                :displayReserves="0"
                class="my-12"
            />
            <div class="demandList">
                <div class="block md:flex py-8 border-b border-gray-200">
                    <span class="my-auto mr-8">
                        預約日期<span class="text-red-500">*</span>
                    </span>
                    <div>
                        <Calendar
                            :class="{ 'p-invalid': formInvalid.date }"
                            placeholder="選擇預約日期"
                            v-model="form.date"
                            dateFormat="yy-mm-dd"
                            :manualInput="false"
                        />
                        <small
                            v-if="formInvalid.date"
                            class="text-red-500 block"
                            >請選擇預約日期</small
                        >
                    </div>
                </div>
                <div class="block md:flex py-8 border-b border-gray-200">
                    <span class="my-auto mr-8">
                        預約數量<span class="text-red-500">*</span>
                    </span>
                    <div class="flex justify-between w-40">
                        <div
                            @click="
                                form.amount > 1
                                    ? form.amount--
                                    : (form.amount = 1)
                            "
                            class="w-8 h-8 rounded-full border-solid border relative cursor-pointer"
                        >
                            <i class="absolute h-0 py-0.5 px-1 select-none"
                                >－</i
                            >
                        </div>
                        <span class="my-auto">{{ form.amount }}</span>
                        <div
                            @click="
                                form.amount <
                                data_lists[list_index].total_amount
                                    ? form.amount++
                                    : ''
                            "
                            class="w-8 h-8 rounded-full border-solid border relative cursor-pointer"
                        >
                            <i
                                class="absolute h-0 py-0.5 px-1.5 select-none not-italic"
                                >＋</i
                            >
                        </div>
                    </div>
                </div>
                <div class="block md:flex py-8 border-b border-gray-200">
                    <span class="my-auto mr-8">
                        預約時段<span class="text-red-500">*</span>
                    </span>
                    <div class="flex">
                        <div class="pr-6">
                            <Dropdown
                                class="lg:w-44 xl:w-48 rounded-md shadow"
                                optionLabel="time"
                                optionValue="id"
                                data-key="id"
                                placeholder="請選擇開始時間"
                                :options="begin_time"
                                v-model="selected_begin"
                                optionDisabled="disabled"
                                @input="
                                    formInvalid.time_ids = false;
                                    timeValidate();
                                "
                            />
                        </div>
                        <p class="my-auto mr-8">至</p>
                        <div class="pr-6">
                            <Dropdown
                                class="lg:w-44 xl:w-48 rounded-md shadow"
                                optionLabel="time"
                                optionValue="id"
                                data-key="id"
                                placeholder="請選擇結束時間"
                                :options="end_time"
                                v-model="selected_end"
                                optionDisabled="disabled"
                                @input="
                                    formInvalid.time_ids = false;
                                    timeValidate();
                                "
                            />
                        </div>
                        <small
                            v-if="formInvalid.time_ids"
                            class="text-red-500 block"
                            >請選擇預約時段</small
                        >
                    </div>
                </div>
                <div class="block md:flex py-8 border-b border-gray-200">
                    <span class="my-auto mr-8">
                        預約事由<span class="text-red-500">*</span>
                    </span>
                    <div>
                        <Textarea
                            :class="{ 'p-invalid': formInvalid.reason }"
                            v-model="form.reason"
                            placeholder="輸入預約事由"
                            @input="formInvalid.reason = false"
                        />
                        <small
                            v-if="formInvalid.reason"
                            class="text-red-500 block"
                            >請填寫預約事由</small
                        >
                    </div>
                </div>
                <div class="block md:flex py-8">
                    <span class="my-auto mr-16"> 備註 </span>
                    <div>
                        <Textarea
                            v-model="form.remark"
                            class="ml-1"
                            placeholder="輸入備註"
                        />
                    </div>
                </div>
                <div
                    class="buttons mt-4 w-full flex justify-between md:justify-end"
                >
                    <Button
                        @click="
                            $root.action = 0;
                            fetchDataAndCallReserved(app_id, date);
                        "
                        label="取消"
                        class="p-button-outlined p-button-secondary w-28 mr-5"
                    />
                    <Button
                        :disabled="$root.loading"
                        @click="
                            reserve();
                            displayReservesInfo = false;
                        "
                        :label="edit_data == undefined ? '預約' : '再次預約'"
                        class="w-28"
                    />
                </div>
            </div>
        </div>
        <Toast ref="toast" position="top-center" />
        <!-- 預約時段重複警示彈窗 -->
        <Dialog
            :visible.sync="displayReservedWarning"
            :modal="true"
            :dismissableMask="true"
            :closable="false"
            :containerStyle="{
                width: '25vw',
                textAlign: 'center',
            }"
        >
            <img
                class="mx-auto mb-4"
                src="@images/popup_state/warning.png"
                alt=""
            />
            <p class="mb-2">以下時段已被預約</p>
            <p class="mb-12">{{ reserved_info }}</p>
            <Button
                label="我知道了"
                @click="displayReservedWarning = false"
            ></Button>
        </Dialog>
        <div v-if="$root.loading">
            <div
                class="fixed top-1/2 left-1/2 transform -translate-y-1/2 -translate-x-1/2 z-50"
            >
                <ProgressSpinner />
            </div>
        </div>
    </div>
</template>
<script>
import axios from "axios";
import Button from "primevue/button";
import Calendar from "primevue/calendar";
import Dialog from "primevue/dialog";
import Dropdown from "primevue/dropdown";
import MultiSelect from "primevue/multiselect";
import InputText from "primevue/inputtext";
import InputNumber from "primevue/inputnumber";
import Textarea from "primevue/textarea";
import Toast from "primevue/toast";
import SearchBar from "@/par/common/search-bar";
import ReserveItem from "@/par/common/reserve-item";
import ProgressSpinner from "primevue/progressspinner";
import reserveDuration from "@/share/ReserveDuration";

export default {
    components: {
        Button,
        Calendar,
        Dropdown,
        MultiSelect,
        InputText,
        InputNumber,
        Textarea,
        Toast,
        SearchBar,
        ReserveItem,
        ProgressSpinner,
        Dialog,
    },
    props: ["edit_data"],
    data() {
        return {
            data_lists: [],
            list_index: 0,
            date: new Date(),
            date_search: null,
            app_id: null,
            selected_begin: null,
            selected_end: null,
            if_search: true,
            displayReservesInfo: null,
            form: {
                pa_id: null,
                name: "",
                date: new Date(),
                amount: 1,
                time_ids: [],
                reason: "",
                remark: "",
            },
            formInvalid: {
                date: false,
                time_ids: false,
                reason: false,
            },
            url: "/api/par/reserve/apparatus",
            edit_item: {},
            edit_reserves: [], // 搜尋列及預約表單資料
            begin_time: new reserveDuration().durations,
            end_time: new reserveDuration().durations,
            search_photo_y: null,
            displayReservedWarning: false,
            reserved_info: [],
            todayMidnight: new Date(),
        };
    },
    created() {},
    mounted() {
        this.resetDateTime(this.form.date);
        this.resetDateTime(this.todayMidnight);
        this.fetchList(null, this.date);
        this.search_photo_y =
            this.$refs.search_photo.getBoundingClientRect().top;
    },
    watch: {
        "$root.action": function (newValue) {
            if (newValue == 1) {
                this.form.date = this.date_search;
            } else {
                this.form.date = null;
                (this.form.amount = 1),
                    (this.form.time_ids = []),
                    (this.form.reason = ""),
                    (this.form.remark = "");
            }
        },
        selected_begin(newVal) {
            this.end_time.forEach((item) => {
                item.disabled = false;
                if (item.id <= newVal && newVal != null) {
                    item.disabled = true;
                }
            });
            this.disabledTime(this.begin_time);

            this.formAddCheckedTime();
        },
        selected_end(newVal) {
            this.begin_time.forEach((item) => {
                item.disabled = false;
                if (item.id >= newVal && newVal != null) {
                    item.disabled = true;
                }
            });
            this.disabledTime(this.end_time);

            this.formAddCheckedTime();
        },
    },
    methods: {
        async fetchList(id, date) {
            try {
                this.data_lists = [];
                const response = await axios.get(this.url, {
                    params: {
                        id: id === 0 ? null : id,
                        date: date,
                    },
                });

                this.data_lists = response.data;
                this.date_search = this.date;
                if (this.edit_data !== undefined) {
                    this.importValue();
                }
            } catch (error) {
                console.error(error);
            }
        },
        disabledTime(timeDuration) {
            if (new Date() < this.form.date) return;

            let currentTime = new Date().getHours();
            timeDuration.forEach((item) => {
                if (item.time.split(":")[0] < currentTime) {
                    item.disabled = true;
                }
            });
        },
        validate() {
            let error = false;
            this.formInvalid.date = this.form.date === null;
            this.formInvalid.time_ids = this.form.time_ids.length == 0;
            this.formInvalid.reason = this.form.reason === "";
            Object.values(this.formInvalid).forEach((item) => {
                if (item == true) {
                    error = true;
                }
            });
            if (this.reserved_info.length > 0) {
                this.displayReservedWarning = true;
                error = true;
            }
            return error;
            return error;
        },
        // 處理及時預約時段驗證
        timeValidate() {
            this.reserved_info = [];
            let item = this.data_lists[this.list_index].reserves;
            for (let i = 0; i < item.length; i++) {
                if (
                    item[i].disabled &&
                    this.selected_begin < item[i].id &&
                    item[i].id < this.selected_end
                ) {
                    this.reserved_info.push(item[i].duration);
                }
            }
            // 預約時段合併
            let combined_coount = 0;
            let reserved_info_length = this.reserved_info.length;
            for (let i = 0; i < reserved_info_length - 1; i++) {
                if (
                    this.reserved_info[combined_coount].slice(6) ===
                    this.reserved_info[combined_coount + 1].slice(0, 5)
                ) {
                    let combined_duration =
                        this.reserved_info[combined_coount].slice(0, 6) +
                        this.reserved_info[combined_coount + 1].slice(6);
                    this.reserved_info.splice(
                        combined_coount,
                        2,
                        combined_duration
                    );
                    combined_coount--;
                }
                combined_coount++;
            }
            this.reserved_info = this.reserved_info.join(" 、 ");
            if (this.reserved_info.length > 0) {
                this.displayReservedWarning = true;
            }
        },
        reserve() {
            if (this.validate() == true) {
                return;
            }
            this.$root.loading = true;
            axios
                .post(
                    this.edit_data == undefined
                        ? this.url
                        : "/api/par/reserve/again",
                    {
                        id: this.$root.reserve_id,
                        form: this.form,
                    }
                )
                .then((response) => {
                    if (response.data.state) {
                        this.$refs.toast.add({
                            severity: "success",
                            summary: "預約成功",
                            life: 3000,
                        });
                        this.$root.action = 0;
                        this.fetchList(null, this.date_search);
                    } else
                        this.$refs.toast.add({
                            severity: "error",
                            summary: "下列時段設備剩餘數量不足",
                            detail: response.data.error,
                            life: 3000,
                        });
                })
                .catch((error) => {
                    console.error(error);
                    this.$refs.toast.add({
                        severity: "error",
                        summary: "預約失敗",
                        life: 3000,
                    });
                })
                .finally(() => {
                    this.$root.loading = false;
                    // 清空表單暫存
                    this.selected_begin = null;
                    this.selected_end = null;
                });
        },
        // 修改預約匯入資料
        importValue() {
            this.$set(this.form, "type", "apparatus");
            this.form.pa_id = this.edit_data.list.pa_id;
            this.form.name = this.edit_data.list.name;
            this.selected_begin = this.edit_data.times[0];
            this.selected_end =
                this.edit_data.times[this.edit_data.times.length - 1] + 1;
            // this.form.time_ids = this.edit_data.times;
            this.form.remark = this.edit_data.remark;
            this.form.reason = this.edit_data.reason;
            this.form.amount = this.edit_data.amount;
            this.form.date = new Date(this.edit_data.date);
            this.list_index = this.data_lists.findIndex(
                (list) => list.id == this.edit_data.list.pa_id
            );
            this.edit_item = this.edit_data.list;
            this.edit_reserves = this.data_lists[this.list_index].reserves;
            this.edit_reserves.forEach((reserve) => {
                delete reserve.disabled;
            });
        },
        async fetchDataAndCallReserved(app_id, date) {
            await this.fetchList(app_id, date);
            this.callReservedMethod();
        },

        callReservedMethod() {
            if (this.$refs.reserveItem)
                this.$refs.reserveItem.forEach((item) => {
                    item.isReserved();
                });
        },
        //處理表單預約時段
        formCheckedTime() {
            if (this.list_index != null && this.data_lists[this.list_index]) {
                this.data_lists[this.list_index].reserves.forEach((item, i) => {
                    if (item.disabled == true) {
                        this.begin_time[i].disabled = true;
                        this.end_time[i + 1].disabled = true;
                    }
                });
            }
        },
        /**
         * 將所選時段新增至表單
         */
        formAddCheckedTime() {
            this.form.time_ids = [];
            let bg_time = this.selected_begin;
            let count = this.selected_end - this.selected_begin;
            for (let i = 0; i < count; i++) {
                // if (this.begin_time[bg_time].disabled != true) {
                this.form.time_ids.push(bg_time);
                // }
                bg_time++;
            }
        },
        // 將該日期的時間重置
        resetDateTime(date) {
            date.setHours(0);
            date.setMinutes(0);
            date.setSeconds(0);
            date.setMilliseconds(0);
        },
    },
};
</script>
