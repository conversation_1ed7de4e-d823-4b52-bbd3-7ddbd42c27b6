<?php

use Illuminate\Support\Arr;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

// 20230726 Ben - 臨時清理暫存
Route::get('/clean', function () {
    return "<html> <body> <h1>手動暫存清除功能</h1> <script> let ls = window.localStorage; if(ls.length > 0) { let keys = []; for (let i = 0, len = ls.length; i < len; ++i) { keys.push(ls.key(i)); } document.write('檢測到的暫存項目如下：<ul>'); for (let i = 0, len = keys.length; i < len; ++i) { document.write('<li>' + keys[i] + ': ' + ls.getItem(keys[i]) + '</li>'); } document.write('</ul>'); } else { document.write('沒有檢測到可以清除的暫存。'); } function confirmed() { window.localStorage.clear(); alert('清除完成！'); window.location = '/'; } </script> <hr /> <h3>注意：此步驟無法還原！</h3> <button onclick='javascript:confirmed()'>是的，我確定要清除暫存</button> </body></html>";
});

Route::get('/', function () {
    if (config('app.debug') == true) {
        \Session::flush();
        return view('_dev.logout');
    } else {
        header('Access-Control-Allow-Origin: *');
        header('Access-Control-Allow-Methods: POST, GET, OPTIONS, PUT, DELETE');
        header('Access-Control-Allow-Headers: Content-Type, X-Auth-Token, Origin, Authorization');
        header('Location: https://sso.funcsync.com/richhonour/login');
    }
});
Route::get('logout', function (Request $request) {
    Session::flush();
    return redirect('/');
});
Route::prefix('sso')->namespace('\App\Modules\SSO\Controllers')->group(function () {
    Route::get('login', 'AuthController@SSOLogin');
    Route::get('hrap', 'SSOController@hrap');
    Route::get('acc', 'SSOController@acc')->name('acc');
    Route::get('pmap', 'SSOController@pmap')->name('pmap');
});
Route::prefix('demand')->namespace('\App\Modules\Demand\Controllers')->group(function () {

    //測試Session登入口
    Route::get('/', 'HomeController@index');
    Route::middleware('check.login')->group(function () {

        Route::middleware('check.auth:demand')->group(function () {
            Route::view('/setting/demand', 'demand.setting.dem_setting');
            Route::view('/setting/database', 'demand.setting.database_setting');
            Route::view('/setting/demPermission', 'demand.setting.permission_setting');
            Route::get('/setting/notify', 'NotifySettingsController@index');
        });
        Route::view('/query', 'demand.query.query')->middleware('check.auth:demand_query');
        Route::view('/submit', 'demand.submit.submit', ['tab' => 0]);
        Route::view('/list', 'demand.submit.submit', ['tab' => 1])->name('list');
        Route::view('/history', 'demand.submit.submit', ['tab' => 2]);
        Route::view('/signing', 'demand.signing.signing');
    });
});


Route::prefix('par')->namespace('\App\Modules\par\Controllers')->group(function () {
    Route::view('/setting', 'par.setting')->middleware('check.auth:par');
    Route::view('/reserve/apparatus', 'par.reserve');
    Route::view('/reserve/postulate', 'par.reserve');
    Route::view('/myReserves', 'par.my-reserves');
    Route::view('/parPermission', 'par.permission-setting');
});

// 文件管理
Route::prefix('explorer')->namespace('\App\Modules\explorer\Controllers')->group(function () {
    Route::get('/', [\App\Modules\explorer\Controllers\ExplorerController::class, 'index']);
    Route::get('/permission', [\App\Modules\explorer\Controllers\AuthController::class, 'index']);
});
