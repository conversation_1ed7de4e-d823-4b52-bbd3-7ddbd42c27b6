<template>
  <div
    style="min-height: calc(100vh - 148px)"
    class="w-full h-full bg-white rounded-lg overflow-x-auto whitespace-nowrap md:whitespace-normal"
  >
    <PermissionSettings
      :isShow="isShowDialog"
      :folderId="permissionFolderId"
      @togglePermission="togglePermissionSettings"
    />
    <ConfirmPopup ref="confirmPopup" style="z-index: 9999"></ConfirmPopup>
    <Toast ref="toast" position="top-center" :baseZIndex="9999" />
    <div class="w-full h-full min-w-table">
      <div
        class="text-xl font-bold text-left text-explorerPrimary border-b border-gray-200 px-6 py-7"
      >
        {{ tableTitle }}
      </div>

      <div
        class="text-left font-bold text-explorerPrimary border-b border-gray-200 bg-gray-50"
      >
        <div class="flex items-center justify-between">
          <div
            v-for="header in tableHeader"
            :key="header"
            class="first:pl-6 last:px-7 py-7 last:mr-0 first:w-64 w-20 last:w-16 lg:w-56 lg:first:w-auto lg:first:flex-grow lg:max-w-filename"
          >
            {{ header }}
          </div>
        </div>
      </div>
      <div v-if="tableDataTree">
        <template v-for="data in tableDataTree">
          <treeTableNode
            v-if="data"
            :data="data"
            :rootId="rootId"
            :key="data.id"
            :padding="0"
            @togglePermission="togglePermissionSettings"
            @getPermissionData="fetchTableData"
          />
        </template>
      </div>
    </div>
  </div>
</template>

<script>
import ConfirmPopup from "primevue/confirmpopup";
import Toast from "primevue/toast";
import TreeTableNode from "./tree-table-node.vue";
import PermissionSettings from "../common/permission-settings.vue";
import { explorerMixin } from "@/mixins/explorerMixin";
import { getAllFolderPermissions } from "@/axios/explorerApi.js";

export default {
  name: "ManagementPermissions",
  components: {
    ConfirmPopup,
    Toast,
    TreeTableNode,
    PermissionSettings,
  },
  data() {
    return {
      isShowDialog: false,
      tableTitle: "文件權限管理",
      tableHeader: ["資料夾名稱", "管理", "瀏覽", "下載", ""],
      tableData: [],
      tableDataTree: [],
      rootId: null,
      permissionFolderId: null,
      expandFolderIds: [],
    };
  },
  mixins: [explorerMixin],
  methods: {
    async fetchTableData() {
      if (this.tableData.length) {
        this.recordFolderExpand();
      }
      try {
        const data = await getAllFolderPermissions();
        this.tableData = data;
        this.tableData.forEach((data) => {
          if (!data.parentId) {
            this.rootId = data.explorerId;
          }
        });
        this.addAttributes();
        // 將表格資料整理成樹狀結構
        this.tableDataTree = this.formatToTree(this.tableData)[0]?.children;
      } catch (error) {
        this.$refs.toast.add({
          severity: "error",
          summary: error.message,
          life: 3000,
        });
      }
    },
    togglePermissionSettings(isShow, folderId) {
      if (!folderId) return;
      this.permissionFolderId = folderId;
      this.isShowDialog = isShow;
      if (!this.isShowDialog) this.fetchTableData();
    },
    addAttributes() {
      // 將表格資料新增 isExpand 屬性
      if (this.expandFolderIds.length) {
        this.tableData.forEach((data) => {
          if (this.expandFolderIds.includes(data.explorerId)) {
            this.$set(data, "isExpand", true);
          } else {
            this.$set(data, "isExpand", false);
          }
        });
      } else {
        this.tableData.forEach((data) => {
          this.$set(data, "isExpand", false);
        });
      }
    },
    recordFolderExpand() {
      // 紀錄資料夾展開狀態
      const newExpandFolderIds = [];
      this.tableData.forEach((data) => {
        if (data.isExpand) newExpandFolderIds.push(data.explorerId);
      });
      this.expandFolderIds = newExpandFolderIds;
    },
  },
  created() {
    this.fetchTableData();
  },
  provide() {
    return {
      fetchFolderPermissionData: this.fetchTableData,
    };
  },
};
</script>
<style>
.p-confirm-popup {
  box-shadow: none !important;
  border: 1px solid #d0d5dd !important;
}

.p-confirm-popup::before,
.p-confirm-popup::after {
  content: none;
}

.p-button-label {
  font-weight: bold !important;
}

.p-confirm-popup-reject:hover {
  background: #e5e7eb !important;
  color: #1d2939 !important;
  border: 1px solid #1d2939 !important;
}
</style>
