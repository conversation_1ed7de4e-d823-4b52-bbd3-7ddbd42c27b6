<?php

namespace App\Modules\Demand\Controllers;


use Illuminate\Http\Request;
use Illuminate\Support\Facades\Session;
use App\Http\Controllers\Controller;
use App\Modules\Demand\Models\Employee;
use App\Modules\Demand\Models\OrgUnit;
use Illuminate\Support\Facades\Log;
class RoleController extends Controller
{
    public function findLevleManager($userId, $level)
    {
        $employee = Employee::where('id', $userId)
            ->with('orgs')
            ->first();

        $org = $employee->orgs[0];
        $manager = Employee::where('id', $org->payload->get('manager'))
            ->with('orgs')
            ->first();
        if (!empty($manager) && $manager->id != $employee->id) {
            return $this->findParent($org, $manager, $level);
        } else {
            return $this->findParent($org, null, $level);
        }
    }

    public function findParent($org, $manager, $level)
    {
        if (empty($org))
            return null;
        if (!empty($manager) && $manager->payload->get('rank') >= $level) {
            return $manager->id;
        } else {
            $orgs = OrgUnit::where('id', $org->payload->get('parent'))->first();
            $managerId = isset($orgs) ? $orgs->payload->get('manager') : null;
            $managers = Employee::where('id', $managerId)
                ->with('orgs')
                ->first();
            return $this->findParent($orgs, $managers, $level);
        }
    }
}
