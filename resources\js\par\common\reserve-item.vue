<template>
    <div>
        <div
            class="demandList flex flex-wrap justify-between"
            :class="displayReserves ? '' : 'md:justify-start'"
        >
            <Galleria
                :value="data.image_url"
                :numVisible="5"
                :circular="true"
                style="max-width: 208px"
                :showItemNavigators="true"
                :showThumbnails="false"
                containerStyle="max-width: 208px;"
            >
                <template #item="slotProps">
                    <img
                        :src="slotProps.item.file_show"
                        style="width: 100%; display: block"
                    />
                </template>
                <template #thumbnail="slotProps">
                    <img
                        :src="slotProps.item.file_show"
                        style="display: block"
                    />
                </template>
            </Galleria>
            <div class="ml-3 w-full md:w-1/2 grow">
                <div class="md:flex md:justify-between">
                    <h2 class="text-2xl font-semibold m-2">
                        {{ data.name }}
                    </h2>
                    <div class="text-right">
                        <span
                            v-if="displayReserves"
                            class="py-1.5 border text-sm px-2 grow-0 text-center rounded-md"
                            :class="
                                is_reserved
                                    ? 'bg-gray-50 text-gray-400 border-gray-400'
                                    : 'bg-green-50 text-green-400 border-green-300'
                            "
                        >
                            {{ is_reserved ? "已被預約" : "可預約" }}
                        </span>
                        <p
                            v-if="is_reserved"
                            @click="displayReservedInfo = true"
                            class="mt-3 underline cursor-pointer"
                        >
                            查看預約狀況
                        </p>
                    </div>
                </div>

                <div v-if="$root.reserve == 0">
                    <p class="text-sm text-gray-400 my-4">
                        數量&emsp;|&emsp;{{ data.total_amount }}
                    </p>
                    <p class="text-sm text-gray-400 my-4">
                        設備管理人&emsp;|&emsp;{{
                            changeNotified(data.notifieds, isCatalog)
                        }}
                    </p>
                </div>
                <div v-else>
                    <p class="my-4 text-gray-400">
                        <i class="fa-solid fa-user-group"></i>&emsp;
                        {{ data.people }}人
                    </p>
                    <p class="my-4 text-gray-400">
                        <i class="fa-solid fa-location-dot"></i>&emsp;&ensp;
                        {{ data.place }}
                    </p>
                </div>
                <div class="block md:flex md:justify-between">
                    <p class="text-sm text-gray-400 my-4 break-all">
                        <span v-if="$root.reserve == 0">設備</span
                        ><span v-else>設施</span>說明&emsp;|&emsp;{{
                            data.remark
                        }}
                    </p>
                    <Button
                        v-if="displayReserves"
                        @click="selectedDuration(selectedBegin, selectedEnd)"
                        label="預約"
                        class="whitespace-nowrap text-black bg-white border-black"
                    ></Button>
                </div>
            </div>

            <Dialog
                header="預約資訊"
                :visible.sync="displayReservedInfo"
                :modal="true"
                :dismissableMask="true"
            >
                <ul class="">
                    <li
                        v-for="res in reserved_info"
                        :key="res.id"
                        class="p-4 flex-grow"
                        style="flex-basis: 50%"
                    >
                        <span>{{ res.duration }}</span>
                        <span class="text-gray-300">&emsp;|&emsp; </span>
                        <span>{{ res.name }}</span>
                        <span class="text-gray-300">&emsp;|&emsp;</span>
                        <span>{{ res.reason }}</span>
                    </li>
                </ul>
            </Dialog>
        </div>
    </div>
</template>
<script>
import Button from "primevue/button";
import Dropdown from "primevue/dropdown";
import Calendar from "primevue/calendar";
import Galleria from "primevue/galleria";
import Dialog from "primevue/dialog";
export default {
    components: {
        Button,
        Dropdown,
        Calendar,
        Galleria,
        Dialog,
    },
    props: [
        "data",
        "dataIndex",
        "displayReserves",
        "selectedBegin",
        "selectedEnd",
        "isCatalog",
    ],
    mounted() {
        // this.$set(this.data, 'apparatus_type', ['工程用', '廁所用']);
    },
    data() {
        return {
            is_reserved: false,
            displayReservedInfo: false,
            reserved_info: [],
        };
    },
    methods: {
        selectedDuration(selected_begin, selected_end) {
            let list_id = this.data.id;
            let list_name = this.data.name;
            let res_id = [];
            for (let i = 0; i < selected_end - selected_begin; i++) {
                res_id.push(selected_begin + i);
            }
            this.$emit("selectedDuration", { res_id, list_id, list_name });
            this.$parent.list_index = this.dataIndex;
            this.$root.action = 1;
        },
        isReserved() {
            this.is_reserved = false;
            this.reserved_info = [];
            let times = this.selectedEnd - this.selectedBegin;
            let begin = this.selectedBegin;
            while (times > 0) {
                if (this.data.reserves[begin].disabled) {
                    // 處理預約顯示轉換
                    this.is_reserved = true;
                    // 處理預約資訊
                    for (
                        let i = 0;
                        i < this.data.reserves[begin].detail.length;
                        i++
                    ) {
                        let reserved_information = {
                            id: this.data.reserves[begin].id,
                            name: this.data.reserves[begin].detail[i]
                                .subscribers,
                            reason:
                                this.data.reserves[begin].detail[i].reason
                                    .length > 10
                                    ? this.data.reserves[begin].detail[
                                          i
                                      ].reason.substring(0, 10) + "..."
                                    : this.data.reserves[begin].detail[i]
                                          .reason,
                            duration: this.data.reserves[begin].duration,
                        };
                        this.reserved_info.push(reserved_information);
                    }
                }
                times--;
                begin++;
            }
            // 同人同預約事由 => 時段合併
            let combined_coount = 0;
            let reserve_info_length = this.reserved_info.length;
            for (let i = 0; i < reserve_info_length - 1; i++) {
                if (
                    this.reserved_info[combined_coount].name ===
                        this.reserved_info[combined_coount + 1].name &&
                    this.reserved_info[combined_coount].id + 1 ===
                        this.reserved_info[combined_coount + 1].id &&
                    this.reserved_info[combined_coount].reason ===
                        this.reserved_info[combined_coount + 1].reason
                ) {
                    let combined_duration =
                        this.reserved_info[combined_coount].duration.slice(
                            0,
                            6
                        ) +
                        this.reserved_info[combined_coount + 1].duration.slice(
                            6
                        );
                    let combined_info = {
                        id: this.reserved_info[combined_coount + 1].id,
                        name: this.reserved_info[combined_coount].name,
                        reason: this.reserved_info[combined_coount].reason,
                        duration: combined_duration,
                    };
                    this.reserved_info.splice(
                        combined_coount,
                        2,
                        combined_info
                    );
                    combined_coount--;
                }
                combined_coount++;
            }
        },
        // 處理封面設備管理人顯示
        changeNotified(notifieds, isCatalog) {
            if (notifieds && notifieds.length > 1 && isCatalog) {
                return notifieds[0].org + " " + notifieds[0].name + " ...";
            } else {
                return notifieds
                    ? notifieds
                          .map((item) => item.org + " " + item.name)
                          .join("、")
                    : [];
            }
        },
    },
};
</script>
<style lang="css">
.p-galleria {
    width: 208px;
}
</style>
