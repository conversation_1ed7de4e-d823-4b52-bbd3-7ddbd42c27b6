{"name": "fdmc-asap", "version": "1.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "vite build"}, "devDependencies": {"@vitejs/plugin-vue2": "^2.3.1", "autoprefixer": "^9.8.8", "axios": "^1.6.0", "cross-env": "^5.1", "laravel-vite-plugin": "^1.0.5", "lodash": "^4.17.13", "postcss": "^7.0.39", "resolve-url-loader": "^2.3.1", "sass": "^1.15.2", "sass-loader": "^7.1.0", "tailwindcss": "npm:@tailwindcss/postcss7-compat@^2.2.17", "terser": "^5.36.0", "vite": "^5.4.10", "vue": "^2.7.16", "vue-template-compiler": "^2.6.14"}, "dependencies": {"@fortawesome/fontawesome-free": "^6.6.0", "docx-preview": "^0.1.20", "nanoid": "^4.0.2", "pdfjs-dist": "2.4.456", "primeicons": "^5.0.0", "primevue": "^2.10.2", "quill": "^1.3.7", "vue-simple-alert": "^1.1.1", "vue2-editor": "^2.10.3", "vuedraggable": "^2.24.3"}}