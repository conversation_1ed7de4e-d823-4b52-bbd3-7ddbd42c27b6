<template>
    <div>
        <!-- 申請列表 -->
        <div v-if="action == 0">
            <span
                v-if="'action' in $parent"
                @click="$parent.action = $parent.action - 1"
                class="cursor-pointer my-auto"
            >
                <i class="fas fa-arrow-left"></i>
            </span>
            <div v-if="'action' in $parent" class="text-center">
                <span class="text-xl font-semibold">
                    申請子單 (僅顯示有設定經辦人的單子)</span
                >
            </div>
            <div v-for="(item, item_index) in dataList" :key="item_index">
                <div class="demandList">
                    <div class="flex">
                        <p class="mr-4 my-auto font-bold">
                            {{ item.name }}
                        </p>
                    </div>
                </div>
                <div class="my-10 flex flex-wrap">
                    <div
                        v-for="(itemList, itemList_index) in item.layouts"
                        :key="itemList_index"
                        class="flex mr-10 pb-10"
                    >
                        <Button
                            :title="itemList.name"
                            @click="getLayout(itemList.id)"
                            class=""
                        >
                            <p class="w-36 truncate">
                                {{ itemList.name }}
                            </p>
                        </Button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 申請內頁 -->
        <div v-if="action == 1">
            <div class="flex items-center justify-between mb-4">
                <div class="w-32">
                    <Button
                        @click="
                            layout_id !== undefined
                                ? ($parent.action = $parent.action - 1)
                                : (action = action - 1);
                            layout_id !== undefined
                                ? ($parent.list.forms = JSON.parse(
                                      $parent.form_list
                                  ))
                                : (forms = []);
                            importOptions = [];
                            flow_sign_role = null;
                            choose = null;
                            counterRoles = [];
                            displayCounterRoles = [];
                        "
                        class="nowrap-button p-button-outlined h-12 p-button-secondary flex gap-2"
                    >
                        <i class="fas fa-arrow-left"></i>
                        <span>返回</span>
                    </Button>
                </div>
                <span class="text-xl font-semibold"> {{ name }} </span>
                <div class="flex justify-end w-32">
                    <div @click="displayModal1 = true" v-if="canImport">
                        <Button
                            label="帶入需求單"
                            class="p-button-text p-button-plain h-10 w-32 underline inline-block"
                        />
                    </div>
                </div>
            </div>
            <div class="text-center"></div>
            <div
                v-for="(form, form_index) in forms"
                :key="form_index"
                class="bg-white w-full rounded-xl shadow mb-12 relative"
            >
                <div class="flex flex-wrap p-10">
                    <div v-for="(item, item_index) in form" :key="item_index">
                        <DemandItem
                            :formIndex="form_index"
                            :item="item"
                            :flowType="
                                'roles' in sign_role[0] ? flowCol.type : ''
                            "
                            :flowColId="
                                'roles' in sign_role[0] ? flowCol.id : 0
                            "
                            v-on:openList="
                                openList(item, item_index, form_index)
                            "
                            v-on:choose="(val) => chooseItem(val)"
                        />
                    </div>
                    <hr
                        v-if="remarks.length > 0"
                        class="w-full text-gray-200"
                    />
                    <div class="mt-5">
                        <div
                            v-for="(
                                remark, remark_index
                            ) in remarksWithoutReviewer"
                            :key="remark_index"
                            class="py-3 flex items-baseline"
                        >
                            <div class="w-5">
                                <p class="w-3 text-primary text-xl">•</p>
                            </div>
                            <template v-if="hasHtmlTag(remark.value)">
                                <p
                                    v-html="remark.value"
                                    class="leading-7 underline"
                                ></p>
                            </template>
                            <template v-else>
                                <a
                                    v-if="remark.file"
                                    :href="remark.file.URL"
                                    :title="remark.file.name"
                                    class="underline"
                                    :download="remark.file.name"
                                    >{{ remark.value }}
                                </a>
                                <div v-else>
                                    {{ remark.value }}
                                </div>
                            </template>
                        </div>
                    </div>
                </div>
                <span
                    v-if="forms.length > 1"
                    @click="forms.splice(form_index, 1)"
                    class="absolute top-5 right-5 cursor-pointer"
                    ><i class="fa-solid fa-x"></i
                ></span>
            </div>
            <div
                class="mb-4"
                v-if="
                    'list' in $parent
                        ? $parent.list.status !== 7
                        : displaySignRole
                "
            >
                <hr class="my-6 text-gray-200" />
                <div class="text-center">
                    <span class="text-xl font-semibold"> 需自行指定之關卡</span>
                </div>
                <div
                    class="inline-block mr-3"
                    v-for="role in flow_sign_role !== null
                        ? flow_sign_role[0].roles
                        : sign_role"
                    :key="role.id"
                    v-if="role.self_applicant && !role.isRemoved"
                >
                    <div>
                        <div class="flex justify-between items-center p-2">
                            <label>{{ role.self_name }}*</label>
                            <button
                                @click="
                                    displayRemoveSignRole = true;
                                    removedSignRole = role;
                                "
                                class="cursor-pointer rounded-full h-4 w-4 hover:bg-gray-300 flex items-center justify-center"
                            >
                                <i
                                    class="pi pi-times block"
                                    style="font-size: 13px"
                                ></i>
                            </button>
                        </div>
                        <Dropdown
                            placeholder="選擇人員"
                            :options="getEmployees"
                            :filter="true"
                            :class="{ 'p-invalid': role.invalid && validated }"
                            optionLabel="name"
                            optionValue="id"
                            data-key="id"
                            v-model="role.role_id"
                            @input="$delete(role, 'invalid')"
                        />
                    </div>
                </div>
                <!-- 恢復關卡 -->
                <div
                    class="inline-block align-bottom"
                    v-show="displayResumeLevelButton"
                >
                    <Button
                        @click="resumeLevel()"
                        label="恢復關卡"
                        class="w-32 p-button-outlined p-button-secondary"
                    ></Button>
                </div>
            </div>
            <div class="flex">
                <div
                    class="mr-3"
                    v-if="
                        'list' in $parent ? $parent.list.status !== 7 : canMulti
                    "
                >
                    <Button
                        @click="addDemand()"
                        label="新增申請單"
                        class="w-32 p-button-outlined p-button-secondary"
                    ></Button>
                </div>
                <div
                    v-if="'list' in $parent ? $parent.list.status !== 7 : true"
                    class="mr-3"
                >
                    <Button
                        @click="openCounterModel()"
                        class="p-button-outlined p-button-secondary"
                        label="會簽"
                    >
                    </Button>
                    <div class="mt-2 ml-1">
                        <p
                            v-if="displayCounterRoles.length > 0"
                            class="text-xs text-gray-400"
                        >
                            已會簽：
                        </p>
                        <p
                            class="text-xs text-gray-400"
                            v-for="role in displayCounterRoles"
                            :key="role.id"
                        >
                            {{ role.org_name }}&nbsp;{{ role.name }}
                        </p>
                    </div>
                    <Dialog
                        :visible.sync="counterModel"
                        :dismissableMask="true"
                        :closable="false"
                        :modal="true"
                        :containerStyle="{ width: '30vw' }"
                        class="heightDialog"
                    >
                        <template #header>
                            <div class="w-full flex justify-between">
                                <h3 class="text-2xl font-semibold">會簽</h3>
                                <Button
                                    @click="addCounterNode()"
                                    class="p-button-text p-button-plain h-10 underline p-0"
                                >
                                    新增審核人
                                </Button>
                            </div>
                        </template>
                        <div class="my-auto font-bold text-gray-400">
                            <div class="pb-8">
                                <div
                                    v-for="(
                                        counterRole, counterIndex
                                    ) in counterRoles"
                                    :key="counterRole.id"
                                >
                                    <span>審核人{{ counterIndex + 1 }}</span>
                                    <br />
                                    <Dropdown
                                        class="xl:w-44 2xl:w-48 rounded-md shadow"
                                        placeholder="選擇審核人"
                                        :options="getEmployees"
                                        :filter="true"
                                        optionLabel="name"
                                        optionValue="id"
                                        data-key="id"
                                        v-model="counterRoles[counterIndex]"
                                    >
                                        <template #option="slotProps">
                                            <div>
                                                <span>{{
                                                    slotProps.option.org_name
                                                }}</span>
                                                <span>{{
                                                    slotProps.option.name
                                                }}</span>
                                            </div>
                                        </template>
                                    </Dropdown>
                                    <button
                                        class="ml-2 w-6 cursor-pointer rounded hover:bg-gray-100"
                                        @click="deleteCounterNode(counterIndex)"
                                    >
                                        <i class="far fa-trash-alt"></i>
                                    </button>
                                    <br />
                                    <br />
                                </div>
                            </div>
                            <!-- <div class="pb-8">
              <span>會簽事由</span>
              <br />
              <InputText
                class="w-full"
                placeholder="輸入會簽事由"
                v-model="counterRemark"
              ></InputText>
            </div> -->
                        </div>

                        <template #footer>
                            <div class="flex justify-end">
                                <Button
                                    @click="counterModel = false"
                                    label="取消"
                                    class="p-button-outlined p-button-secondary w-28 mr-5"
                                />
                                <Button
                                    @click="closeCounterModel()"
                                    label="確定"
                                    class="w-28"
                                />
                            </div>
                        </template>
                    </Dialog>
                </div>
                <!-- 預覽 -->
                <div>
                    <Button
                        @click="displayModalPreview = true"
                        class="p-button-outlined p-button-secondary"
                        label="預覽"
                    >
                    </Button>
                    <Dialog
                        :visible.sync="displayModalPreview"
                        :dismissableMask="true"
                        :closable="true"
                        :modal="true"
                        :containerStyle="{ width: '85vw' }"
                        class="preview"
                    >
                        <template #header>
                            <div class="w-full"></div>
                        </template>
                        <div
                            class="mt-1 p-5 rounded-md border border-gray-200 shadow-sm"
                        >
                            <!-- 表單名稱 -->
                            <div
                                class="w-full flex align-center justify-center"
                            >
                                <h3 class="text-2xl font-semibold">
                                    {{ name }}
                                </h3>
                            </div>
                            <!-- 申請人信息 -->
                            <div class="w-full text-center mt-3 mb-6">
                                <div class="flex justify-around">
                                    <div>
                                        <div class="text-base text-gray-400">
                                            申請人
                                        </div>
                                        <div class="text-lg font-semibold">
                                            {{ user.name }}
                                        </div>
                                    </div>
                                    <div>
                                        <div class="text-base text-gray-400">
                                            部門
                                        </div>
                                        <div class="text-lg font-semibold">
                                            {{ user.dep }}
                                        </div>
                                    </div>
                                    <div>
                                        <div class="text-base text-gray-400">
                                            職稱
                                        </div>
                                        <div class="text-lg font-semibold">
                                            {{ user.title }}
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <!-- 同整層 -->
                            <div
                                class="overflow-y-hidden overflow-x-auto w-full"
                            >
                                <table class="table-auto w-full">
                                    <thead class="border-b border-gray-200">
                                        <tr class="h-14">
                                            <th
                                                class="text-left min-width-56"
                                            ></th>
                                            <th
                                                v-for="(
                                                    item, item_index
                                                ) in forms[0]"
                                                :key="item_index"
                                                class="whitespace-nowrap text-left"
                                            >
                                                {{ item.name }}
                                            </th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr
                                            v-for="(form, form_index) in forms"
                                            :key="form_index"
                                            :class="
                                                form_index % 2 === 0
                                                    ? 'h-14'
                                                    : 'h-14 bg-gray-50'
                                            "
                                            class="border-b border-gray-200"
                                        >
                                            <td>{{ form_index + 1 + "." }}</td>
                                            <td
                                                v-for="(
                                                    item, item_index
                                                ) in form"
                                                :key="item_index"
                                            >
                                                <div
                                                    v-if="
                                                        item.type ===
                                                            'customList' ||
                                                        item.type === 'list'
                                                    "
                                                >
                                                    <a
                                                        :href="
                                                            '#popUpId_' +
                                                            form_index +
                                                            item.id
                                                        "
                                                    >
                                                        <Button
                                                            :label="
                                                                item.type ==
                                                                'list'
                                                                    ? '項目清單'
                                                                    : '自訂表單'
                                                            "
                                                            class="p-button-outlined p-button-secondary"
                                                        >
                                                        </Button>
                                                    </a>
                                                </div>
                                                <div
                                                    v-else-if="
                                                        item.type === 'multi'
                                                    "
                                                >
                                                    {{ multiSummary(item) }}
                                                </div>
                                                <div
                                                    v-else-if="
                                                        item.type === 'time'
                                                    "
                                                >
                                                    {{
                                                        item.value
                                                            ? formatTime(
                                                                  item.value
                                                              )
                                                            : ""
                                                    }}
                                                </div>
                                                <div
                                                    v-else-if="
                                                        item.type === 'date'
                                                    "
                                                >
                                                    {{
                                                        item.value
                                                            ? formatDate(
                                                                  item.value
                                                              )
                                                            : ""
                                                    }}
                                                </div>
                                                <div
                                                    v-else-if="
                                                        item.type === 'money'
                                                    "
                                                >
                                                    {{
                                                        item.value
                                                            ? thousands(
                                                                  item.value
                                                              ) + " 元"
                                                            : ""
                                                    }}
                                                </div>
                                                <div
                                                    v-else-if="
                                                        item.type ===
                                                            'document' &&
                                                        item.files?.[0]
                                                    "
                                                    class="font-semibold"
                                                >
                                                    <div
                                                        v-for="(
                                                            file, file_index
                                                        ) in item.files"
                                                        :key="file_index"
                                                    >
                                                        <a
                                                            :href="file.URL"
                                                            target="_blank"
                                                            class="w-32 truncate block"
                                                        >
                                                            {{ file.name }}
                                                        </a>
                                                    </div>
                                                </div>
                                                <div
                                                    v-else-if="
                                                        item.type === 'dropdown'
                                                    "
                                                >
                                                    <template
                                                        v-if="item.mul == 1"
                                                    >
                                                        {{
                                                            item.value?.join(
                                                                "、"
                                                            )
                                                        }}
                                                    </template>
                                                    <template v-else>
                                                        {{ item.value?.name }}
                                                    </template>
                                                </div>
                                                <div v-else>
                                                    {{
                                                        item.value instanceof
                                                        Object
                                                            ? item.value.name
                                                            : item.value
                                                    }}
                                                </div>
                                            </td>
                                        </tr>
                                        <tr class="h-14">
                                            <td></td>
                                            <td
                                                v-for="(
                                                    amount, amount_index
                                                ) in totalAmountForPreview"
                                                :key="amount_index"
                                            >
                                                <div class="font-semibold">
                                                    {{
                                                        amount
                                                            ? thousands(
                                                                  amount
                                                              ) + " 元"
                                                            : ""
                                                    }}
                                                </div>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                        <!-- 表單層 -->
                        <template v-for="(form, form_index) in filteredForms">
                            <template v-for="(lists, lists_index) in form">
                                <Card
                                    v-if="lists.type === 'list'"
                                    class="mt-6 transition duration-200 delay-150 border border-gray-200 shadow-sm"
                                    :id="'popUpId_' + form_index + lists.id"
                                    :key="form_index + lists.id"
                                >
                                    <template #title>
                                        <div
                                            class="flex justify-center text-xl"
                                        >
                                            {{
                                                form_index +
                                                1 +
                                                ". " +
                                                lists.name
                                            }}
                                        </div>
                                    </template>
                                    <template #content>
                                        <div class="w-full overflow-auto">
                                            <table class="table-auto w-full">
                                                <thead>
                                                    <tr class="h-12 bg-gray-50">
                                                        <th
                                                            class="text-left min-width-200"
                                                            v-if="
                                                                'name' in
                                                                lists.list[0]
                                                            "
                                                        >
                                                            項目
                                                        </th>
                                                        <th
                                                            class="text-left min-width-200"
                                                            v-if="
                                                                'count' in
                                                                lists.list[0]
                                                            "
                                                        >
                                                            數量
                                                        </th>
                                                        <th
                                                            class="text-left min-width-200"
                                                            v-if="
                                                                'unit' in
                                                                lists.list[0]
                                                            "
                                                        >
                                                            單位
                                                        </th>
                                                        <th
                                                            class="text-left min-width-200"
                                                            v-if="
                                                                'price' in
                                                                lists.list[0]
                                                            "
                                                        >
                                                            單價
                                                        </th>
                                                        <th
                                                            class="text-left min-width-200"
                                                            v-if="
                                                                'total' in
                                                                lists.list[0]
                                                            "
                                                        >
                                                            總價
                                                        </th>
                                                        <th
                                                            class="text-left min-width-200"
                                                            v-if="
                                                                'memo' in
                                                                lists.list[0]
                                                            "
                                                        >
                                                            備註
                                                        </th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    <tr
                                                        v-for="(
                                                            items, items_index
                                                        ) in lists.list"
                                                        :key="items_index"
                                                        class="border-t border-gray-100 h-12"
                                                    >
                                                        <td
                                                            v-for="(
                                                                item,
                                                                key,
                                                                item_index
                                                            ) in formatInOrderItemsWithProto(
                                                                items,
                                                                lists.list[0]
                                                            )"
                                                            :key="item_index"
                                                        >
                                                            {{
                                                                key ===
                                                                    "price" ||
                                                                key === "total"
                                                                    ? item
                                                                        ? "$" +
                                                                          thousands(
                                                                              item
                                                                          )
                                                                        : ""
                                                                    : item
                                                            }}
                                                        </td>
                                                    </tr>
                                                    <tr
                                                        class="border-t border-gray-100 h-12 bg-gray-50"
                                                    >
                                                        <td
                                                            v-for="(
                                                                item,
                                                                key,
                                                                item_index
                                                            ) in formatInOrderItems(
                                                                lists.list[0]
                                                            )"
                                                            :key="item_index"
                                                            class="font-semibold"
                                                        >
                                                            {{
                                                                key === "total"
                                                                    ? lists.list.reduce(
                                                                          (
                                                                              acc,
                                                                              cur
                                                                          ) =>
                                                                              acc +
                                                                              cur.total,
                                                                          0
                                                                      )
                                                                        ? "$" +
                                                                          thousands(
                                                                              lists.list.reduce(
                                                                                  (
                                                                                      acc,
                                                                                      cur
                                                                                  ) =>
                                                                                      acc +
                                                                                      cur.total,
                                                                                  0
                                                                              )
                                                                          )
                                                                        : ""
                                                                    : ""
                                                            }}
                                                        </td>
                                                    </tr>
                                                </tbody>
                                            </table>
                                        </div>
                                    </template>
                                </Card>
                                <Card
                                    v-else-if="lists.type === 'customList'"
                                    class="mt-6 transition duration-200 delay-150 border border-gray-200 shadow-sm"
                                    :id="'popUpId_' + form_index + lists.id"
                                >
                                    <template #title>
                                        <div
                                            class="flex justify-center text-xl"
                                        >
                                            {{
                                                form_index +
                                                1 +
                                                ". " +
                                                lists.name
                                            }}
                                        </div>
                                    </template>
                                    <template #content>
                                        <CustomForm
                                            :key="form_index + lists.id"
                                            :action="action + 1"
                                            :dem_custom_list="
                                                filteredForms[form_index][
                                                    lists_index
                                                ].custom_list
                                            "
                                            :dem_form_setting="
                                                filteredForms[form_index][
                                                    lists_index
                                                ].form_setting
                                            "
                                            :signOff="0"
                                            :edit="false"
                                        />
                                    </template>
                                </Card>
                            </template>
                        </template>
                    </Dialog>
                </div>
            </div>
            <div class="buttons w-full flex justify-between md:justify-end">
                <Button @click="reset" label="清除" class="p-button-link" />
                <Button
                    @click="
                        layout_id !== undefined
                            ? ($parent.action = $parent.action - 1)
                            : (action = action - 1);
                        layout_id !== undefined
                            ? ($parent.list.forms = list_forms_default)
                            : (forms = []);
                        importOptions = [];
                        flow_sign_role = null;
                        choose = null;
                    "
                    label="取消"
                    class="p-button-outlined p-button-secondary w-28 mr-5"
                />
                <Button
                    :disabled="btnDisabled == 1"
                    @click="layout_id !== undefined ? submitAgain() : submit()"
                    :label="layout_id !== undefined ? '再次申請' : '申請'"
                    class="w-28"
                />
            </div>
        </div>
        <ConfirmDialog ref="confirmDialog" />
        <Toast ref="toast" position="top-center" />
        <Dialog
            :visible.sync="displayModal2"
            :closable="false"
            :containerStyle="{
                width: '30vw',
            }"
            :modal="true"
        >
            <div class="pb-8">
                <img class="mx-auto" src="@images/popup_state/warning.png" />
                <br />
                <p class="text-center">
                    {{
                        displayModal2 && "value" in modal2Info
                            ? modal2Info.value.prompt
                            : ""
                    }}
                </p>
            </div>
            <template #footer>
                <div class="flex justify-center">
                    <Button
                        @click="
                            displayModal2 = false;
                            modal2Info = null;
                        "
                        label="我知道了"
                    />
                </div>
            </template>
        </Dialog>
        <Dialog
            :visible.sync="displayModal"
            :dismissableMask="true"
            :containerStyle="{
                width: '70%',
            }"
            :modal="true"
        >
            <template #header>
                <div class="w-full flex justify-between">
                    <h3 class="text-2xl font-semibold whitespace-nowrap">
                        項目清單
                    </h3>
                </div>
            </template>
            <div class="m-0">
                <table class="py-5 table-auto" width="100%">
                    <thead class="border-b border-gray-200">
                        <tr>
                            <th class="text-left" v-if="'name' in menuForm[0]">
                                項目
                            </th>
                            <th class="text-left" v-if="'count' in menuForm[0]">
                                數量
                            </th>
                            <th class="text-left" v-if="'unit' in menuForm[0]">
                                單位
                            </th>
                            <th class="text-left" v-if="'price' in menuForm[0]">
                                單價
                            </th>
                            <th class="text-left" v-if="'total' in menuForm[0]">
                                <p class="w-32">總價</p>
                            </th>
                            <th class="text-left" v-if="'memo' in menuForm[0]">
                                備註
                            </th>
                            <th
                                v-if="
                                    displayModal &&
                                    settingForm[column_index].mode == 1
                                "
                                class="text-right w-40"
                            ></th>
                        </tr>
                    </thead>
                    <tbody class="border-gray-200">
                        <tr
                            v-for="(item, item_index) in menuForm"
                            :key="item_index"
                        >
                            <td class="py-3" v-if="'name' in menuForm[0]">
                                <InputText
                                    v-if="
                                        displayModal &&
                                        settingForm[column_index].mode == 1
                                    "
                                    v-model="item.name"
                                />
                                <p v-else>{{ item.name }}</p>
                            </td>
                            <td class="py-3" v-if="'count' in menuForm[0]">
                                <InputNumber v-model="item.count" />
                            </td>
                            <td class="py-3" v-if="'unit' in menuForm[0]">
                                <InputText
                                    v-if="
                                        displayModal &&
                                        settingForm[column_index].mode == 1
                                    "
                                    v-model="item.unit"
                                />
                                <p v-else>{{ item.unit }}</p>
                            </td>
                            <td class="py-3" v-if="'price' in menuForm[0]">
                                <InputNumber
                                    mode="currency"
                                    currency="USD"
                                    locale="en-US"
                                    v-if="
                                        displayModal &&
                                        settingForm[column_index].mode == 1
                                    "
                                    v-model="item.price"
                                />
                                <p v-else>{{ item.price }}</p>
                            </td>
                            <td class="p-2" v-if="'total' in menuForm[0]">
                                <p>{{ menuTotal(item) }}</p>
                            </td>
                            <td class="py-3" v-if="'memo' in menuForm[0]">
                                <InputText
                                    v-if="
                                        displayModal &&
                                        settingForm[column_index].mode == 1
                                    "
                                    v-model="item.memo"
                                />
                                <p v-else>{{ item.memo }}</p>
                            </td>
                            <td
                                v-if="
                                    displayModal &&
                                    settingForm[column_index].mode == 1
                                "
                                class="text-right my-3"
                            >
                                <button
                                    v-if="menuForm.length > 1"
                                    @click="menuForm.splice(item_index, 1)"
                                >
                                    <img
                                        style="max-width: 24px"
                                        src="@images/icon/delete_enable.png"
                                        alt=""
                                    />
                                </button>
                            </td>
                        </tr>
                        <tr>
                            <td class="py-3" v-if="'name' in menuForm[0]"></td>
                            <td class="py-3" v-if="'count' in menuForm[0]"></td>
                            <td class="py-3" v-if="'unit' in menuForm[0]"></td>
                            <td class="py-3" v-if="'price' in menuForm[0]"></td>
                            <td class="p-2" v-if="'total' in menuForm[0]">
                                <p>合計 ${{ finallyTotal() }}</p>
                            </td>
                            <td class="py-3" v-if="'memo' in menuForm[0]"></td>
                            <td v-if="action == 1" class="my-3"></td>
                        </tr>
                    </tbody>
                </table>
                <div
                    v-if="displayModal && settingForm[column_index].mode == 1"
                    @click="
                        menuForm.push(
                            JSON.parse(
                                JSON.stringify(menuForm[menuForm.length - 1])
                            )
                        )
                    "
                    class="w-full h-12 my-3 text-current cursor-pointer"
                >
                    <i class="fa-solid fa-plus"></i>
                    <span class="my-auto inline-block">新增項目</span>
                </div>
            </div>
            <template #footer> </template>
        </Dialog>
        <ImportDialog
            header="帶入需求單"
            :visible.sync="displayModal1"
            :dismissableMask="true"
            :containerStyle="{
                width: '30vw',
            }"
            :modal="true"
            class="import"
        >
            <div class="my-auto font-bold text-gray-400">
                <!-- :options="getRoles"  v-model="reviewToNodeId" @input="invalids.orgs = false"-->

                <div class="pb-8">
                    <span>需求單類型</span>
                    <br />

                    <Dropdown
                        class="w-full mb-8"
                        placeholder="選擇要帶入的需求單"
                        optionLabel="name"
                        optionValue="id"
                        data-key="id"
                        :options="importOptions"
                        :showClear="true"
                        @show="fetchImportDropdown"
                        v-model="importsLayoutId"
                    />
                    <span>需求單單號</span>
                    <br />
                    <InputText
                        type="text"
                        placeholder="輸入需求單單號"
                        v-model="importsDemandNo"
                    />
                </div>
            </div>
            <template #footer>
                <Button
                    @click="displayModal1 = false"
                    label="取消"
                    class="p-button-outlined p-button-secondary w-28 mr-5"
                />
                <Button @click="importValue()" label="帶入" class="w-28" />
            </template>
        </ImportDialog>
        <Dialog
            :visible.sync="displayModal3"
            :dismissableMask="true"
            :containerStyle="{
                width: '70%',
            }"
            :modal="true"
            header="自訂表單"
            class="customDialog"
            @hide="
                $set(
                    forms[form_index][column_index],
                    'custom_list',
                    $refs.customForm.custom_rows
                );
                $set(
                    forms[form_index][column_index],
                    'form_setting',
                    $refs.customForm.form_setting
                );
                autoSave();
            "
        >
            <CustomForm
                :action="action + 1"
                :mode="'submit'"
                :dem_custom_list="
                    displayModal3 &&
                    'custom_list' in forms[form_index][column_index]
                        ? forms[form_index][column_index].custom_list
                        : [['']]
                "
                :dem_form_setting="
                    displayModal3 &&
                    'form_setting' in forms[form_index][column_index]
                        ? forms[form_index][column_index].form_setting
                        : [
                              {
                                  id: 1,
                                  name: '',
                                  type: 'input',
                                  mode: 0,
                                  is_must: false,
                                  frozen: false,
                              },
                          ]
                "
                :dem_can_insert="
                    displayModal3 &&
                    'can_insert' in forms[form_index][column_index]
                        ? forms[form_index][column_index].can_insert
                        : false
                "
                :signOff="0"
                ref="customForm"
            />
            <template #footer>
                <div
                    v-if="settingForm[column_index].can_insert"
                    @click="
                        $refs.customForm.custom_rows.push([]);
                        $refs.customForm.custom_values.push({});
                        $refs.customForm.form_setting.forEach((col, index) => {
                            col.type == 'input'
                                ? $refs.customForm.custom_rows[
                                      $refs.customForm.custom_rows.length - 1
                                  ].push('')
                                : $refs.customForm.custom_rows[
                                      $refs.customForm.custom_rows.length - 1
                                  ].push(0);
                            $set(
                                $refs.customForm.custom_values[
                                    $refs.customForm.custom_values.length - 1
                                ],
                                index + '',
                                null
                            );
                        });
                    "
                    class="p-1 text-center"
                >
                    <label>新增內容</label>
                </div>
                <div v-else>
                    <div class="p-1"></div>
                </div>
            </template>
        </Dialog>
        <!-- 移除關卡彈窗 -->
        <Dialog
            :visible.sync="displayRemoveSignRole"
            :closable="false"
            :containerStyle="{
                width: '350px',
            }"
            :modal="true"
        >
            <div class="pb-16">
                <img class="mx-auto" src="@images/popup_state/warning.png" />
                <br />
                <p class="text-center">是否確定移除關卡?</p>
            </div>
            <template #footer>
                <div class="flex justify-evenly">
                    <Button
                        @click="displayRemoveSignRole = false"
                        label="取消"
                        class="p-button-outlined p-button-secondary"
                    />
                    <Button @click="removeSignRole()" label="確定" class="" />
                </div>
            </template>
        </Dialog>
        <ScrollTop :threshold="1" icon="pi pi-arrow-up" />
    </div>
</template>

<script>
import axios from "axios";
import Button from "primevue/button";
import Card from "primevue/card";
import ConfirmDialog from "primevue/confirmdialog";
import CustomForm from "@/demand/common/custom-form";
import DemandItem from "@/demand/common/demand-item";
import Dialog from "primevue/dialog";
import Dropdown from "primevue/dropdown";
import ScrollTop from "primevue/scrolltop";
import ImportDialog from "primevue/dialog";
import InputText from "primevue/inputtext";
import InputNumber from "primevue/inputnumber";
import Toast from "primevue/toast";
import DataTable from "primevue/datatable";
import Column from "primevue/column";
import ColumnGroup from "primevue/columngroup";
import Row from "primevue/row";
export default {
    components: {
        Button,
        Card,
        ConfirmDialog,
        CustomForm,
        DemandItem,
        Dialog,
        Dropdown,
        ScrollTop,
        ImportDialog,
        InputText,
        InputNumber,
        Toast,
        DataTable,
        Column,
        ColumnGroup,
        Row,
    },
    props: [
        "parent_no",
        "layout_id",
        "demand_id",
        "list_forms",
        "default_forms",
        "default_setting",
    ],
    data() {
        return {
            action: 0,
            column_index: 0,
            messages: null,
            dataList: [],
            db_data: {},
            displayModal: false,
            displayModal1: false,
            displayModal2: false,
            displayModal3: false,
            modal2Info: null,
            counterModel: false,
            displayModalPreview: false,
            menuForm: [
                {
                    name: "",
                    count: 0,
                },
            ],
            apiURL: "/api/demand/submit",
            settingForm: [],
            sign_role: [],
            flow_sign_role: null,
            name: "",
            id: 0,
            forms: [],
            original_layout_id: null,
            canMulti: false,
            canImport: false,
            counterRoles: [], //被會簽的json簽核節點們
            displayCounterRoles: [],
            displayRemoveSignRole: false,
            remarks: [],
            imports: [],
            importOptions: [],
            importsLayoutId: null,
            importsDemandNo: null,
            getEmployees: [],
            user: {},
            choose: null,
            btnDisabled: 0,
            list_forms_default: null,
            filter_roles: [],
            form_index: null,
            validated: false,
            removedSignRole: null,
        };
    },
    mounted() {
        //針對申請子單的例外處理
        if (!window.demandCache) {
            window.demandCache = {
                get: async (layoutId) => {
                    try {
                        let { data } = await axios.get(
                            `/api/demand/submit/cache/${layoutId}`
                        );
                        return data;
                    } catch (e) {
                        console.warn(e);
                        throw e;
                    }
                },
                set: async (layoutId, data) => {
                    try {
                        axios.put(`/api/demand/submit/cache/${layoutId}`, data);
                    } catch (e) {
                        console.warn(e);
                        throw e;
                    }
                },
                del: async (layoutId) => {
                    try {
                        axios.delete(`/api/demand/submit/cache/${layoutId}`);
                    } catch (e) {
                        console.warn(e);
                        throw e;
                    }
                },
            };
        }
        if (this.parent_no) {
            this.getGroups(true);
        } else {
            this.getGroups();
        }

        if (this.layout_id !== null && this.layout_id !== undefined) {
            this.action = 1;
            this.settingForm = this.default_setting.columns;
            this.name = this.default_setting.name;
            this.id = this.default_setting.id;
            this.original_layout_id = this.default_setting.original_id;
            this.canMulti = this.default_setting.can_multi;
            this.canImport = this.default_setting.import;
            this.remarks = this.default_setting.remarks;
            this.sign_role = this.default_setting.sign_role;
            this.forms = JSON.parse(JSON.stringify(this.list_forms));
            this.list_forms_default = JSON.parse(
                JSON.stringify(this.default_forms)
            );
        }
        this.getEmployeesDropDown();
        this.getUserData();
    },
    watch: {
        action: function (newValue) {
            if (newValue == 0) {
                this.btnDisabled = 0;
                this.validated = false;
            }
        },
        forms: {
            handler: function (val, oldVal) {
                this.autoSave();
                this.totalCompute();
            },
            deep: true,
        },
        sign_role: {
            handler: function (val, oldVal) {
                this.autoSave();
            },
            deep: true,
        },
    },
    methods: {
        autoSave: window._.debounce(async function () {
            if (this.action == 0) return;
            if (
                !this.forms[0]?.every(
                    (el, index) => el.name == this.settingForm[index].name
                )
            ) {
                this.$refs.toast.add({
                    severity: "error",
                    summary: "表格與設定不一致，暫存失敗",
                    life: 3000,
                });
                return;
            }

            if (
                (this.layout_id == null || this.layout_id == undefined) &&
                this.action == 1 &&
                this.btnDisabled == 0 &&
                this.forms.length > 0 &&
                this.forms.length != []
            ) {
                let temp_list = {
                    formsValue: this.forms.map((form) =>
                        form.map((col) => {
                            if (col.type == "list") {
                                return col.list;
                            } else if (col.type == "customList") {
                                return col.custom_list;
                            } else if (col.type == "document") {
                                return col.files;
                            } else {
                                return col.value;
                            }
                        })
                    ),
                    layout: this.settingFormNoValue.map((colSetting) => {
                        if (colSetting.type == "database") {
                            return { ...colSetting, data: [] };
                        }
                        return colSetting;
                    }),
                    sign_role: this.sign_role,
                    layout_id: this.id,
                };

                await window.demandCache.set(this.original_layout_id, {
                    data: temp_list,
                });

                this.$refs.toast.add({
                    severity: "success",
                    summary: "資料已暫存",
                    life: 3000,
                });
            }
        }, 10000),
        async fetchCache() {
            await window.demandCache
                .get(this.original_layout_id)
                .then(({ forms, formsValue, layout, layout_id, sign_role }) => {
                    // 無上次
                    if (!layout) {
                        return;
                    }

                    // 檢查是哪個欄位不一樣 Debug 用
                    // this.settingFormNoValue.map((el, index) => {
                    //     if (JSON.stringify(el) != JSON.stringify(layout[index])) {
                    //         console.log(index, el, layout[index])
                    //     }
                    // })

                    // layout一致可以直接套入cache內容，否則清除cache
                    if (layout_id == this.id) {
                        // 套用cache內容
                        // formsValue 是新 cache 僅存取 column value
                        if (formsValue) {
                            this.forms = this.forms.map((form, formIndex) =>
                                form.map((col, colIndex) => {
                                    if (col.type == "list") {
                                        return {
                                            ...col,
                                            list: formsValue[formIndex][
                                                colIndex
                                            ],
                                        };
                                    } else if (col.type == "customList") {
                                        return {
                                            ...col,
                                            custom_list:
                                                formsValue[formIndex][colIndex],
                                        };
                                    } else if (col.type == "document") {
                                        return {
                                            ...col,
                                            files: formsValue[formIndex][
                                                colIndex
                                            ],
                                        };
                                    } else if (
                                        col.type == "date" ||
                                        col.type == "time"
                                    ) {
                                        return {
                                            ...col,
                                            value: formsValue[formIndex][
                                                colIndex
                                            ]
                                                ? new Date(
                                                      formsValue[formIndex][
                                                          colIndex
                                                      ]
                                                  )
                                                : null,
                                        };
                                    } else if (col.type == "single") {
                                        if (formsValue[formIndex][colIndex]) {
                                            if (
                                                formsValue[formIndex][colIndex]
                                                    .name == "其他"
                                            ) {
                                                col.options.forEach((op) => {
                                                    if (
                                                        op.id ==
                                                        formsValue[formIndex][
                                                            colIndex
                                                        ].id
                                                    ) {
                                                        op.option =
                                                            formsValue[
                                                                formIndex
                                                            ][colIndex].option;
                                                    }
                                                });
                                            }
                                        }
                                        return {
                                            ...col,
                                            value: formsValue[formIndex][
                                                colIndex
                                            ],
                                        };
                                    } else if (col.type == "multi") {
                                        if (formsValue[formIndex][colIndex]) {
                                            const otherOption = formsValue[
                                                formIndex
                                            ][colIndex].find(
                                                (op) => op?.name == "其他"
                                            );
                                            if (otherOption) {
                                                col.options.forEach((op) => {
                                                    if (
                                                        op.id == otherOption.id
                                                    ) {
                                                        op.option =
                                                            otherOption.option;
                                                    }
                                                });
                                            }
                                        }
                                        return {
                                            ...col,
                                            value: formsValue[formIndex][
                                                colIndex
                                            ],
                                        };
                                    } else {
                                        return {
                                            ...col,
                                            value: formsValue[formIndex][
                                                colIndex
                                            ],
                                        };
                                    }
                                })
                            );
                        }
                        // 舊 cache 過度用
                        else {
                            if (forms) {
                                this.forms = forms;
                            }
                        }

                        // 檢查當前sign_role與cache是否一致
                        // 剔除已選擇人員的影響
                        const latest_sign_role_layout = this.sign_role.map(
                            (role) => {
                                // flow role
                                if (role.roles) {
                                    return {
                                        ...role,
                                        roles: role.roles.map((role) => {
                                            if (role.self_applicant) {
                                                return {
                                                    ...role,
                                                    role_id: undefined,
                                                    isRemoved: undefined,
                                                };
                                            } else {
                                                return role;
                                            }
                                        }),
                                    };
                                }
                                // general role
                                else {
                                    if (role.self_applicant) {
                                        return {
                                            ...role,
                                            role_id: undefined,
                                            isRemoved: undefined,
                                        };
                                    } else {
                                        return role;
                                    }
                                }
                            }
                        );
                        const cache_sign_role_layout = sign_role.map((role) => {
                            // flow role
                            if (role.roles) {
                                return {
                                    ...role,
                                    roles: role.roles.map((role) => {
                                        if (role.self_applicant) {
                                            return {
                                                ...role,
                                                role_id: undefined,
                                                isRemoved: undefined,
                                            };
                                        } else {
                                            return role;
                                        }
                                    }),
                                };
                            }
                            // general role
                            else {
                                if (role.self_applicant) {
                                    return {
                                        ...role,
                                        role_id: undefined,
                                        isRemoved: undefined,
                                    };
                                } else {
                                    return role;
                                }
                            }
                        });

                        // debug用，檢查sign_role是否一致
                        // latest_sign_role_layout.forEach((role, i) => {
                        //     if (role.roles) {
                        //         role.roles.forEach((role, ii) => {
                        //             if (
                        //                 JSON.stringify(role) !=
                        //                 JSON.stringify(
                        //                     cache_sign_role_layout[i].roles[ii]
                        //                 )
                        //             ) {
                        //                 console.log(JSON.stringify(role));
                        //                 console.log(
                        //                     JSON.stringify(
                        //                         cache_sign_role_layout[i].roles[
                        //                             ii
                        //                         ]
                        //                     )
                        //                 );
                        //             }
                        //         });
                        //     }
                        // });

                        // 一樣就可以套入cache內容(包含已選擇人員)，否則使用最新sign_role
                        if (
                            JSON.stringify(latest_sign_role_layout) ==
                            JSON.stringify(cache_sign_role_layout)
                        ) {
                            this.sign_role = sign_role;
                        }
                    } else {
                        this.$refs.toast.add({
                            severity: "warn",
                            summary: "因此需求單欄位變動,清除原暫存資料!",
                            detail: this.messages,
                            life: 3000,
                        });
                        window.demandCache.del(this.original_layout_id);
                    }
                })
                .catch((e) => {
                    console.error(e);
                });
        },
        getGroups(isChild = false) {
            axios
                .get(this.apiURL + "/groups", {
                    params: {
                        is_child: isChild,
                    },
                })
                .then((response) => {
                    let dataRows = response.data;
                    if (dataRows) {
                        this.dataList = dataRows;
                        this.dataList.forEach((list) => {
                            list.layouts.sort((a, b) => {
                                return (
                                    (b.sort != "" && b.sort != null) -
                                        (a.sort != "" && a.sort != null) ||
                                    a.sort - b.sort
                                );
                            });
                        });
                    }
                })
                .catch((error) => {
                    console.error(error);
                });
        },
        openCounterModel() {
            this.counterModel = true;
        },
        addCounterNode() {
            this.counterRoles.push(0);
        },
        closeCounterModel() {
            if (!this.checkCanCloseCounterModel()) {
                this.$refs.toast.add({
                    severity: "error",
                    summary: "請選擇會簽人員",
                    life: 3000,
                });
                return;
            }
            this.counterModel = false;
            this.displayCounterRoles = [];
            this.counterRoles.forEach((role, index) => {
                this.displayCounterRoles.push(
                    this.getEmployees.find((emp) => emp.id == role)
                );
            });
        },
        checkCanCloseCounterModel() {
            return !this.counterRoles.some((role) => role === 0);
        },
        deleteCounterNode(index) {
            this.counterRoles.splice(index, 1);
        },
        getUserData() {
            axios
                .get("/api/demand/user/data")
                .then((response) => {
                    this.user = response.data ?? {};
                    let user = {
                        id: this.user.id,
                        job_title: this.user.title,
                        name: this.user.name,
                        org_name: this.user.dep,
                    };
                    this.forms.forEach((form) => {
                        form.forEach((col) => {
                            if (col.type == "employee") {
                                this.$set(col, "value", user);
                            }
                        });
                    });
                })
                .catch((error) => {
                    console.error(error);
                });
        },
        async getLayout(id) {
            await axios
                .get(this.apiURL + "/layout", { params: { id: id } })
                .then((response) => {
                    let res = response.data;
                    if (res) {
                        this.settingForm = res.columns;
                        this.name = res.name;
                        this.id = res.id;
                        // this.prevId = res.prev_id;
                        this.original_layout_id = res.original_id;
                        this.canMulti = res.can_multi;
                        this.canImport = res.import;
                        this.remarks = [];
                        for (let i = 0; i < res.remarks.length; i++) {
                            if (res.remarks[i] instanceof Object) {
                                this.remarks.push(res.remarks[i]);
                            } else {
                                this.remarks.push({
                                    value: res.remarks[i],
                                    recipient: "所有人",
                                });
                            }
                        }

                        if (this.forms && this.forms.length == 0) {
                            this.forms.push(this.settingForm);
                        }
                        this.sign_role = res.sign_role;
                        this.forms.forEach((form) => {
                            form.forEach((col) => {
                                if (col.type == "employee") {
                                    if (Object.keys(this.user).length === 0) {
                                        this.getUserData();
                                    } else {
                                        let user = {
                                            id: this.user.id,
                                            job_title: this.user.title,
                                            name: this.user.name,
                                            org_name: this.user.dep,
                                        };
                                        this.$set(col, "value", user);
                                    }
                                }
                                if (!("value" in col) && col.type == "total") {
                                    this.$set(col, "value", null);
                                }
                                this.$delete(col, "invalid");
                            });
                        });
                    }
                });

            await this.fetchCache();

            this.action = 1;
        },
        openList(item, item_index, form_index) {
            this.column_index = item_index;
            this.modal2Info = item;
            this.form_index = form_index;
            if (item.id == this.settingForm[item_index].id) {
                if (item.type == "list") {
                    this.menuForm = item.list;
                    this.displayModal = true;
                } else if (item.type == "customList") {
                    this.displayModal3 = true;
                } else if (item.type == "cascade") {
                    const index = item.options.findIndex((option) => {
                        if (option.states) {
                            return option.states
                                .map((el) => el.iname)
                                .includes(item.value.split(",")[1]);
                        } else {
                            return option.name == item.value;
                        }
                    });
                    item = { value: item.options[index] };
                    this.modal2Info = item;
                    if (
                        "prompt" in item.value &&
                        item.value.prompt !== undefined &&
                        item.value.prompt !== "" &&
                        item.value.prompt !== null
                    ) {
                        this.displayModal2 = true;
                    }
                } else if (
                    "prompt" in item.value &&
                    item.value.prompt !== undefined &&
                    item.value.prompt !== "" &&
                    item.value.prompt !== null
                ) {
                    this.displayModal2 = true;
                }
            }
        },
        validateCustomForm() {
            let error = false;
            this.forms.forEach((form) => {
                form.forEach((col) => {
                    if (col.type == "customList") {
                        col.custom_list.forEach((row, row_index) => {
                            col.custom_list[row_index].forEach(
                                (custom_col, col_index) => {
                                    if (
                                        col.form_setting[col_index].mode == 1 &&
                                        col.form_setting[col_index].is_must
                                    ) {
                                        if (
                                            custom_col == null ||
                                            custom_col.length === 0
                                        ) {
                                            error = true;
                                        } else {
                                        }
                                    }
                                }
                            );
                        });
                    }
                });
            });
            return error;
        },
        menuTotal(item) {
            item.total = (item.count * 10000 * item.price) / 10000 || 0;
            return item.total;
        },
        finallyTotal() {
            let menu_totals = this.menuForm.map((form) => form.total);
            return menu_totals.reduce((a, m) => {
                return a + m || 0;
            });
        },
        fetchImportDropdown() {
            if (!this.importOptions.length) {
                axios
                    .get(this.apiURL + "/import/dropdown", {
                        params: { layout_id: this.id },
                    })
                    .then((response) => {
                        let res = response.data;
                        if (res) {
                            this.importOptions = res;
                        }
                    });
            }
        },
        importValue() {
            let params = {
                layout_id: this.id,
                demand_no: this.importsDemandNo,
            };
            axios
                .get(this.apiURL + "/import/value", {
                    params: params,
                })
                .then((response) => {
                    let res = response.data;
                    if (res && res.forms.length !== 0) {
                        this.imports = res.forms;
                        this.forms = [];
                        this.imports.forEach(() => {
                            this.forms.push(
                                JSON.parse(JSON.stringify(this.settingForm))
                            );
                        });
                        this.forms.forEach((form, index) => {
                            form.forEach((f) => {
                                this.imports[index].forEach((imp) => {
                                    if (f.id == imp.id) {
                                        if (
                                            f.type == "money" &&
                                            imp.value == ""
                                        ) {
                                            this.$set(f, "value", 0);
                                        } else if (
                                            f.type == "date" ||
                                            f.type == "time"
                                        ) {
                                            if (imp.value !== "") {
                                                this.$set(
                                                    f,
                                                    "value",
                                                    new Date(imp.value)
                                                );
                                            }
                                        }
                                        if (f.type == "customList") {
                                            this.$set(
                                                f,
                                                "custom_list",
                                                imp.custom_list
                                            );
                                        } else {
                                            this.$set(f, "value", imp.value);
                                        }
                                    }
                                });
                            });
                        });
                        this.forms[0].forEach((form) => {
                            if (form.id == this.sign_role[0].col_id) {
                                this.choose = form;
                                if ("value" in this.choose) {
                                    this.flow_sign_role = this.sign_role.filter(
                                        (role) => {
                                            if (
                                                this.choose.value.id == role.id
                                            ) {
                                                return role;
                                            } else if (
                                                role.option_ids.includes(
                                                    this.choose.value.id
                                                )
                                            ) {
                                                return role;
                                            }
                                        }
                                    );
                                }
                            }
                        });

                        // flow role
                        if (this.flow_sign_role !== null) {
                            this.flow_sign_role[0].roles.forEach((role) => {
                                // 申請人填寫
                                if (role.self_applicant) {
                                    // 帶入單據有人選
                                    if (role.self_name in res.roles) {
                                        role.role_id =
                                            res.roles[role.self_name];
                                        // 帶入單據未選
                                    } else {
                                        role.isRemoved = true;
                                    }
                                }
                            });
                        }
                        // sign role
                        else {
                            this.sign_role.forEach((role) => {
                                if (role.self_applicant) {
                                    // 帶入單據有人選
                                    if (role.self_name in res.roles) {
                                        role.role_id =
                                            res.roles[role.self_name];
                                        // 帶入單據未選
                                    } else {
                                        role.isRemoved = true;
                                    }
                                }
                            });
                        }

                        this.$refs.toast.add({
                            severity: "success",
                            summary: "獲取成功",
                            detail: this.messages,
                            life: 3000,
                        });
                    } else if (res == 0) {
                        this.$refs.toast.add({
                            severity: "error",
                            summary: "獲取失敗",
                            detail: this.messages,
                            life: 3000,
                        });
                    } else {
                        this.forms = [];
                        this.forms.push(
                            JSON.parse(JSON.stringify(this.settingForm))
                        );
                    }
                })
                .catch((error) => {
                    console.error(error);
                    this.$refs.toast.add({
                        severity: "error",
                        summary: "獲取失敗!",
                        detail: this.messages,
                        life: 3000,
                    });
                })
                .finally(() => {
                    this.displayModal1 = false;
                    this.imports = [];
                    this.importsLayoutId = null;
                    this.importsDemandNo = null;
                });
        },
        removeSignRole() {
            this.$set(this.removedSignRole, "isRemoved", true);
            this.removedSignRole = null;
            this.displayRemoveSignRole = false;
        },
        getRoles() {
            return this.flow_sign_role !== null
                ? this.flow_sign_role[0].roles
                : this.sign_role;
        },
        resumeLevel() {
            this.getRoles().forEach((role) => {
                if (role.self_applicant) {
                    role.isRemoved = false;
                }
            });
        },
        resetLevel() {
            this.getRoles().forEach((role) => {
                //目前系統設計上只能修改審核人，不能修改經辦人
                if (role.type == 0) {
                    role.isRemoved = false;
                    delete role.role_id;
                }
            });
            this.$forceUpdate();
        },

        validate() {
            let error = false;
            this.forms.forEach((form) => {
                form.forEach((f) => {
                    if (f.type !== "document") {
                        var regu = "^[ ]+$";
                        var re = new RegExp(regu);
                        if (
                            f.must &&
                            !f.audit_must &&
                            (!("value" in f) ||
                                f.value?.length == 0 ||
                                re.test(f.value) ||
                                f.value === null)
                        ) {
                            error = true;
                            this.$set(f, "invalid", true);
                        } else {
                            this.$delete(f, "invalid");
                        }
                    } else {
                        if (
                            f.must &&
                            !f.audit_must &&
                            (!f.files || f.files.length == 0)
                        ) {
                            error = true;
                            this.$refs.toast.add({
                                severity: "error",
                                summary: "*字號附件欄位需上傳附件",
                                life: 3000,
                            });
                        }
                    }
                });
            });

            if (error) {
                return error;
            }

            // 檢測特定選項流程
            if (this.flow_sign_role !== null) {
                if (
                    this.flow_sign_role[0].roles.some(
                        (role) => role.self_applicant === true
                    )
                ) {
                    let flow_sign_role_applicant =
                        this.flow_sign_role[0].roles.filter(
                            (role) =>
                                role.self_applicant === true && !role.isRemoved
                        );
                    flow_sign_role_applicant.forEach((role) => {
                        if (!("role_id" in role) || role.role_id === 0) {
                            this.$set(role, "invalid", true);
                            error = true;
                        } else {
                            this.$delete(role, "invalid");
                        }
                    });
                }
            }
            // 檢測通用流程
            else {
                if (
                    this.sign_role.some((role) => role.self_applicant == true)
                ) {
                    let sign_role_applicants = this.sign_role.filter(
                        (role) => role.self_applicant == true && !role.isRemoved
                    );
                    sign_role_applicants.forEach((role) => {
                        if (!("role_id" in role) || role.role_id === 0) {
                            this.$set(role, "invalid", true);
                            error = true;
                        } else {
                            this.$delete(role, "invalid");
                        }
                    });
                }
            }

            return error;
        },
        addDemand() {
            //   if (this.forms.length > 9) {
            //     this.$refs.toast.add({
            //       severity: "error",
            //       summary: "最多新增10筆",
            //       life: 3000,
            //     });
            //     return;
            //   }
            this.forms[this.forms.length - 1].forEach((col) => {
                if (
                    (col.type == "date" || col.type == "time") &&
                    "value" in col &&
                    col.value !== null
                ) {
                    this.$set(col, "dateTime", Date.parse(col.value));
                }
            });
            this.forms.push(
                JSON.parse(JSON.stringify(this.forms[this.forms.length - 1]))
            );
            this.forms.forEach((form) => {
                form.forEach((col) => {
                    if (
                        (col.type == "date" || col.type == "time") &&
                        "value" in col &&
                        col.value !== null
                    ) {
                        col.value = new Date(col.dateTime);
                    }
                });
            });
            this.forms[this.forms.length - 1].forEach((col) => {
                if (col.type == "document") {
                    col.files = [];
                }
            });
        },
        chooseItem(val) {
            this.choose = val;
            if ("roles" in this.sign_role[0]) {
                this.forms.forEach((form) => {
                    form.forEach((col) => {
                        if (col.id == this.choose.id) {
                            this.$set(col, "value", this.choose.value);
                        }
                    });
                });
            }
        },
        submit() {
            const validatorResult = this.validate();
            const validatorCustomFormResult = this.validateCustomForm();
            this.validated = true;
            if (validatorResult == true || validatorCustomFormResult == true) {
                this.$refs.toast.add({
                    severity: "error",
                    summary:
                        validatorResult == true
                            ? "*字號為必填項目!"
                            : "自訂表單*字號為必填項目!",
                    life: 3000,
                });
                return;
            }
            //限制金額判斷
            //一般流程
            if (
                this.sign_role.some(
                    (role) =>
                        "limit_dollar" in role && role.limit_dollar == true
                )
            ) {
                let dollar_col = this.forms[0].filter((form) => {
                    return form.id == this.sign_role[0].dollar_col_id;
                });
                if (
                    !("value" in dollar_col[0]) ||
                    dollar_col[0].value == null
                ) {
                    dollar_col[0].value = 0;
                }
                let sign_role = this.sign_role.filter((role, index) => {
                    if (role.limit_dollar == false) return true;
                    if (
                        role.dollar <= dollar_col[0].value ||
                        !("dollar" in role)
                    ) {
                        return true;
                    } else {
                        if (
                            !("dollar" in this.sign_role[index - 1]) ||
                            this.sign_role[index - 1].limit_dollar == false
                        ) {
                            this.$set(
                                this.sign_role[index - 1],
                                "money_filters",
                                []
                            );
                        }
                        this.sign_role.forEach((rol, rol_index) => {
                            this.filter_roles.push(
                                JSON.parse(JSON.stringify(role))
                            );
                            if ("money_filters" in rol) {
                                if (rol.money_filters.length == 0) {
                                    rol.money_filters.push(role);
                                } else if (
                                    role.id -
                                        rol.money_filters[
                                            rol.money_filters.length - 1
                                        ].id ===
                                    1
                                ) {
                                    rol.money_filters.push(role);
                                }
                                rol.money_filters.forEach((mf) => {
                                    const col_value = ["id", "self_name"];
                                    Object.keys(mf).forEach((key) => {
                                        if (!col_value.includes(key)) {
                                            delete mf[key];
                                        }
                                    });
                                });
                            }
                        });
                    }
                });
                const set = new Set();
                this.filter_roles = this.filter_roles.filter((item) => {
                    return !set.has(item.id) ? set.add(item.id) : false;
                });
                this.filter_roles.forEach((filterItem, index) => {
                    filterItem.money_sort = 1;
                    if (
                        index !== 0 &&
                        this.filter_roles[index].id -
                            this.filter_roles[index - 1].id ===
                            1
                    ) {
                        this.filter_roles[index].money_sort =
                            this.filter_roles[index - 1].money_sort + 1;
                    }
                });
                this.sign_role = sign_role;
            } else if (
                //依選項選擇流程
                this.flow_sign_role !== null &&
                this.flow_sign_role[0].roles.some((role) => role.limit_dollar)
            ) {
                let dollar_col = this.forms[0].filter((form) => {
                    return (
                        form.id == this.flow_sign_role[0].roles[0].dollar_col_id
                    );
                });
                if (
                    !("value" in dollar_col[0]) ||
                    dollar_col[0].value == null
                ) {
                    dollar_col[0].value = 0;
                }
                //如果金額判斷有值
                let sign_role = this.flow_sign_role[0].roles.filter(
                    (role, index) => {
                        if (role.limit_dollar == false) return true;
                        if (
                            role.dollar <= dollar_col[0].value ||
                            !("dollar" in role)
                        ) {
                            return true;
                        } else {
                            if (
                                !(
                                    "dollar" in
                                    this.flow_sign_role[0].roles[index - 1]
                                ) ||
                                this.flow_sign_role[0].roles[index - 1]
                                    .limit_dollar == false
                            ) {
                                this.$set(
                                    this.flow_sign_role[0].roles[index - 1],
                                    "money_filters",
                                    []
                                );
                            }
                            // if (role.id -index === 1) {
                            //   role.money_sort + 1;
                            // }
                            this.filter_roles.push(
                                JSON.parse(JSON.stringify(role))
                            );
                            this.flow_sign_role[0].roles.forEach(
                                (rol, rol_index) => {
                                    if ("money_filters" in rol) {
                                        if (rol.money_filters.length == 0) {
                                            rol.money_filters.push(role);
                                        } else if (
                                            role.id -
                                                rol.money_filters[
                                                    rol.money_filters.length - 1
                                                ].id ===
                                            1
                                        ) {
                                            rol.money_filters.push(role);
                                        }
                                        rol.money_filters.forEach((mf) => {
                                            const col_value = [
                                                "id",
                                                "self_name",
                                            ];
                                            Object.keys(mf).forEach((key) => {
                                                if (!col_value.includes(key)) {
                                                    delete mf[key];
                                                }
                                            });
                                        });
                                    }
                                }
                            );
                        }
                    }
                );
                const set = new Set();
                this.filter_roles = this.filter_roles.filter((item) => {
                    return !set.has(item.id) ? set.add(item.id) : false;
                });
                this.filter_roles.forEach((filterItem, index) => {
                    filterItem.money_sort = 1;
                    if (
                        index !== 0 &&
                        this.filter_roles[index].id -
                            this.filter_roles[index - 1].id ===
                            1
                    ) {
                        this.filter_roles[index].money_sort =
                            this.filter_roles[index - 1].money_sort + 1;
                    }
                });
                this.flow_sign_role[0].roles = sign_role;
            }
            //清除欄位選項，寫進DB
            this.forms.forEach((form) => {
                form.forEach((col) => {
                    if (col.type == "employee" || col.type == "cascade") {
                        col.options = [];
                    }
                    if (col.type == "database") {
                        col.selected.data = [];
                        col.filteredDb = [];
                    }
                });
            });

            // 依據特定人員及金額跳過簽核關卡
            this.skipLevel();

            // 過濾自訂表單
            this.filterOutUnwantedRowFromCustomLists();

            let param = {
                id: this.id,
                layout_original_id: this.original_layout_id,
                forms: this.forms,
                is_child: this.parent_no == undefined ? false : true,
                parent: this.parent_no,
                sign_role: this.getRoles(),
                filter_roles: this.filter_roles,
                applicant: {
                    name: this.user.name,
                    dep: this.user.dep,
                    title: this.user.title,
                },
            };
            if (this.counterRoles.length > 0) {
                let roles = [];

                let employeesList = this.getEmployees;
                this.counterRoles.forEach(function (id, index) {
                    let employee = employeesList.filter((emp) => emp.id == id);
                    if (employee.length > 0) {
                        let role = {
                            role_id: employee[0].id,
                            self_name: employee[0].org_name,
                            rank: employee[0].org_id,
                            apply_status: 0,
                            child: false,
                            document: false,
                            countersigned: false,
                            to_counter: 1,
                        };
                        roles.push(role);
                    }
                });
                if (roles.length > 0) {
                    param.counterRoles = roles;
                }
            }
            this.btnDisabled = 1;
            axios
                .post(this.apiURL, param)
                .then((response) => {
                    if (response.data) {
                        window.demandCache
                            .del(this.original_layout_id)
                            .then(() => {
                                this.$refs.toast.add({
                                    severity: "success",
                                    summary: "申請成功",
                                    detail: this.messages,
                                    life: 3000,
                                });
                            })
                            .catch((e) => {
                                this.$refs.toast.add({
                                    severity: "error",
                                    summary: "刪除快取失敗",
                                    detail: e,
                                    life: 3000,
                                });
                            });
                        if ("action" in this.$parent) {
                            this.$parent.action = this.$parent.action - 1;
                        } else {
                            this.$root.currentTab = 1;
                        }
                    } else {
                        this.$refs.toast.add({
                            severity: "error",
                            summary: "申請失敗,請重新申請!",
                            detail: this.messages,
                            life: 3000,
                        });
                    }
                })
                .catch((error) => {
                    console.error(error);
                    this.$refs.toast.add({
                        severity: "error",
                        summary: "申請失敗,請重新申請",
                        detail: this.messages,
                        life: 3000,
                    });
                });
        },
        submitAgain() {
            //清除欄位選項，寫進DB
            this.forms.forEach((form) => {
                form.forEach((col) => {
                    if (col.type == "employee" || col.type == "cascade") {
                        col.options = [];
                    }
                    if (col.type == "database") {
                        col.selected.data = [];
                        col.filteredDb = [];
                    }
                });
            });
            let param = {
                demand_id: this.demand_id,
                layout_id: this.layout_id,
                forms: this.forms,
            };
            axios
                .post(this.apiURL + "/again", param)
                .then((response) => {
                    if (response.data) {
                        this.$parent.action = this.$parent.action - 2;
                        this.$parent.list = {};
                        this.$parent.dataLists = [];
                        this.$parent.getLists();
                        this.$refs.toast.add({
                            severity: "success",
                            summary: "申請成功",
                            detail: this.messages,
                            life: 3000,
                        });
                    } else {
                        this.$refs.toast.add({
                            severity: "error",
                            summary: "申請失敗,請重新申請!",
                            detail: this.messages,
                            life: 3000,
                        });
                    }
                })
                .catch((error) => {
                    console.error(error);
                    this.$refs.toast.add({
                        severity: "error",
                        summary: "申請失敗,請重新申請",
                        detail: this.messages,
                        life: 3000,
                    });
                });
        },
        getEmployeesDropDown() {
            axios
                .get("/api/demand/employees")
                .then((response) => {
                    this.getEmployees = response.data ?? [];
                })
                .catch((error) => {
                    console.error(error);
                });
        },
        totalCompute() {
            this.forms.map((form) => {
                return form.map((col, index) => {
                    if (col.type === "total") {
                        const col1Value = form.find(
                            (el) =>
                                el.id === this.settingForm[index].cols.col1.id
                        ).value;
                        const col2Value = form.find(
                            (el) =>
                                el.id === this.settingForm[index].cols.col2.id
                        ).value;
                        const computedValue = eval(
                            `${col1Value} ${col.compute} ${col2Value}`
                        );
                        col.value = col.parseInt
                            ? computedValue.toFixed(0)
                            : computedValue.toFixed(2);
                        return col;
                    }
                    return col;
                });
            });

            // this.settingForm.forEach((setting, settingIndex) => {
            //   this.forms.forEach((form, index) => {
            //     if (setting.type !== "total") return;
            //     let col1 = form.find((f) => f.id == form[settingIndex].cols.col1?.id);
            //     let col2 = form.find((f) => f.id == form[settingIndex].cols.col2?.id);
            //     form[settingIndex].cols.col1.value = col1.value;
            //     form[settingIndex].cols.col2.value = col2.value;
            //     if (setting.compute == "+" || setting.compute == "-") {
            //       var r1, r2, m, n;
            //       try {
            //         r1 = form[settingIndex].cols.col1.value
            //           .toString()
            //           .split(".")[1].length;
            //       } catch (e) {
            //         r1 = 0;
            //       }
            //       try {
            //         r2 = form[settingIndex].cols.col2.value
            //           .toString()
            //           .split(".")[1].length;
            //       } catch (e) {
            //         r2 = 0;
            //       }
            //       m = Math.pow(10, Math.max(r1, r2));
            //       n = r1 >= r2 ? r1 : r2;
            //     }
            //     if (setting.compute == "+") {
            //       form[settingIndex].value = Number(
            //         (
            //           ((form[settingIndex].cols.col1.value !== null
            //             ? form[settingIndex].cols.col1.value
            //             : 0) *
            //             m +
            //             (form[settingIndex].cols.col2.value !== null
            //               ? form[settingIndex].cols.col2.value
            //               : 0) *
            //               m) /
            //           m
            //         ).toFixed(n)
            //       );
            //     } else if (setting.compute == "-") {
            //       form[settingIndex].value = Number(
            //         (
            //           ((form[settingIndex].cols.col1.value !== null
            //             ? form[settingIndex].cols.col1.value
            //             : 0) *
            //             m -
            //             (form[settingIndex].cols.col2.value !== null
            //               ? form[settingIndex].cols.col2.value
            //               : 0) *
            //               m) /
            //           m
            //         ).toFixed(n)
            //       );
            //     } else if (setting.compute == "*") {
            //       var m = 0,
            //         s1 =
            //           form[settingIndex].cols.col1.value !== null
            //             ? form[settingIndex].cols.col1.value
            //             : 0,
            //         s2 =
            //           form[settingIndex].cols.col2.value !== null
            //             ? form[settingIndex].cols.col2.value
            //             : 0;
            //       // try {
            //       //   m += s1.split(".")[1].length;
            //       // } catch (e) {}
            //       // try {
            //       //   m += s2.split(".")[1].length;
            //       // } catch (e) {}
            //       // form[settingIndex].value =
            //       //   (Number(s1.replace(".", "")) * Number(s2.replace(".", ""))) /
            //       //   Math.pow(10, m);
            //       form[settingIndex].value = parseFloat(s1) * parseFloat(s2);
            //     } else if (setting.compute == "/") {
            //       var t1 = 0,
            //         t2 = 0,
            //         r1,
            //         r2;
            //       try {
            //         t1 = form[settingIndex].cols.col1.value
            //           .toString()
            //           .split(".")[1].length;
            //       } catch (e) {
            //         t1 = "0";
            //       }
            //       try {
            //         t2 = form[settingIndex].cols.col2.value
            //           .toString()
            //           .split(".")[1].length;
            //       } catch (e) {
            //         t2 = "0";
            //       }
            //       r1 =
            //         form[settingIndex].cols.col1.value !== null
            //           ? Number(
            //               form[settingIndex].cols.col1.value
            //                 .toString()
            //                 .replace(".", "")
            //             )
            //           : 0;
            //       r2 =
            //         form[settingIndex].cols.col2.value !== null
            //           ? Number(
            //               form[settingIndex].cols.col2.value
            //                 .toString()
            //                 .replace(".", "")
            //             )
            //           : 0;
            //       if (setting.parseInt == 0) {
            //         form[settingIndex].value = (
            //           (r1 / r2) *
            //           Math.pow(10, t2 - t1)
            //         ).toFixed(2);
            //       } else if (setting.parseInt == 1) {
            //         form[settingIndex].value = parseInt(
            //           (r1 / r2) * Math.pow(10, t2 - t1)
            //         );
            //       } else {
            //         form[settingIndex].value = (r1 / r2) * Math.pow(10, t2 - t1);
            //       }
            //     }
            //   });
            // });
        },
        reset() {
            this.forms.forEach((form) => {
                //清除表單內容
                form.forEach((col) => {
                    if (col.type == "date") {
                        if (col.date.selected !== "auto") {
                            if (col.preset) {
                                this.$set(
                                    col,
                                    "value",
                                    JSON.parse(JSON.stringify(col.preset))
                                );
                            } else {
                                this.$set(col, "value", null);
                            }
                        }
                    } else if (col.type == "document") {
                        this.$set(col, "files", []);
                    } else if (col.type == "list") {
                        if (col.default) {
                            this.$set(
                                col,
                                "list",
                                JSON.parse(JSON.stringify(col.default))
                            );
                        }
                    } else if (col.type == "customList") {
                        if (col.default) {
                            this.$set(
                                col,
                                "custom_list",
                                JSON.parse(JSON.stringify(col.default))
                            );
                        }
                    } else {
                        if (col.preset) {
                            this.$set(
                                col,
                                "value",
                                JSON.parse(JSON.stringify(col.default))
                            );
                        } else {
                            this.$set(col, "value", null);
                        }
                    }
                });
                // 清除自行指定關卡
                if (
                    "list" in this.$parent
                        ? this.$parent.list.status !== 7
                        : this.displaySignRole
                ) {
                    this.resetLevel();
                }
                //清除會簽人員
                this.counterRoles = [];
                this.displayCounterRoles = [];
            });
        },
        formHeader(row) {
            if (this.mode == "signing") {
                if (
                    this.signer !== undefined &&
                    this.signer.fill_column_list_ids.includes(row.id) &&
                    row.mode == 2 &&
                    row.is_must
                ) {
                    return row.name + "*";
                } else {
                    return row.name;
                }
            } else {
                if (row.mode == 1 && row.is_must) {
                    return row.name + "*";
                } else {
                    return row.name;
                }
            }
        },
        formatInOrderItems(items) {
            let newItems = {};
            if ("name" in items) {
                newItems.name = items.name;
            }
            if ("count" in items) {
                newItems.count = items.count;
            }
            if ("unit" in items) {
                newItems.unit = items.unit;
            }
            if ("price" in items) {
                newItems.price = items.price;
            }
            if ("total" in items) {
                newItems.total = items.total;
            }
            if ("memo" in items) {
                newItems.memo = items.memo;
            }
            return newItems;
        },
        formatInOrderItemsWithProto(items, proto) {
            let newItems = {};
            if ("name" in proto) {
                newItems.name = proto.name;
            }
            if ("count" in proto) {
                newItems.count = proto.count;
            }
            if ("unit" in proto) {
                newItems.unit = proto.unit;
            }
            if ("price" in proto) {
                newItems.price = proto.price;
            }
            if ("total" in proto) {
                newItems.total = proto.total;
            }
            if ("memo" in proto) {
                newItems.memo = proto.memo;
            }

            return { ...newItems, ...items };
        },
        multiSummary(item) {
            if (item.value) {
                if (item.value instanceof Array) {
                    return item.value.reduce((acc, cur, index) => {
                        if (index === 0) {
                            return acc + cur.name.toString();
                        }
                        return acc + "、" + cur.name.toString();
                    }, "");
                }
                if (item.value instanceof Object) {
                    return item.value.name;
                }

                return "";
            }
            return "";
        },
        thousands(value) {
            if (value) {
                value += "";
                var arr = value.split(".");
                var re = /(\d{1,3})(?=(\d{3})+$)/g;

                return (
                    arr[0].replace(re, "$1,") +
                    (arr.length == 2 ? "." + arr[1] : "")
                );
            } else {
                return "";
            }
        },
        formatDate(timeValue) {
            const turnMonthIntoNo = (str) => {
                switch (str) {
                    case "Jan":
                        return "01";
                    case "Feb":
                        return "02";
                    case "Mar":
                        return "03";
                    case "Apr":
                        return "04";
                    case "May":
                        return "05";
                    case "Jun":
                        return "06";
                    case "Jul":
                        return "07";
                    case "Aug":
                        return "08";
                    case "Sep":
                        return "09";
                    case "Oct":
                        return "10";
                    case "Nov":
                        return "11";
                    case "Dec":
                        return "12";
                    default:
                        break;
                }
            };
            const turnWeekIntoCN = (str) => {
                switch (str) {
                    case "Mon":
                        return "一";
                    case "Tue":
                        return "二";
                    case "Wed":
                        return "三";
                    case "Thu":
                        return "四";
                    case "Fri":
                        return "五";
                    case "Sat":
                        return "六";
                    case "Sun":
                        return "日";
                    default:
                        break;
                }
            };
            const year = timeValue.toString().slice(11, 15);
            const month = turnMonthIntoNo(timeValue.toString().slice(4, 7));
            // const month = timeValue.toString().slice(4,7)
            const day = timeValue.toString().slice(8, 10);
            const week = turnWeekIntoCN(timeValue.toString().slice(0, 3));
            return year + "/" + month + "/" + day + " (" + week + ")";
        },
        formatTime(timeValue) {
            const date = new Date(timeValue);
            let hours = date.getHours();
            let minutes = date.getMinutes();
            const ampm = hours >= 12 ? "PM" : "AM";
            hours = hours % 12;
            hours = hours ? hours : 12;
            hours = hours < 10 ? "0" + hours : hours;
            minutes = minutes < 10 ? "0" + minutes : minutes;

            return hours + ":" + minutes + " " + ampm;
        },
        filterOutUnwantedRowFromCustomLists() {
            this.forms = this.forms.map((form) =>
                form.map((col) => {
                    if (
                        col.type == "customList" &&
                        col.form_setting?.some((el) => el.filterCriteria)
                    ) {
                        // 如果有審核人需要填寫的過濾條件，就先不能過濾。
                        if (
                            col.form_setting.some(
                                (setting) =>
                                    setting.mode == 2 && setting.filterCriteria
                            )
                        ) {
                            return col;
                        }
                        // 濾掉 mode = 0,1 且 filterCriteria = true 但都無內容的 row
                        col.custom_list = col.custom_list.filter((row) => {
                            return row.some((el, index) => {
                                if (col.form_setting[index].filterCriteria) {
                                    return el;
                                }
                            });
                        });
                    }
                    return col;
                })
            );
        },
        // 依據特定人員及金額跳過簽核關卡
        skipLevel() {
            const roles = this.getRoles();
            roles.forEach((role, index) => {
                if (role.skip_level_list) {
                    let isSkip = false;
                    role.skip_level_list.some((skip) => {
                        skip.employee_ids.some((employee_id) => {
                            if (
                                index > 0 &&
                                this.forms[0].find(
                                    (col) => col.id == role.dollar_col_id
                                ).value <= skip.money && // 欄位金額小於在 skip.money 以下。
                                employee_id === roles[index - 1].role_id
                            ) {
                                this.$set(role, "isRemoved", true);
                                isSkip = true;
                                return true;
                            }
                        });
                        if (isSkip) return true;
                        return false;
                    });
                }
            });
        },

        hasHtmlTag(str) {
            // 只檢查是否包含有效的<a>標籤
            // 排除純文字中的 < > 符號
            const regex = /<\/?[a-zA-Z][a-zA-Z0-9]*(\s[^>]*)?>/;
            return regex.test(str);
        },
    },
    computed: {
        displaySignRole() {
            if (
                this.choose !== null &&
                this.choose.id == this.sign_role[0].col_id &&
                "value" in this.choose
            ) {
                this.flow_sign_role = this.sign_role.filter((role) => {
                    let chooseId;
                    if (this.choose.type == "cascade") {
                        if (!this.choose.value) return false;
                        const selectedOption = this.choose.options.find(
                            (option) =>
                                option.name == this.choose.value.split(",")[0]
                        );
                        if (!selectedOption) return false;
                        chooseId =
                            selectedOption.id +
                            (this.choose.value.split(",")[1] || "");
                    } else {
                        chooseId = this.choose.value?.id;
                    }

                    // 跟流程ID相同
                    const cond1 = chooseId == role.id;
                    // 跟共用流程ID相同
                    const cond2 = role.option_ids.includes(chooseId);

                    return cond1 || cond2;
                });
                return this.flow_sign_role[0]?.roles.some(
                    (rank) => rank.self_applicant == true
                );
            } else {
                return this.sign_role.some(
                    (rank) => rank.self_applicant == true
                );
            }
        },
        flowCol() {
            return this.settingForm.find(
                (el) => el.id == this.sign_role[0].col_id
            );
        },
        settingFormNoValue() {
            if (this.settingForm.length > 0) {
                return JSON.parse(JSON.stringify(this.settingForm)).map(
                    (form) => {
                        return {
                            id: form.id,
                            name: form.name,
                            type: form.type,
                            must: form.must || null,
                            date: form.type == "date" ? form.date : null,
                            data:
                                form.type == "database"
                                    ? form.selected.data
                                    : null,
                            preset: "preset" in form ? form.preset : null,
                            cols: form.type == "total" ? form.cols : null,
                            options:
                                form.type == "dropdown" ||
                                form.type == "single" ||
                                form.type == "multi" ||
                                form.type == "cascade"
                                    ? form.options
                                    : null,
                            // custom_list is sort of value as well
                            // custom_list:
                            //     form.type == "customList"
                            //         ? form.custom_list
                            //         : null,
                            form_setting:
                                form.type == "customList"
                                    ? form.form_setting
                                    : null,
                            can_insert:
                                form.type == "customList"
                                    ? form.can_insert
                                    : null,
                        };
                    }
                );
            }
        },
        totalAmountForPreview() {
            return this.settingForm.map((el, col_index) => {
                if (el.type == "money") {
                    const sum = this.forms.reduce((acc, form) => {
                        return acc + form[col_index].value;
                    }, 0);
                    return sum;
                }
                return 0;
            });
        },
        remarksWithoutReviewer() {
            return this.remarks.filter((i) => i.recipient !== "審核人");
        },
        displayResumeLevelButton() {
            const roles = this.getRoles();
            const showButton = roles.some((role) => role.isRemoved);
            return showButton;
        },
        filteredForms: {
            cache: false,
            get() {
                let newForms = this.forms.map((form) =>
                    form.map((col) => {
                        if (
                            col.type == "customList" &&
                            col.form_setting?.some((el) => el.filterCriteria)
                        ) {
                            // 如果有審核人需要填寫的過濾條件，就先不能過濾。
                            if (
                                col.form_setting.some(
                                    (setting) =>
                                        setting.mode == 2 &&
                                        setting.filterCriteria
                                )
                            ) {
                                return col;
                            }
                            // 濾掉 mode = 0,1 且 filterCriteria = true 但都無內容的 row
                            let newCol = JSON.parse(JSON.stringify(col));
                            newCol.custom_list = col.custom_list.filter(
                                (row) => {
                                    return row.some((el, index) => {
                                        if (
                                            col.form_setting[index]
                                                .filterCriteria
                                        ) {
                                            return el;
                                        }
                                    });
                                }
                            );
                            return newCol;
                        }
                        return col;
                    })
                );

                newForms = newForms.map((form) =>
                    form.filter(
                        (col) => col.type == "list" || col.type == "customList"
                    )
                );
                return newForms;
            },
        },
    },
};
</script>
<style>
.import .p-dialog-content {
    height: 25rem;
}

.heightDialog .p-dialog-content {
    height: 28rem;
}

.preview .p-dialog-header {
    background-color: rgb(250, 251, 253) !important;
    padding-top: 16px !important;
    padding-bottom: 16px !important;
}

.preview > .p-dialog-content {
    background-color: rgb(250, 251, 253) !important;
    max-height: 75vh;
    scroll-behavior: smooth;
}

.min-width-200 {
    min-width: 200px;
}

.min-width-56 {
    min-width: 56px;
    max-width: 56px;
}

.preview .p-card-content {
    padding-top: 8px !important;
    padding-bottom: 0px !important;
}

.preview .p-dialog {
    overflow: hidden;
}
</style>
<style scoped>
.preview th,
.preview td {
    padding: 8px;
}

.preview td > div {
    min-width: 5rem;
    max-width: 20rem;
    width: max-content;
}
</style>
