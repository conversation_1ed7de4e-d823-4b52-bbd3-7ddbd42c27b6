<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>@yield('title', '標題名稱')</title>
    @vite(['resources/css/app.css', 'resources/js/app.js'])

    <!-- Styles -->
    <style>
        html,
        body {
            background-color: #fff;
            color: #4D4D4D;
            font-family: Noto Sans TC;
            font-weight: 500;
            height: 100vh;
            margin: 0;
            word-wrap: break-word;
            word-break: break-all;
        }

        .full-height {
            height: 100vh;
        }

        .flex-center {
            align-items: center;
            display: flex;
            justify-content: center;
        }

        .position-ref {
            position: relative;
        }

        .top-right {
            position: absolute;
            right: 10px;
            top: 18px;
        }

        .content {
            text-align: center;
        }

        .title {
            font-size: 84px;
        }

        .links>a {
            color: #636b6f;
            padding: 0 25px;
            font-size: 13px;
            font-weight: 600;
            letter-spacing: .1rem;
            text-decoration: none;
            text-transform: uppercase;
        }

        .m-b-md {
            margin-bottom: 30px;
        }

        #sidebar::-webkit-scrollbar {
            width: 10px;
        }

        #sidebar::-webkit-scrollbar-thumb {
            background: #b9b9b9;
        }

        #sidebar::-webkit-scrollbar-track {
            background: #d9d9d9;
        }



        @media print {
            * {
                -webkit-print-color-adjust: exact !important;
            }

            .noPrint {
                display: none !important;
            }
        }

        /*  radiobutton  dropdown */
        .p-invalid>.p-inputtext,
        .p-invalid.p-inputtext,
        .p-invalid.p-radiobutton-box,
        .p-invalid.p-dropdown,
        .p-invalid.p-cascadeselect,
        .p-invalid.p-multiselect {
            border-width: 2px !important;
        }

        @media screen and (min-width: 1341px) {

            .p-invalid>.p-inputtext,
            .p-invalid.p-inputtext,
            .p-invalid.p-radiobutton-box,
            .p-invalid.p-dropdown,
            .p-invalid.p-cascadeselect,
            .p-invalid.p-multiselect {
                border-width: 3px !important;
            }
        }

        [v-cloak] {
            display: none;
        }
    </style>
</head>

<body>
    <div style="max-width:1920px" class="h-full flex flex-col lg:flex-row mx-0 lg:mx-auto">
        <div id="app" v-cloak>
            <div v-if="sideMenuActive == 1" @click="sideMenuActive = 0" class="fixed h-screen top-0 left-0 w-full z-30">
            </div>
            <span @click="sideMenuActive = 1" class="cursor-pointer bg-primary fixed hidden lg:block"
                style="top:50%;left:0px;">
                <i class="fas fa-bars text-white text-2xl"></i>
            </span>
            <div id="sidebar" style="transition: 0.5s;"
                class="w-64 bg-primary h-screen z-40 fixed top-0 noPrint overflow-y-scroll overscroll-none"
                :class="sideMenuActive == 1 ? 'left-0' : '-left-64'">
                <div class="w-full text-center py-12 relative">
                    <div class="cursor-pointer" @click="showOptions=!showOptions">
                        <p class="text-white font-medium text-2xl inline-block">@yield('name', '系統名稱')</p>
                        <i class="fas fa-sm inline-block text-white ml-2"
                            :class="showOptions ? 'fa-chevron-up' : 'fa-chevron-down'"></i>
                    </div>
                    <div class="bg-white rounded w-48 absolute right-8 z-30" v-show="showOptions">
                        <ul>
                            <li onclick="javascript:location.href = '/sso/hrap';"
                                class="hover:bg-primary hover:bg-opacity-25 py-2 cursor-pointer">人資系統</li>
                            <li onclick="javascript:location.href = '/demand/list';"
                                class="hover:bg-primary hover:bg-opacity-25 py-2 cursor-pointer bg-primary bg-opacity-25">
                                @yield('name', '系統名稱')</li>
                            <li onclick="javascript:window.open('/sso/acc');"
                                class="hover:bg-primary hover:bg-opacity-25 py-2 cursor-pointer">財務系統</li>
                            <li onclick="javascript:location.href = '/sso/pmap';"
                                class="hover:bg-blue-700 hover:bg-opacity-25 py-2 cursor-pointer">
                                專案管理(測試)
                            </li>
                        </ul>
                    </div>
                    <!-- <select class="text-white focus:text-black inline-block bg-transparent font-medium text-2xl" onchange="javascript:location.href = this.value;">
                            <option value="/demand/list" selected>@yield('name', '系統名稱')　</option>
                            <option value="/sso/hrap">人資系統　</option>
                        </select> -->
                </div>
                <nav>
                    <div v-for="(item,item_index) in funcMenu" :key="item_index">
                        <a :href="item.url" @click="item.drop == 1 ? item.drop = 0 : item.drop = 1;"
                            class="menuList" :class="item.active || item.drop ? 'opacity-100' : 'opacity-50'">
                            <span v-if="item.name=='需求單'">
                                <i class="fa-solid fa-table-list"></i>
                            </span>
                            <span v-else-if="item.name=='公設預約'">
                                <i class="fa-solid fa-clipboard-list"></i>
                            </span>
                            <span v-else>
                                <i class="fa-solid fa-table-list"></i>
                            </span>
                            <p class="inline-block mx-4">@{{ item['name'] }}</p>
                            <span v-if="('dropDown' in item)" class="float-right">
                                <i class="fas" :class="item.drop ? 'fa-chevron-up' : 'fa-chevron-down'"></i>
                            </span>
                        </a>
                        <div v-if="funcMenu[item_index].drop == 1">
                            <div v-for="(itemList, itemList_index) in item.dropDown" :key="itemList_index">
                                <a :href="itemList.url"
                                    @click="itemList.drop == 1 ? itemList.drop = 0 : itemList.drop = 1;"
                                    class="dropList apply-list"
                                    :class="itemList.active || itemList.drop ? 'opacity-100 list' : 'opacity-50'">
                                    <p class="ml-16">@{{ itemList['name'] }}</p>
                                    <span v-if="('dropDown' in itemList)" class="float-right">
                                        <i class="fas"
                                            :class="funcMenu[item_index].dropDown[itemList_index].drop == 0 ?
                                                'fa-chevron-down' : 'fa-chevron-up'"></i>
                                    </span>
                                </a>
                                <div
                                    v-if="funcMenu[item_index].drop == 1 && funcMenu[item_index].dropDown[itemList_index].drop == 1">
                                    <div v-for="(listList, listList_index) in itemList.dropDown"
                                        :key="listList_index">
                                        <a :href="listList.url"
                                            @click="listList.drop == 1 ? listList.drop = 0 : listList.drop = 1;"
                                            class="dropList apply-list"
                                            :class="listList.active ? 'opacity-100 list' : 'opacity-50'">
                                            <p class="ml-20">@{{ listList['name'] }}</p>
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </nav>
            </div>
            <!-- 手機版HEADER -->
            <div v-if="isShow !== ''" @click="isShow = ''" class="fixed h-screen top-0 left-0 w-full z-30 lg:z-auto">
            </div>
            <nav class="bg-primary fixed w-full px-5 py-5 justify-center text-white flex lg:hidden z-30 noPrint">
                <div class="flex justify-between w-full">
                    <span @click="sideMenuActive = 1" class="cursor-pointer">
                        <i class="fas fa-bars text-white text-xl"></i>
                    </span>
                    <p class="text-white text-base">@yield('name', '需求單系統')</p>
                    <div>
                        <!-- 員工資料 -->
                        <span @click="showPerson" class="relative mr-6">
                            <button><i class="far fa-user-circle text-white text-xl"></i></button>
                            <div @click.stop v-if="isShow == 'person'" class="absolute top-9 -right-14 z-40">
                                <div class="speech-bubble relative p-4 md:p-8 bg-white rounded-lg shadow">
                                    <div class="block md:flex justify-between">
                                        <div class="flex">
                                            <img class="rounded-full mr-5 w-24"
                                                :src="user == null ? '' : user.photo" />
                                            <div class="my-auto">
                                                <span
                                                    class="text-xl font-semibold text-black">@{{ user == null ? '' : user.name }}</span>
                                                <br />
                                                <p class="text-sm text-gray-400 text-black">@{{ user == null ? '' : user.title }}</p>
                                            </div>
                                        </div>
                                        <div>
                                            <a href="/"><button
                                                    class="w-28 bg-primary px-2 py-2 rounded">登出</button></a>
                                        </div>
                                    </div>
                                    <div class="flex mt-6">
                                        <div class="block md:flex w-full md:w-4/5">
                                            <div class="w-full md:w-1/4 p-auto md:pr-5">
                                                <span class="text-sm text-gray-400">
                                                    員工編號
                                                </span>
                                                <br />
                                                <p class="text-black">@{{ user == null ? '' : user.no }}</p>
                                            </div>
                                            <span class="hidden md:block text-3xl text-gray-100">｜</span>
                                            <div class="w-full md:w-1/4 p-auto md:px-5">
                                                <span class="text-sm text-gray-400">
                                                    部門
                                                </span>
                                                <br />
                                                <p class="text-black">@{{ user == null ? '' : user.dep }}</p>
                                            </div>
                                            <span class="hidden md:block text-3xl text-gray-100">｜</span>
                                            <div class="flex w-full md:w-1/4 p-auto md:px-5">
                                                <div>
                                                    <span class="text-sm text-gray-400">
                                                        代理人
                                                    </span>
                                                    <span @click.stop="editAgentModal = true">
                                                        <i class="fas fa-pencil-alt text-black"></i>
                                                    </span>
                                                    <br />
                                                    <p class="text-black">@{{ user == null ? '' : user.agent }}</p>
                                                </div>
                                                <!-- <div>
                                                        <span>
                                                            代理人代簽核
                                                        </span>
                                                        <br/>
                                                        <div>
                                                            <InputSwitch @change="agentSign" v-model="user==null ? '' : user.agentSign" />
                                                        </div>
                                                    </div> -->
                                            </div>
                                            <span class="hidden md:block text-3xl text-gray-100">｜</span>
                                            <div class="flex w-full md:w-1/4 p-auto md:px-5">
                                                <div>
                                                    <span class="text-sm text-gray-400">
                                                        直屬主管
                                                    </span>
                                                    <br />
                                                    <p class="text-black">@{{ user == null ? '' : user.manager }}</p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </span>


                        <!-- 通知提醒 -->
                        <span @click="showNotify" class="relative">
                            <span v-if="notifies.length > 0"
                                class="absolute -right-2 -top-2 align-top text-white text-lg">•</span>
                            <button><i class="far fa-bell text-white text-xl"></i></button>
                            <div @click.stop v-if="isShow == 'notify'" class="absolute top-9 -right-4 z-40">
                                <div class="speech-bubble2 relative bg-white rounded-lg shadow z-10">
                                    <div class="flex justify-between pt-8 px-8">
                                        <p class="font-bold text-2xl text-black">通知</p>
                                        <button @click="clearNotify()" class="text-base text-black underline mt-auto">
                                            清除通知
                                        </button>
                                    </div>
                                    <div v-if="notifies.length == 0" class="py-6 px-8 text-center text-black">
                                        目前無通知
                                    </div>
                                    <div v-else class="h-96 px-8 overflow-y-auto">
                                        <div v-for="(item, item_index) in notifies" :key="item_index">
                                            <div class="py-6 flex">
                                                <div class="w-5">
                                                    <p v-if="!item.read" class="w-3 text-primary text-xl">
                                                        •
                                                    </p>
                                                </div>
                                                <a @click="putNotify(item.id)" class="hover:underline text-black"
                                                    :href="item.url">@{{ item.content }}</a>
                                            </div>
                                            <hr class="text-black" />
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </span>
                    </div>
                </div>
            </nav>
        </div>
        <div style="background: rgb(250, 251, 253);" id="content" class="w-full flex-grow">
            <div class="sticky top-16 lg:top-0 z-20">
                <!-- 選單BANNER -->
                @yield('banner')
            </div>
            <div style="background: #FAFBFD;" v-cloak>
                <main class="mb-0 pt-20 pb-8 lg:pt-8 px-8">
                    @yield('content')
                </main>
                @yield('count-banner')
            </div>
        </div>
    </div>
    <script type="text/javascript">
        const functionMenu = {!! json_encode(Session::get('funcMenu')) !!};
        const isDevelopment = @json(!config('app.debug'));
    </script>
    @yield('js')
</body>

</html>
