<?php

namespace App\Modules\Demand\Controllers;


use Illuminate\Http\Request;
use Illuminate\Support\Facades\Session;
use App\Http\Controllers\Controller;
use App\Modules\Demand\Models\DemandGroup;
use App\Modules\Demand\Models\DemandLayout;
use App\Modules\Demand\Models\ListLayout;
use App\Modules\Demand\Models\Code;
use App\Modules\Demand\Models\DataAuth;
use Illuminate\Support\Facades\Log;

class DemandGroupController extends Controller
{
    protected $company_id;
    protected $user_id;

    public function __construct()
    {
        $this->company_id = Session::get('CompanyId');
        $this->user_id = Session::get('employee_id');
    }

    public function fetchGroupSetting()
    {
        $groups = DemandGroup::with(
            'layouts:group_id,demand_layouts.id,demand_layouts.name,open,can_multi,sign_role,payload->columns as columns,payload->sort as sort,payload->flow_setting_type as flow_setting_type'
        )
            ->where('company_id', $this->company_id)
            ->select('id', 'name')
            ->orderBy('id', 'asc')
            ->get();
        if (!$groups) {
            return [];
        }
        $getCodeTable = Code::where('code_kind', 'AE')->where('code_id', 'column')->first();
        return $groups->each(function ($group) use ($getCodeTable) {

            $group->layouts->each(function ($layout) use ($getCodeTable) {
                $layout->columns = json_decode($layout->columns);
                // 欄位審核人必填
                $auditMusts = collect();
                collect($layout->columns)->where('audit_must', true)->each(function ($must) use (&$auditMusts, $getCodeTable) {
                    $columnName = $getCodeTable ? $getCodeTable->nm_zh_tw : '';
                    $auditMusts->push(
                        [
                            'name' => $columnName . $must->id . ' ' . $must->name,
                            'id' => $must->id
                        ]
                    );
                });
                $layout->audit_musts = $auditMusts;

                // 自訂表單審核人必填
                $listMust = collect();
                collect($layout->columns)->where('type', 'customList')
                    ->each(function ($col, $colIndex) use (&$listMust) {
                        if (!isset($col->form_setting)) return;
                        collect($col->form_setting)->each(function ($set, $setIndex) use (&$listMust, $col, $colIndex) {
                            if ($set->mode == 2)
                                $listMust->push(
                                    [
                                        'name' => $col->name . '-' . $set->name,
                                        'column_id' => $colIndex,
                                        // 'id' => $colIndex . '-' . $setIndex
                                        'id' => $set->id
                                    ]
                                );
                        });
                    });
                $layout->list_must = $listMust;

                $layout->sort = isset($layout['sort']) ? intval($layout->sort) : null;
                $layout->cols = $layout['columns'];
                $layout->flow_setting_type = isset($layout['flow_setting_type']) ? (int)$layout->flow_setting_type : 0;
            });
            $authIds = DataAuth::whereJsonContains('payload->user_list', (int)$this->user_id)->get()->pluck('layout_id');

            $group->layouts->makeHidden(['group_id', 'columns']);
            $sortLayouts = $group->layouts->filter(function ($layout, $key) use ($authIds) {
                return in_array($layout->id, $authIds->all());
            })->sortBy('id')->values()->all();
            unset($group->layouts);
            $group->layouts = $sortLayouts;
        });
    }

    public function createGroup(Request $request)
    {
        if ($request->get('name')) {
            $GroupModel = DemandGroup::create(
                [
                    'name' => $request->get('name'),
                    'company_id' => $this->company_id,
                    'created_by' => $this->user_id,
                    'updated_by' => $this->user_id
                ]
            );
            return $GroupModel->id;
        }
        return 0;
    }

    public function updateGroup(Request $request)
    {
        if ($request->get('id') && $request->get('name')) {

            $group = DemandGroup::where('id', $request->get('id'))->first();
            $group->setAttribute('name', $request->get('name'))
                ->setAttribute('updated_by', $this->user_id);
            $group->save();
            return 1;
        }
        return 0;
    }

    //** 刪除類別 會把子layouts全部刪掉 */
    public function deleteGroups(Request $request)
    {
        $id = $request['id'];
        if ($id) {

            $group = DemandGroup::where('id', $id)->first();
            $layouts = DemandLayout::where('group_id', $id)->get();
            if (!$group || $layouts->count() > 0) {
                return [0, '還有表單狀態無法刪除'];
            }
            $group->delete();

            $layouts = DemandLayout::where('group_id', $id)->get();

            $layouts->each(function ($layout) {
                return $layout->delete();
            });

            $lists = ListLayout::whereIn('demand_layout_id', $layouts->pluck('id'))->get();
            $lists->each(function ($list) {
                return $list->delete();
            });

            return [1];
        }
        return [0, '刪除失敗'];
    }
}
