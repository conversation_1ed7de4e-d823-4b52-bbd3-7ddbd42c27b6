<?php

namespace App\Jobs;

use App\Modules\Demand\Models\CalendarSetting;
use App\Modules\Demand\Models\Company;
use App\Modules\Demand\Models\DemandSetting;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Session;

class DemandNotify implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    // protected $tz = 'Asia/Taipei';
    protected $companies;
    protected $demandSetting;
    protected $calendarSettings;
    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->companies =Company::all();
        $this->calendarSettings =CalendarSetting::
            // where('payload->is_holiday', true)
            where('payload->year', (string)now()->year)
            ->where('payload->date', '<=', now('Asia/Taipei')->toisostring())
            ->get();
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        Log::info('notify start');
        $this->demandNotify();

        Log::info('notify end');
    }



    private function demandNotify()
    {
        $companies = $this->companies;
        $demandSetting =DemandSetting::where('payload->noticeSend', true)->where('payload->company_id', '<>', 0)->get();
        $demandSetting->each(function ($item, $key)  use ($companies) {
            $msg=$item->payload->get('msg');
            $company_id = $item->payload->get('company_id');
            $noticeFrequency = $item->payload['noticeFrequency'];
            $noitifyLastDate = isset($companies->where('id', $company_id)->first()->metadata['lastNotificationDate']['demandSignOff'])?$companies->where('id', $company_id)->first()->metadata['lastNotificationDate']['demandSignOff']:now()->addDays(-1) ;
            // 上次執行到今天有多少個國定假日
            $nationalHoliday = $this->nationalHoliday($company_id, $noitifyLastDate);
            // 計算上次執行日期到今天的天數 - 國定假日 - 周休二日
            // diffInWeekdays頭尾日期都會算所以要-1
            $weekDay = Carbon::parse($noitifyLastDate)->diffInWeekdays(now()) - 1 - $nationalHoliday;
            if ($weekDay >= 0 && $weekDay % $noticeFrequency == 0){
                CheckSignOffNotification::dispatch($company_id,$msg);
            }
        });
    }

    private function nationalHoliday($companyId, $noitifyLastDate)
    {
        $holiday =0;
        $this->calendarSettings->each(function ($value) use ($companyId, $noitifyLastDate, &$holiday) {
            if ($value->payload['company_id'] !=  $companyId) return ;
            if ($value->payload['date'] <  $noitifyLastDate) return ;

            if ($value->payload['is_holiday'] && Carbon::parse($value->payload['date'])->setTimezone('asia/taipei')->isWeekend())
            //假日且放假 跳過
                return ;
            else if($value->payload['is_holiday'])
                // 平日放假
                $holiday++;
            else if(Carbon::parse($value->payload['date'])->setTimezone('asia/taipei')->isWeekend())
                //假日上班
                $holiday--;
        });

        return $holiday;
    }
}
