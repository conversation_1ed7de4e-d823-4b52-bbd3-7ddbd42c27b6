{"name": "laravel/laravel", "type": "project", "description": "The Laravel Framework.", "keywords": ["framework", "laravel"], "license": "MIT", "require": {"php": "^8.2", "aspose-cloud/aspose-words-cloud": "^22.10", "barryvdh/laravel-dompdf": "^2.0", "doctrine/dbal": "^3.0", "dompdf/dompdf": "^2.0", "google/cloud-document-ai": "^1.8", "gotenberg/gotenberg-php": "^2.12", "guzzlehttp/guzzle": "^7.5", "intervention/image": "^2.7", "laravel/framework": "^10.0", "laravel/legacy-factories": "^1.4", "laravel/tinker": "^2.0", "phpoffice/phpspreadsheet": "1.*", "phpoffice/phpword": "^0.18.3", "rap2hpoutre/fast-excel": "^5.5", "sabre/vobject": "^4.5", "staudenmeir/eloquent-json-relations": "^1.1", "symfony/psr-http-message-bridge": "^7.2"}, "require-dev": {"barryvdh/laravel-debugbar": "^3.6", "filp/whoops": "^2.0", "mockery/mockery": "^1.0", "nunomaduro/collision": "^7.0", "phpunit/phpunit": "^10.0"}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "platform-check": false, "allow-plugins": {"php-http/discovery": true}}, "extra": {"laravel": {"dont-discover": []}}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "minimum-stability": "stable", "prefer-stable": true, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"]}}