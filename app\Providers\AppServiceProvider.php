<?php

namespace App\Providers;

use Illuminate\Support\Collection;
use App\Modules\Demand\Models\Demand;
use App\Observers\demandObserver;
use Illuminate\Support\ServiceProvider;


class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     *
     * @return void
     */
    public function register()
    {
        //
    }

    /**
     * Bootstrap any application services.
     *
     * @return void
     */
    public function boot()
    {
        // 計算這個時段的所有預約設備數量
        Collection::macro('sumReservesAmount', function ($timeId) {

            return  $this->filter(function ($value) use ($timeId) {
                return in_array($timeId, $value->payload['time']);
            })->sum(function ($item) {
                return $item->payload['amount'];
            });
        });

        // 檢查所有已預約的設施，是否存在這個時段
        Collection::macro('isTimeinReserves', function ($timeId) {
            return  $this->filter(function ($value) use ($timeId) {
                return in_array($timeId, $value->payload['time']);
            })
                ->isNotEmpty();
        });
        // sql紀錄 查看用
        // \DB::listen(function($query) {
        //     \File::append(
        //         storage_path('/logs/query.log'),
        //         '[' . date('Y-m-d H:i:s') . ']' . PHP_EOL . $query->sql . ' [' . implode(', ', $query->bindings) . ']' . PHP_EOL . PHP_EOL
        //     );
        // });

        // 找到相對應位置的customlist
        Collection::macro('findList', function ($form_index, $column_index) {
            return  $this->where('form_index', $form_index)
                ->where('column_index', $column_index)
                ->first();
        });
        Demand::observe(demandObserver::class);
    }
}
