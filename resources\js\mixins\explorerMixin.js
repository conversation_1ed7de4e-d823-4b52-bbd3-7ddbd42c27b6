export const explorerMixin = {
    methods: {
        // 將資料轉為樹狀結構
        formatToTree(data) {
            let treeMapList = data.reduce((acc, curr) => {
                if (curr.explorerId) {
                    acc[curr["explorerId"]] = curr;
                } else {
                    acc[curr["id"]] = curr;
                }
                return acc;
            }, {});
            let result = data.reduce((acc, curr) => {
                let pid = curr.parentId;
                let parent = treeMapList[pid];
                if (parent) {
                    parent.children
                        ? parent.children.push(curr)
                        : (parent.children = [curr]);
                } else if (!pid) {
                    acc.push(curr);
                }
                return acc;
            }, []);
            return result;
        },
    },
};
