<?php

namespace App\Modules\Demand\Middleware;


use Closure;
use Config;
use Illuminate\Support\Facades\Session;
use App\Modules\Demand\Models\Employee;
use App\Modules\Demand\Models\Company;
use App\Modules\Demand\Models\FuncAuth;
use Illuminate\Support\Facades\Log;

class CheckLogin
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle($request, Closure $next)
    {

        //判斷是否有登入
        if (!Session::has('employee_id')) {
            return redirect('/');
        }
        if (!Session::has('funcMenu')) {
            $funcAuth = FuncAuth::where('company_id', Session::get('CompanyId'))->get();
            if (!$funcAuth) {
                return redirect('/');
            }

            $demand = $funcAuth->firstwhere('type', 'demand');
            $demandQuery = $funcAuth->firstwhere('type', 'demand_query');
            $par = $funcAuth->firstwhere('type', 'par');
            $explorer = $funcAuth->firstwhere('type', 'explorer');
            $isDebug = Config::get('app.debug');

            if ($demand) {
                $demandAuth = $demand->payload->get('demand_auth');
                $demandList = $demand->payload->get('user_list');
            }

            if ($demandQuery) {
                $demandQueryAuth = $demandQuery->payload->get('demand_query_auth');
                $demandQueryList = $demandQuery->payload->get('user_list');
            }

            if ($par) {
                $parAuth = $par->payload->get('par_auth');
                $parList = $par->payload->get('par_list');
            }

            if ($explorer && $isDebug) {
                $explorerAuth = $explorer->payload->get('auth');
                $explorerList = $explorer->payload->get('user_list');
            }


            // 選單列表
            $MenuArray = [
                'demandList' => [
                    'name' => '需求單',
                    'drop' => 0,
                    'dropDown' => [
                        [
                            'name' => '需求申請',
                            'url' => '/demand/submit',
                            'last_section' => 'submit',
                            'active' => 0
                        ],
                        [
                            'name' => '待審核',
                            'url' => '/demand/signing',
                            'last_section' => 'signing',
                            'active' => 0
                        ]
                    ]
                ],
                'parList' => [
                    'name' => '公設預約',
                    'drop' => 0,
                    'dropDown' => [
                        [
                            'name' => '預約登記',
                            'drop' => 0,
                            'dropDown' => [
                                [
                                    'name' => '設施預約',
                                    'url' => '/par/reserve/postulate',
                                    'last_section' => 'postulate',
                                    'active' => 0
                                ],
                                // [
                                //     'name' => '設備預約',
                                //     'url' => '/par/reserve/apparatus',
                                //     'last_section' => 'apparatus',
                                //     'active' => 0
                                // ],

                            ]
                        ],
                        [
                            'name' => '我的預約',
                            'url' => '/par/myReserves',
                            'last_section' => 'myReserves',
                            'active' => 0
                        ],
                    ]
                ],
                // 'explorerList' => [
                //     'name' => '文件管理',
                //     'drop' => 0,
                //     'dropDown' => [
                //         [
                //             'name' => '文件庫',
                //             'url' => '/explorer',
                //             'last_section' => 'explorer',
                //             'active' => 0
                //         ],
                //     ]
                // ],
            ];

            if ($isDebug) {
                $MenuArray['explorerList'] = [
                    'name' => '文件管理',
                    'drop' => 0,
                    'dropDown' => [
                        [
                            'name' => '文件庫',
                            'url' => '/explorer',
                            'last_section' => 'explorer',
                            'active' => 0
                        ],
                    ]
                ];
                # code...
            }
            // 相關權限
            $SettingArray = [
                'demandSetting' => [
                    'name' => '相關設定',
                    'drop' => 0,
                    'dropDown' => [
                        [
                            'name' => '需求單設定',
                            'url' => '/demand/setting/demand',
                            'last_section' => 'demand',
                            'active' => 0
                        ],
                        [
                            'name' => '資料庫設定',
                            'url' => '/demand/setting/database',
                            'last_section' => 'database',
                            'active' => 0
                        ],
                        [
                            'name' => '權限設定',
                            'url' => '/demand/setting/demPermission',
                            'last_section' => 'demPermission',
                            'active' => 0
                        ],
                        [
                            'name' => '通知設定',
                            'url' => '/demand/setting/notify',
                            'last_section' => 'notify',
                            'active' => 0
                        ],
                    ]
                ],
                'demandQuery' => [
                    'name' => '需求單查詢',
                    'url' => '/demand/query',
                    'last_section' => 'query',
                    'active' => 0
                ],
                'parSetting' => [
                    [
                        'name' => '公設管理',
                        'url' => '/par/setting',
                        'last_section' => 'setting',
                        'active' => 0
                    ],
                    [
                        'name' => '權限設定',
                        'url' => '/par/parPermission',
                        'last_section' => 'parPermission',
                        'active' => 0
                    ]
                ],
                'explorerSetting' => [
                    [
                        'name' => '權限設定',
                        'url' => '/explorer/permission',
                        'last_section' => 'permission',
                        'active' => 0
                    ]
                ],

            ];

            $funcMenu = [];
            $authLevel = [];
            // 需求單
            if (!empty($demandAuth)) {
                array_push($funcMenu, $MenuArray['demandList']);

                // 需求單查詢
                if (!empty($demandQueryAuth) && in_array(Session::get('employee_id'), $demandQueryList)) {
                    array_push($authLevel, 'demand_query');
                    array_push($funcMenu[0]['dropDown'], $SettingArray['demandQuery']);
                }

                //相關設定
                if (in_array(Session::get('employee_id'), $demandList)) {
                    array_push($authLevel, 'demand');
                    array_push($funcMenu[0]['dropDown'], $SettingArray['demandSetting']);
                }
            }



            // 公設預約
            if (!empty($parAuth)) {
                array_push($funcMenu, $MenuArray['parList']);
                //權限設定
                if (in_array(Session::get('employee_id'), $parList)) {
                    array_push($authLevel, 'par');
                    array_push(
                        $funcMenu[1]['dropDown'],
                        ...$SettingArray['parSetting']
                    );
                }
            }
            // 文件管理
            if (!empty($explorerAuth)) {
                array_push($funcMenu, $MenuArray['explorerList']);
                //權限設定
                if (in_array(Session::get('employee_id'), $explorerList)) {
                    array_push($authLevel, 'explorer');
                    array_push(
                        $funcMenu[2]['dropDown'],
                        ...$SettingArray['explorerSetting']
                    );
                }
            }
            Session::put('authLevel', $authLevel);
            Session::put('funcMenu', $funcMenu);
        }
        return $next($request);
    }
}
