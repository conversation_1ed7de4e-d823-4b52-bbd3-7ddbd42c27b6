@import "./fontawesome.css";

.menuList {
    @apply block py-6 text-white px-10 focus:opacity-100 hover:opacity-100 cursor-pointer;
}

.dropList {
    @apply flex justify-between pt-4 ml-4 pb-4 pr-8 text-white hover:opacity-100 cursor-pointer;
}

.demandItemWidth {
    @apply w-56 2xl:w-96;
}

.demandList {
    @apply p-6 w-full bg-white rounded-xl shadow;
}

.demandListSelect {
    @apply pr-6 pb-6 w-full bg-white rounded-xl shadow border border-white hover:border-primary;
}

.newSettingBtn {
    @apply w-40 h-12 text-current border-2 border-dashed py-3 text-center rounded-md cursor-pointer;
}

.thead {
    @apply flex justify-between mb-5 p-6 border-b border-gray-300 w-full;
}

.list {
    background: rgba(255, 255, 255, 0.2);
    border-radius: 4px 0px 0px 4px;
    color: white;
}

.apply-list:hover {
    background: rgba(255, 255, 255, 0.2);
    border-radius: 4px 0px 0px 4px;
    color: white;
}
