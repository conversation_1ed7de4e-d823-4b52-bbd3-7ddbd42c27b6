<?php

namespace App\Modules\par\Controllers;

use App\Http\Controllers\Controller;
use App\Modules\Demand\Models\OrgUnit;
use App\Modules\Demand\Models\Code;
use App\Modules\par\Models\Apparatus;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Session;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\Cache;

class CommonController extends Controller
{
    protected $company_id;
    protected $user_id;
    public function __construct()
    {
        $this->company_id = Session::get('CompanyId');
        $this->user_id = Session::get('employee_id');
    }
    // 部門下拉
    public function fetchOrgUnits(Request $request)
    {

        $org_units = OrgUnit::select('org_units.id', 'org_units.payload->fullname as name')
            ->where('company_id', $this->company_id)
            // ->orderby('payload->name')
            ->orderby('payload->sort')
            ->get();

        return count($org_units) > 0 ? $org_units : null;
    }
    public static function fetchReserveTimePeriod()
    {
        if (!Cache::has('codeTimePeriod')) {
            $code = Code::select('code_id as id', 'nm_zh_tw as name')
                ->where('code_kind', 'AK')
                ->orderBy('sort_order', 'asc')
                ->get();
            Cache::put('codeTimePeriod', $code, today(Session::get('timezone'))->addDays(1));
        } else
            $code = Cache::get('codeTimePeriod');

        return $code;
    }

    public function fetchApparatusesType()
    {
        $apparatus = Apparatus::company()
            ->select('id', 'payload->type as type')
            ->orderby('type')
            ->get()
            ->unique('type')->values();
        return $apparatus;
    }

    /**
     * 取得預約時間區間
     *
     * @param int $hour 切分多少小時為一個區間
     *
     * @return \Illuminate\Support\Collection
     *
     */
    public static function fetchReserveTimePeriodPerHour(int $hour = 1)
    {
        /** @var \Illuminate\Database\Eloquent\Collection $dataset */
        $dataset = static::fetchReserveTimePeriod();
        // 客戶提出的新要求是要移除 08:00-09:00 這個區間，而原本的資料結構無法變更，故只能在這操作
        $dataset = $dataset->filter(fn ($period) => $period->id > 1);

        return $dataset
            ->groupBy(fn ($period) => intval($period->id / (2 * $hour)))
            ->transform(fn ($groupPeriod) => (object)[
                'ids' => $groupPeriod->pluck('id')->toArray(),
                'name' => sprintf(
                    "%s-%s",
                    explode('-', $groupPeriod->first()->name)[0] ?? '',
                    explode('-', $groupPeriod->last()->name)[1] ?? '',
                ),
            ]);
    }
}
