<?php

namespace App\Modules\Demand\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class DemandQuery extends Model
{
    use \Staudenmeir\EloquentJsonRelations\HasJsonRelationships, SoftDeletes;

    protected $table = 'demands_query';

    protected $fillable = [];

    protected $casts = ['payload' => 'collection'];

    public function layout()
    {
        return $this->belongsTo(DemandLayout::class);
    }

    public function employee()
    {
        return $this->hasOne(Employee::class, 'id', 'created_by');
    }

    public function customList()
    {
        return $this->hasMany(CustomList::class, 'demand_id', 'demand_id');
    }
}
