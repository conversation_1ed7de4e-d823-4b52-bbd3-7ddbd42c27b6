@foreach ($data['msg'] as $item => $msg)
    <div>
        @isset($msg['isCanceled'])
        <p style="color: red">{!! $msg['isCanceled'] !!}預約取消通知:</p>
        @endisset
        @isset($msg['isRevised'])
        <p style="color: red">{!! $msg['isRevised'] !!}預約修改通知:</p>
        @endisset
        @isset($msg['isRemind'])
        <p style="color: red">會前提醒通知:</p>
        @endisset
        @isset($msg['user'])
            <p>預約人:{!! $msg['user'] !!}</p>
        @endisset
        <p>名稱:{!! $msg['name'] !!}</p>
        @isset($msg['amount'])
        <p>數量:{!! $msg['amount'] !!}</p>
        @endisset
        <p>預約日期:{!! $msg['date'] !!}</p>
        <p>預約時段:
            @php
            $msg['time'] = substr($msg['time'][0],0,6).substr($msg['time'][count($msg['time']) - 1],6);
            @endphp
            {!! $msg['time'] !!}
        </p>
        <p>預約事由:{!! $msg['reason'] !!}</p>
        <p>備註:{!! $msg['remark'] !!}</p>

        @isset($msg['notified'])
            <p>設備支援:
                @foreach ($msg['notified'] as $notified_key => $notified)
                    {!! $notified !!}
                    @if ($notified_key < count($msg['notified']) - 1)
                        <span>、</span>
                    @endif
                @endforeach
            </p>
        @endisset
    </div>
    <hr>
    <br>
@endforeach
