<template>
  <div class="flex flex-col bg-white rounded-lg flex-grow">
    <!-- 右鍵選單 -->
    <CustomContextMenu
      v-show="isShowContextMenu"
      :contextmenuPosition="contextmenuPosition"
      :menu="
        menuType === 'selectionBox'
          ? selectionBoxContextMenu
          : menuType === 'file'
          ? fileContextMenu
          : menuType === 'folder'
          ? folderContextMenu
          : menuType === 'paste'
          ? pasteContextMenu
          : []
      "
    />

    <!-- 檔案詳細 -->
    <FileInfo
      :isShow="isShowFileInfo"
      :selectedItem="selectedItem"
      :currentFolderData="currentFolderData"
      :versionHistoryRecord="versionHistoryRecord"
      @toggleDialog="toggleFileInfo"
      @onDelete="handleDelete"
      @onOpen="handleOpenFile"
      @onDownload="handleDownloadFile"
    />

    <!-- 權限設定 -->
    <PermissionSettings
      :isShow="isShowPermissionSettings"
      :folderId="permissionFolderId"
      @togglePermission="togglePermissionSettings"
    />

    <!--檔案預覽 -->
    <Dialog
      :visible.sync="isShowPdf"
      :containerStyle="{
        maxWidth: '90%',
        width: '682px',
        maxHeight: '90%',
      }"
      :modal="true"
      dismissableMask
    >
      <template #header>
        <div></div>
      </template>

      <div class="w-full h-full overflow-x-hidden" ref="filePreview"></div>

      <template #footer>
        <div></div>
      </template>
    </Dialog>

    <!-- 複製/剪下提示訊息 -->
    <div
      v-show="toastMessage"
      style="box-shadow: 0px 4px 12px 0px #f0f0f0"
      class="w-auto h-12 px-4 py-3 rounded-lg bg-indigo-100 text-primary fixed top-28 left-1/2 transform -translate-x-1/2 place-items-center z-50 transition-all"
    >
      <p class="flex items-center justify-center">
        <span> {{ toastMessage }}</span>
        <button class="ml-8" @click="handleCancelPaste">取消</button>
      </p>
    </div>

    <!-- header -->
    <Navbar
      :rootId="rootId"
      :isSearched="isSearched"
      :isShowDirTree="isShowDirTree"
      :currentPathIds="currentPathIds"
      :currentPathNames="currentPathNames"
      :prevPathIds="prevPathIds"
      :nextPathIds="nextPathIds"
      :layout="layout"
      :isThumbnail="isThumbnail"
      @updateIsShowDirTree="updateIsShowDirTree"
    />

    <!-- content min-h-[721px] -->
    <div class="relative h-content flex w-full lg:h-80 justify-start flex-grow">
      <!-- folder structure -->
      <div
        v-show="isShowDirTree"
        class="flex-shrink-0 overflow-auto border-r py-2 border-gray-200 bg-white hidden xs:block"
        :class="resizable && 'resize-mouse'"
        :style="{ width: dirWidth + 'px' }"
        @mouseenter.stop="handleMouseEnter"
        @mouseleave.stop="handleMouseLeave"
        @mousedown="startResize"
      >
        <ul style="min-width: 100%" class="w-max h-full">
          <DirectoryTree
            :data="dirTree"
            :nextPathIds="nextPathIds"
            :padding="16"
          />
        </ul>
      </div>
      <div
        class="flex-grow"
        @contextmenu="handleContextMenu(null, $event)"
        @touchstart="handleTouchStart(null, $event)"
        @touchend="handleTouchEnd"
        @touchmove="handleTouchEnd"
      >
        <!-- 選擇框 -->
        <SelectionBox v-if="isSelecting" :boxStyle="boxStyle" />
        <!-- 無搜尋資料 -->
        <div
          v-if="isSearched && !currentData?.length"
          class="w-full h-full flex flex-col justify-center items-center"
        >
          <img src="@images/page_state/search_empty.png" alt="search_empty" />
          <p style="color: #475467" class="mt-8 text-base font-bold">
            無任何搜尋結果，請再次搜尋
          </p>
        </div>

        <!-- 無資料 -->
        <div
          v-else-if="!currentData?.length && !isLoading"
          class="w-full h-full flex flex-col justify-center items-center"
        >
          <img src="@images/page_state/empty.png" alt="no-data" />
          <p style="color: #475467" class="mt-8 text-base font-bold">
            目前暫無資料
          </p>
        </div>

        <!-- grid排版 -->
        <div
          v-else-if="layout === 'grid' && currentData?.length"
          class="w-full h-full overflow-auto flex justify-start items-start px-3"
          @mousedown="startSelection"
        >
          <div class="flex flex-wrap">
            <div
              ref="fileRefs"
              v-for="data in currentData"
              class="flex flex-col justify-start items-center text-center cursor-pointer border border-transparent hover:bg-blue-100 hover:border-blue-500 transition m-3"
              :class="{
                'w-52': isThumbnail,
                'w-20': !isThumbnail,
                'opacity-40': !isDragging && cutItems.includes(data),
              }"
              :key="data.id"
              :data-id="data.id"
              :draggable="true"
              @touchstart.prevent="handleTouchStart(data, $event)"
              @touchend.prevent="handleTouchEnd(data)"
              @touchmove.prevent="handleTouchEnd(data)"
              @contextmenu="handleContextMenu(data, $event)"
              @click="data.type === 'folder' && handleOpenFolder(data)"
              @dblclick="data.type !== 'folder' && handleOpenFile(data)"
              @dragstart="handleDragStart(data)"
              @dragover.prevent="isSelecting = false"
              @dragend="handleDragEnd"
            >
              <div class="w-full mb-0.5 px-3">
                <img class="w-full" :src="data.iconPath" />
              </div>
              <div
                class="w-full max-h-14 overflow-hidden whitespace-normal break-all"
              >
                <input
                  :ref="data.isChangingName && 'newNameInput'"
                  v-show="data.isChangingName"
                  type="text"
                  class="rename w-full text-center border text-xs p-1 outline-none focus:border-blue-500"
                  :placeholder="data.name"
                  :value="newName"
                  @change="
                    ($event) => $emit('updateNewName', $event.target.value)
                  "
                  @blur="$emit('onRename')"
                  @keyup.enter="$emit('onRename')"
                />
                <span
                  v-show="!data.isChangingName"
                  class="text-xs text-gray-800 text-ellipsis file-name"
                >
                  {{ data.name }}
                </span>
              </div>
            </div>
          </div>
        </div>

        <!-- list排版 -->
        <div
          v-else-if="layout === 'list' && currentData?.length"
          class="w-full h-full overflow-auto"
        >
          <table class="w-full h-full text-explorerPrimary flex flex-col">
            <thead class="flex sticky w-full">
              <tr
                style="background-color: #f9fafb"
                class="flex flex-row items-center w-full"
              >
                <th
                  class="w-28 md:w-40 lg:w-96 font-bold flex items-center pl-6 py-2"
                >
                  <span>名稱</span>
                  <i
                    v-show="sort === 'asc'"
                    class="pi pi-arrow-up ml-2 cursor-pointer"
                    @click="
                      $emit('onSortName', 'desc');
                      sort = 'desc';
                    "
                  />
                  <i
                    v-show="sort === 'desc'"
                    class="pi pi-arrow-down ml-2 cursor-pointer"
                    @click="
                      $emit('onSortName', 'asc');
                      sort = 'asc';
                    "
                  />
                </th>
                <th
                  class="w-28 md:w-40 lg:w-96 font-bold flex items-center p-2"
                >
                  <span>上傳日期</span>
                  <i
                    v-show="sort === 'asc'"
                    class="pi pi-arrow-up ml-2 align-middle cursor-pointer"
                    @click="
                      $emit('onSortDate', 'desc');
                      sort = 'desc';
                    "
                  />
                  <i
                    v-show="sort === 'desc'"
                    class="pi pi-arrow-down ml-2 align-middle cursor-pointer"
                    @click="
                      $emit('onSortDate', 'asc');
                      sort = 'asc';
                    "
                  />
                </th>
                <th
                  class="w-28 md:w-40 lg:w-auto flex-1 font-bold flex items-center py-2"
                >
                  檔案大小
                </th>
              </tr>
            </thead>
            <tbody class="flex-1 overflow-y-auto">
              <tr
                ref="fileRefs"
                v-for="data in currentData"
                :key="data.id"
                :data-id="data.id"
                class="w-full"
              >
                <td
                  @touchstart.prevent="handleTouchStart(data, $event)"
                  @touchend.prevent="handleTouchEnd(data)"
                  @touchmove.prevent="handleTouchEnd(data)"
                  @contextmenu="handleContextMenu(data, $event)"
                  @click="data.type === 'folder' && handleOpenFolder(data)"
                  @dblclick="data.type !== 'folder' && handleOpenFile(data)"
                  class="w-28 md:w-40 lg:w-96 cursor-pointer flex items-center justify-start pl-6 py-3"
                  :class="{
                    'opacity-40': !isDragging && cutItems.includes(data),
                  }"
                >
                  <img
                    class="w-3.5 cursor-pointer mr-2"
                    :src="data?.iconPath"
                  />
                  <input
                    :ref="data.isChangingName && 'newNameInput'"
                    v-show="data?.isChangingName"
                    type="text"
                    class="rename text-left border px-1 outline-none focus:border-blue-500"
                    :placeholder="data?.name"
                    :value="newName"
                    @change="
                      ($event) => $emit('updateNewName', $event.target.value)
                    "
                    @blur="$emit('onRename')"
                    @keyup.enter="$emit('onRename')"
                  />
                  <span
                    v-show="!data?.isChangingName"
                    class="text-explorerPrimary truncate"
                    >{{ data.name }}</span
                  >
                </td>
                <td class="w-28 md:w-40 lg:w-96 px-2 py-3">
                  {{ data.timeCreated?.replace(/-/g, "/") || "" }}
                </td>
                <td class="w-28 md:w-40 lg:w-auto py-3">
                  {{
                    data.size
                      ? Math.floor((data?.size * 1) / 1024) + " KB"
                      : "-"
                  }}
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>

    <!-- footer -->
    <div
      style="border-color: #d0d5dd"
      class="text-sm text-explorerPrimary align-middle border-t px-7 w-full h-10 leading-10 rounded-b-md"
    >
      {{ currentData?.length }} 個項目
    </div>
  </div>
</template>

<script>
import * as pdfjsLib from "pdfjs-dist";
import { renderAsync } from "docx-preview";
import Dialog from "primevue/dialog";
import DirectoryTree from "./directory-tree.vue";
import FileInfo from "./file-info.vue";
import CustomContextMenu from "./custom-contextmenu.vue";
import PermissionSettings from "../common/permission-settings.vue";
import Navbar from "./navbar.vue";
import SelectionBox from "./selection-box.vue";
import {
  getFile,
  copyItem,
  cutItem,
  removeItem,
  getVersionRecord,
} from "@/axios/explorerApi.js";

export default {
  name: "FileView",
  components: {
    Dialog,
    DirectoryTree,
    FileInfo,
    CustomContextMenu,
    PermissionSettings,
    Navbar,
    SelectionBox,
  },
  props: {
    rootId: { type: Number, required: true },
    layout: { type: String, required: true },
    currentData: { type: Array, required: true },
    currentPathIds: { type: Array, required: true },
    currentPathNames: { type: Array, required: true },
    prevPathIds: { type: Array, required: true },
    nextPathIds: { type: Array, required: true },
    selectedItem: { required: true },
    newName: { type: String, required: true },
    dirTree: { type: Array, required: true },
    isSearched: { type: Boolean, required: true },
    isThumbnail: { type: Boolean, required: true },
    isLoading: { type: Boolean, required: true },
    currentFolderData: { required: true },
    confirmOption: { type: Object, required: true },
  },
  inject: [
    "fetchDataByFolderId",
    "fetchDirectory",
    "checkFolderPermission",
    "updatePrevPathIds",
  ],
  data() {
    return {
      isDragging: false,
      isSelecting: false,
      selectedItemIds: [],
      boxStyle: {},
      selectStartX: 0,
      selectStartY: 0,
      resizable: false,
      isResizing: false,
      resizeStartX: 0,
      dirWidth: 240,
      dirRightPosition: 260,
      sort: "asc",
      isShowDirTree: false,
      isShowContextMenu: false,
      isShowPdf: false,
      isShowPermissionSettings: false,
      isShowFileInfo: {
        fileHistory: false,
        fileDetails: false,
      },
      toastMessage: "",
      // 資料夾權限資料
      permissionFolderId: null,
      copiedItems: [],
      cutItems: [],
      menuType: "",
      fileContextMenu: [
        {
          label: "open",
          name: "開啟",
          iconPath: new URL("@images/icon/open.svg", import.meta.url).href,
          disableIconPath: new URL("@images/icon/open_disable.svg", import.meta.url).href,
          hasPermission: false,
          command: () => this.handleOpenFile(),
        },
        {
          label: "content",
          name: "檔案內容",
          iconPath: new URL("@images/icon/content.svg", import.meta.url).href,
          hasPermission: false,
          command: () => (this.isShowFileInfo.fileDetails = true),
        },
        {
          label: "version",
          name: "版本歷程記錄",
          iconPath: new URL("@images/icon/version.svg", import.meta.url).href,
          hasPermission: false,
          command: () => this.handleGetVersion(),
        },
        {
          label: "download",
          name: "下載",
          iconPath: new URL("@images/icon/down.svg", import.meta.url).href,
          disableIconPath: new URL("@images/icon/down_disable.svg", import.meta.url).href,
          hasPermission: false,
          command: () => this.handleDownloadFile(false),
        },
        {
          label: "rename",
          name: "重新命名",
          iconPath: new URL("@images/icon/edit.svg", import.meta.url).href,
          disableIconPath: new URL("@images/icon/edit_disable.svg", import.meta.url).href,
          hasPermission: false,
          command: () => this.$emit("onChangeName"),
        },
        {
          label: "copy",
          name: "複製",
          iconPath: new URL("@images/icon/copy.svg", import.meta.url).href,
          disableIconPath: new URL("@images/icon/copy_disable.svg", import.meta.url).href,
          hasPermission: false,
          command: () => this.handleCopy(),
        },
        {
          label: "cut",
          name: "剪下",
          iconPath: new URL("@images/icon/cut.svg", import.meta.url).href,
          disableIconPath: new URL("@images/icon/cut_disable.svg", import.meta.url).href,
          hasPermission: false,
          command: () => this.handleCut(),
        },
        {
          label: "delete",
          name: "刪除",
          iconPath: new URL("@images/icon/delete.svg", import.meta.url).href,
          disableIconPath: new URL("@images/icon/delete_disable.svg", import.meta.url).href,
          hasPermission: false,
          command: () =>
            this.$emit("setConfirm", {
              type: "warning",
              message: "確定刪除檔案？",
              imgPath: new URL("@images/popup_state/warning.png", import.meta.url).href,
              leftBtn: {
                text: "取消",
                command: () => (this.confirmOption.isShow = false),
              },
              rightBtn: {
                text: "確定",
                command: () => this.handleDelete(),
              },
              isShow: true,
            }),
        },
      ],
      folderContextMenu: [
        {
          label: "open",
          name: "開啟",
          iconPath: new URL("@images/icon/open.svg", import.meta.url).href,
          disableIconPath: new URL("@images/icon/open_disable.svg", import.meta.url).href,
          hasPermission: false,
          command: () => this.handleOpenFolder(),
        },
        {
          label: "rename",
          name: "重新命名",
          iconPath: new URL("@images/icon/edit.svg", import.meta.url).href,
          disableIconPath: new URL("@images/icon/edit_disable.svg", import.meta.url).href,
          hasPermission: false,
          command: () => this.$emit("onChangeName"),
        },
        {
          label: "copy",
          name: "複製",
          iconPath: new URL("@images/icon/copy.svg", import.meta.url).href,
          disableIconPath: new URL("@images/icon/copy_disable.svg", import.meta.url).href,
          hasPermission: false,
          command: () => this.handleCopy(),
        },
        {
          label: "cut",
          name: "剪下",
          iconPath: new URL("@images/icon/cut.svg", import.meta.url).href,
          disableIconPath: new URL("@images/icon/cut_disable.svg", import.meta.url).href,
          hasPermission: false,
          command: () => this.handleCut(),
        },
        {
          label: "delete",
          name: "刪除",
          iconPath: new URL("@images/icon/delete.svg", import.meta.url).href,
          disableIconPath: new URL("@images/icon/delete_disable.svg", import.meta.url).href,
          hasPermission: false,
          command: () =>
            this.$emit("setConfirm", {
              type: "warning",
              message: "確定刪除此資料夾？",
              imgPath: new URL("@images/popup_state/warning.png", import.meta.url).href,
              leftBtn: {
                text: "取消",
                command: () => (this.confirmOption.isShow = false),
              },
              rightBtn: {
                text: "確定",
                command: () => this.handleDelete(),
              },
              isShow: true,
            }),
        },
        {
          label: "permission",
          name: "權限管理",
          iconPath: new URL("@images/icon/permission.svg", import.meta.url).href,
          disableIconPath: new URL("@images/icon/permission_disable.svg", import.meta.url).href,
          hasPermission: false,
          command: () => this.togglePermissionSettings(true),
        },
      ],
      pasteContextMenu: [
        {
          label: "paste",
          name: "貼上",
          iconPath: new URL("@images/icon/paste.svg", import.meta.url).href,
          disableIconPath: new URL("@images/icon/paste_disable.svg", import.meta.url).href,
          hasPermission: false,
          command: () => this.handlePaste(),
        },
      ],
      selectionBoxContextMenu: [
        {
          label: "download",
          name: "下載",
          iconPath: new URL("@images/icon/down.svg", import.meta.url).href,
          disableIconPath: new URL("@images/icon/down_disable.svg", import.meta.url).href,
          hasPermission: false,
          command: () => this.handleDownloadFile(false),
        },
        {
          label: "copy",
          name: "複製",
          iconPath: new URL("@images/icon/copy.svg", import.meta.url).href,
          disableIconPath: new URL("@images/icon/copy_disable.svg", import.meta.url).href,
          hasPermission: false,
          command: () => this.handleCopy(),
        },
        {
          label: "cut",
          name: "剪下",
          iconPath: new URL("@images/icon/cut.svg", import.meta.url).href,
          disableIconPath: new URL("@images/icon/cut_disable.svg", import.meta.url).href,
          hasPermission: false,
          command: () => this.handleCut(),
        },
        {
          label: "delete",
          name: "刪除",
          iconPath: new URL("@images/icon/delete.svg", import.meta.url).href,
          disableIconPath: new URL("@images/icon/delete_disable.svg", import.meta.url).href,
          hasPermission: false,
          command: () =>
            this.$emit("setConfirm", {
              type: "warning",
              message: "確定刪除檔案？",
              imgPath: new URL("@images/popup_state/warning.png", import.meta.url).href,
              leftBtn: {
                text: "取消",
                command: () => (this.confirmOption.isShow = false),
              },
              rightBtn: {
                text: "確定",
                command: () => this.handleDelete(),
              },
              isShow: true,
            }),
        },
      ],
      contextmenuPosition: {
        x: "",
        y: "",
      },
      versionHistoryRecord: [],
    };
  },
  methods: {
    updateIsShowDirTree(newVal) {
      if (window.innerWidth <= 375 || this.isShowDirTree === newVal) return;
      this.isShowDirTree = newVal;
    },
    handleContextMenu(data, event) {
      // 設定 contextmenu 的出現座標
      if (event.type === "contextmenu") {
        this.contextmenuPosition.x = event.clientX + 5 + "px";
        this.contextmenuPosition.y = event.clientY + 5 + "px";
      } else if (event.type === "touchstart") {
        this.contextmenuPosition.x = event.targetTouches[0].pageX + "px";
        this.contextmenuPosition.y = event.targetTouches[0].pageY + "px";
      } else return;

      if (this.selectedItemIds.length) {
        this.menuType = "selectionBox";
        this.isShowContextMenu = true;
        this.checkContextmenuPermission(
          this.selectionBoxContextMenu,
          this.selectedItems
        );
      } else if (
        (!data && this.copiedItems.length) ||
        (!data && this.cutItems.length)
      ) {
        this.menuType = "paste";
        this.isShowContextMenu = true;
        this.checkContextmenuPermission(
          this.pasteContextMenu,
          this.currentFolderData
        );
      } else if (data && !this.copiedItems.length && !this.cutItems.length) {
        this.$emit("updateSelectedItem", data);
        this.isShowContextMenu = true;
        if (data.type === "folder") {
          this.menuType = "folder";
          this.checkContextmenuPermission(this.folderContextMenu, data);
        } else if (data.type) {
          this.menuType = "file";
          this.checkContextmenuPermission(
            this.fileContextMenu,
            this.currentFolderData
          );
        }
      }
    },
    checkContextmenuPermission(menu, folderData) {
      // 設定 contextmenu 的功能權限
      const permissionMap = {
        open: ["admin", "read"],
        content: ["admin", "read"],
        version: ["admin", "read"],
        download: ["admin", "download"],
        rename: ["admin"],
        copy: ["admin"],
        cut: ["admin"],
        paste: ["admin"],
        delete: ["admin"],
        permission: ["admin"],
      };

      menu.forEach((item) => {
        const permissions = permissionMap[item.label] || [];
        // 檢查檔案是否多選
        if (Array.isArray(folderData)) {
          // 檢查是否有任何資料夾被選中
          const hasFolderSelected = folderData.some(
            (folder) => folder.type === "folder"
          );

          // 如果有資料夾被選中且為 download 選單項目,則禁用
          if (hasFolderSelected && item.label === "download") {
            item.hasPermission = false;
          } else {
            item.hasPermission = folderData.every((folder) => {
              // 如果是資料夾,則檢查資料夾權限
              if (folder.type === "folder") {
                return permissions.some((permission) => folder[permission]);
              }
              // 如果是檔案,則檢查當前資料夾權限
              return permissions.some(
                (permission) => this.currentFolderData[permission]
              );
            });
          }
        } else {
          item.hasPermission = permissions.some(
            (permission) => folderData[permission]
          );
        }
      });
    },
    handleTouchStart(data, event) {
      if (!data && !this.copiedItems.length && !this.cutItems.length) return;
      // touch 500ms 後開啟 contextmenu
      this.touchTimer = setTimeout(() => {
        this.handleContextMenu(data, event);
      }, 500);
    },
    handleTouchEnd(data) {
      clearTimeout(this.touchTimer);
      if (!this.isShowContextMenu && data) this.handleOpenFolder(data);
    },
    handleMouseEnter(event) {
      // 滑鼠移入元素右側邊框可調整寬度
      this.resizable = true;
      this.dirRightPosition = event.target.getBoundingClientRect().right;
      event.target.addEventListener("mousemove", this.toggleResizable);
    },
    toggleResizable(event) {
      if (Math.abs(this.dirRightPosition - event.clientX) > 6) {
        this.resizable = false;
      } else {
        this.resizable = true;
      }
    },
    handleMouseLeave() {
      this.resizable = false;
      document.removeEventListener("mousemove", this.toggleResizable);
    },
    startResize(event) {
      if (this.resizable) {
        this.isResizing = true;
        this.resizeStartX = event.clientX;

        document.addEventListener("mousemove", this.handleMouseMove);
        document.addEventListener("mouseup", this.stopResize);
      }
    },
    handleMouseMove(event) {
      // 滑鼠拖曳右側邊框重設寬度
      if (this.isResizing) {
        const deltaX = event.clientX - this.resizeStartX;
        this.dirWidth += deltaX;
        this.resizeStartX = event.clientX;
      }
    },
    stopResize() {
      // 移除事件監聽
      this.isResizing = false;
      document.removeEventListener("mousemove", this.handleMouseMove);
      document.removeEventListener("mouseup", this.stopResize);
    },
    handleOpenFolder(data) {
      if (!data) data = this.selectedItem;
      if (
        !data.id ||
        data.type !== "folder" ||
        data.isChangingName ||
        !this.checkFolderPermission(data.id)
      ) {
        return;
      }
      this.updatePrevPathIds(data.id);
      this.fetchDataByFolderId(data.id);
      this.nextPathIds.length = 0;
    },
    async handleOpenFile(data, isOldVersion = false, versionId) {
      if (!data) data = this.selectedItem;
      if (!this.currentFolderData?.admin && !this.currentFolderData?.read)
        return;

      if (!data.type.includes("pdf") && !data.type.includes("docx")) {
        this.$emit("setConfirm", {
          type: "warning",
          message: "此檔案無法預覽，僅pdf和docx提供預覽",
          imgPath: new URL("@images/popup_state/warning.png", import.meta.url).href,
          centerBtn: {
            text: "我知道了",
            command: () => (this.confirmOption.isShow = false),
          },
          isShow: true,
        });
        return;
      }

      // 有 versionId 代表要開啟的是檔案歷史紀錄
      const id = versionId || data.id;
      let url;

      try {
        const blob = await getFile({
          id,
          type: "read",
          isOldVersion,
        });
        url = URL.createObjectURL(blob);

        this.isShowPdf = true;

        // 等待 pdf 的 dialog 渲染完畢才能獲取 filePreview
        await this.$nextTick();
        const pagesContainer = this.$refs.filePreview;

        if (data.type.includes("pdf")) {
          pdfjsLib.GlobalWorkerOptions.workerSrc = require("pdfjs-dist/build/pdf.worker.entry.js");

          const pdfDocument = await pdfjsLib.getDocument(url).promise;
          const numPages = pdfDocument.numPages;

          // 獲取 pdf 所有頁數並渲染到 canvas
          for (let pageNum = 1; pageNum <= numPages; pageNum++) {
            const canvas = document.createElement("canvas");
            canvas.id = `page${pageNum}`;
            canvas.style.display = "inline-block";
            pagesContainer.appendChild(canvas);
            const pdfPage = await pdfDocument.getPage(pageNum);
            this.renderPdf(pdfPage, canvas);
          }
        } else if (data.type.includes("docx")) {
          // 渲染 docx 並調整預設樣式
          await renderAsync(blob, pagesContainer);
          const docxWrapper = document.querySelector(".docx-wrapper");
          const docx = document.querySelector(".docx");
          if (docxWrapper && docx) {
            docxWrapper.style.padding = 0;
            docxWrapper.style.background = "white";
            docx.style.padding = "0 100px";
            docx.style.boxShadow = "none";
          }
        }
      } catch (error) {
        this.$refs.toast.add({
          severity: "error",
          summary: error.message,
          life: 3000,
        });
      } finally {
        // 釋放已使用完畢的 url 物件
        if (url) URL.revokeObjectURL(url);
      }
    },
    renderPdf(pdfPage, canvas) {
      const scale = 1.5;
      const viewport = pdfPage.getViewport({ scale });
      const outputScale = window.devicePixelRatio || 1;

      canvas.width = Math.floor(viewport.width * outputScale);
      canvas.height = Math.floor(viewport.height * outputScale);
      canvas.style.width = "100%";
      canvas.style.height = "100%";

      const context = canvas.getContext("2d");
      const transform =
        outputScale !== 1 ? [outputScale, 0, 0, outputScale, 0, 0] : null;

      pdfPage.render({
        canvasContext: context,
        transform: transform,
        viewport: viewport,
      });
    },
    handleCopy() {
      if (!this.currentFolderData.admin) return;
      if (this.selectedItemIds.length !== 0) {
        this.copiedItems = this.selectedItems;
        this.toastMessage = `已複製${this.selectedItemIds.length}個資料夾/檔案`;
        this.selectedItemIds = [];
      } else if (this.selectedItem) {
        this.copiedItems = [this.selectedItem];
        const obj = this.copiedItems[0].type === "folder" ? "資料夾" : "檔案";
        this.toastMessage = `已複製1個${obj}`;
      }
    },
    handleCut() {
      if (!this.currentFolderData.admin) return;
      if (this.selectedItemIds.length !== 0) {
        this.cutItems = this.selectedItems;
        this.toastMessage = `已剪下${this.selectedItemIds.length}個資料夾/檔案`;
        this.selectedItemIds = [];
      } else if (this.selectedItem) {
        this.cutItems = [this.selectedItem];
        const obj = this.cutItems[0].type === "folder" ? "資料夾" : "檔案";
        this.toastMessage = `已剪下1個${obj}`;
      }
    },
    async handlePaste(targetFolderId = this.currentFolderData.id) {
      const targetFolder =
        this.currentData.find((item) => item.id === targetFolderId) ||
        this.currentFolderData;

      const selectedItems = this.copiedItems.length
        ? this.copiedItems
        : this.cutItems;

      try {
        if (targetFolder.type !== "folder") {
          throw new Error("無法拖曳至檔案");
        }
        if (!targetFolder.admin) {
          throw new Error("目標資料夾無權限");
        }
        if (selectedItems.includes(targetFolder)) {
          throw new Error("請勿在資料夾內部貼上");
        }
        this.cutItems.forEach((item) => {
          if (!item.admin) {
            throw new Error("選取檔案/資料夾無權限");
          }
        });

        if (targetFolderId === this.currentFolderData.id) {
          await Promise.all(
            selectedItems.map(async (item) => {
              const { id, type, name } = item;
              const itemsToCheck =
                type === "folder"
                  ? this.currentData.filter((data) => data.type === "folder")
                  : this.currentData.filter((data) => data.type === type);

              const isNameExist = itemsToCheck.some(
                (data) => data.name.toLowerCase() === name.toLowerCase()
              );
              if (isNameExist) {
                throw new Error(`名稱"${name}"已存在`);
              }

              const isInCurrentFolder =
                id === targetFolderId ||
                this.currentFolderData?.path.find(
                  (folderId) => folderId === id
                );
              if (isInCurrentFolder) {
                throw new Error("請勿在資料夾內部貼上");
              }
            })
          );
        }

        const ids = selectedItems.map((item) => item.id);
        if (this.copiedItems.length) {
          await copyItem({ ids, targetFolderId });
        } else if (this.cutItems.length) {
          await cutItem({ ids, targetFolderId });
        }
        await this.fetchDirectory();
        await this.fetchDataByFolderId(this.currentFolderData?.id);
      } catch (error) {
        this.$emit("setConfirm", {
          type: "wrong",
          message: error.message,
          imgPath: new URL("@images/popup_state/wrong.png", import.meta.url).href,
          centerBtn: {
            text: "我知道了",
            command: () => (this.confirmOption.isShow = false),
          },
          isShow: true,
        });
      } finally {
        this.handleCancelPaste();
      }
    },
    handleCancelPaste() {
      // 取消貼上，重設 contextmenu 及相關變數
      this.menuType = "";
      this.toastMessage = "";
      this.isShowContextMenu = false;
      this.copiedItems = [];
      this.cutItems = [];
      this.selectedItemIds = [];
      this.initFilesStyle();
    },
    async handleDelete() {
      if (!this.currentFolderData.admin) return;
      let ids;
      if (this.selectedItemIds.length) {
        ids = this.selectedItemIds;
      } else {
        ids = [this.selectedItem.id];
      }
      if (!ids.length) return;
      try {
        await removeItem({ ids });
        this.$refs.toast.add({
          severity: "success",
          summary: "刪除成功",
          life: 3000,
        });
        this.confirmOption.isShow = false;
        await this.fetchDirectory();
      } catch (error) {
        console.error("delete", error);
        this.$refs.toast.add({
          severity: "error",
          summary: error.message,
          life: 3000,
        });
      } finally {
        this.initFilesStyle();
        this.selectedItemIds = [];
        await this.fetchDataByFolderId(this.currentFolderData?.id);
      }
    },
    async handleGetVersion() {
      this.isShowFileInfo.fileHistory = true;
      if (!this.selectedItem) return;
      const { id } = this.selectedItem;
      try {
        const data = await getVersionRecord(id);
        this.versionHistoryRecord = data;
      } catch (error) {
        this.$refs.toast.add({
          severity: "error",
          summary: error.message,
          life: 3000,
        });
      }
    },
    async handleDownloadFile(isOldVersion, versionId) {
      if (!this.currentFolderData?.admin && !this.currentFolderData?.read) {
        return;
      }

      const downloadFile = async (item) => {
        const { id, name, type } = item;
        const itemId = versionId || id;
        let url;

        try {
          const blob = await getFile({
            id: itemId,
            type: "download",
            isOldVersion,
          });
          url = URL.createObjectURL(blob);

          // 新增一個a標籤來下載檔案
          const link = document.createElement("a");
          document.body.appendChild(link);
          link.href = url;
          link.download = `${name}.${type}`;
          link.click();
        } catch (error) {
          console.error(error);
          this.$refs.toast.add({
            severity: "error",
            summary: error.message,
            life: 3000,
          });
        } finally {
          if (url) {
            // 釋放已使用完畢的 url 物件
            URL.revokeObjectURL(url);
          }
        }
      };

      if (this.selectedItems.length) {
        for (let i = 0; i < this.selectedItems.length; i++) {
          await downloadFile(this.selectedItems[i]);
        }
      } else {
        downloadFile(this.selectedItem);
      }
    },
    toggleFileInfo(isShow, type) {
      this.isShowFileInfo[type] = isShow;
      if (this.versionHistoryRecord.length) this.versionHistoryRecord = [];
    },
    async togglePermissionSettings(isShow) {
      this.permissionFolderId = this.selectedItem.id;
      this.isShowPermissionSettings = isShow;
    },
    initFilesStyle() {
      this.$refs.fileRefs.forEach((ref) => {
        ref.style.border = "1px solid transparent";
        ref.style.backgroundColor = "transparent";
      });
    },
    startSelection(event) {
      // 只有在滑鼠左鍵點擊時才觸發選擇框
      if (
        event.button !== 0 ||
        this.isLoading ||
        this.isResizing ||
        this.isDragging ||
        (this.selectedItem && this.selectedItem.isChangingName)
      )
        return;

      this.initFilesStyle();
      this.boxStyle = {
        left: "0px",
        top: "0px",
        width: "0px",
        height: "0px",
      };

      this.isSelecting = true;
      this.selectStartX = event.clientX;
      this.selectStartY = event.clientY;
      document.addEventListener("mousemove", this.selectionMouseMove);
      document.addEventListener("mouseup", this.stopSelection);
    },
    selectionMouseMove(event) {
      if (this.isDragging) {
        this.isSelecting = false;
        return;
      }
      const currentX = event.clientX;
      const currentY = event.clientY;

      this.boxStyle = {
        left: `${Math.min(this.selectStartX, currentX)}px`,
        top: `${Math.min(this.selectStartY, currentY)}px`,
        width: `${Math.abs(this.selectStartX - currentX)}px`,
        height: `${Math.abs(this.selectStartY - currentY)}px`,
      };
    },
    stopSelection() {
      if (this.isDragging || !this.isSelecting) return;
      this.isSelecting = false;
      this.selectedItemIds = [];
      document.removeEventListener("mousemove", this.selectionMouseMove);
      document.removeEventListener("mouseup", this.stopSelection);

      const selectionBox = this.$el
        .querySelector(".selection-box")
        .getBoundingClientRect();
      this.$refs.fileRefs.forEach((ref) => {
        const fileElement = ref.getBoundingClientRect();
        if (
          fileElement.left < selectionBox.right &&
          fileElement.right > selectionBox.left &&
          fileElement.top < selectionBox.bottom &&
          fileElement.bottom > selectionBox.top
        ) {
          const fileId = +ref.dataset?.id;
          if (!this.selectedItemIds.includes(fileId)) {
            this.selectedItemIds.push(fileId);
          }
          ref.style.border = "1px solid #3B82F6";
          ref.style.backgroundColor = "#DBEAFE";
        }
      });
      this.boxStyle = {
        left: "0px",
        top: "0px",
        width: "0px",
        height: "0px",
      };
    },
    handleDragStart(item) {
      this.isSelecting = false;
      this.isDragging = true;
      this.cutItems = [item];
    },
    handleDragEnd(event) {
      const x = event.clientX;
      const y = event.clientY;
      const targetElement = document
        .elementFromPoint(x, y)
        .closest("[data-id]");
      const targetFolderId = +targetElement?.dataset?.id;
      if (this.selectedItemIds.length) {
        this.cutItems = this.selectedItems;
      }
      if (targetFolderId && this.cutItems.length) {
        this.handlePaste(targetFolderId);
      } else {
        this.cutItems = [];
      }
      this.isDragging = false;
      this.isSelecting = false;
    },
  },
  mounted() {
    // 點擊空白處關閉 contextmenu
    window.addEventListener("click", () => {
      if (this.isShowContextMenu) {
        this.isShowContextMenu = false;
        this.menuType = "";
      }
    });

    // 禁止截圖
    window.addEventListener("keyup", (event) => {
      if (event.key === "PrintScreen" && this.isShowPdf) {
        this.isShowPdf = false;
        this.$emit("setConfirm", {
          type: "warning",
          message: "該頁面不可截圖",
          imgPath: new URL("@images/popup_state/warning.png", import.meta.url).href,
          centerBtn: {
            text: "我知道了",
            command: () => (this.confirmOption.isShow = false),
          },
          isShow: true,
        });
      }
    });
  },
  watch: {
    isSearched: function () {
      if (this.isSearched) this.isShowDirTree = false;
    },
  },
  computed: {
    selectedItems() {
      return this.currentData.filter((data) =>
        this.selectedItemIds.includes(data.id)
      );
    },
  },
};
</script>

<style scoped>
.rename::selection {
  background-color: #dcedfd;
  color: #1d2939;
}

.resize-mouse {
  cursor: ew-resize;
}

.file-name {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  line-clamp: 3;
  line-height: 160%;
}
</style>
