<?php

namespace App\Modules\par\Listeners;

use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use App\Mail\notification;
use App\Modules\par\Events\notificationEvent;
use Exception;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Log;

class SendEmailNotification implements ShouldQueue
{
    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     *
     * @param  object  $event
     * @return void
     */
    public function handle(notificationEvent $event)
    {
        try {
            Mail::to(Config::get('mail.from.address'))
                ->bcc($event->data['email'])
                ->queue(new notification($event->data));
        } catch (Exception $e) {
            Log::error('mail:' . $event->data['email']);
            Log::error($e);
        }
    }
}
