<?php

namespace App\Modules\par\Services;

use App\Jobs\CancelReserveNotify;
use App\Modules\Demand\Models\Notification;
use App\Modules\par\Controllers\CommonController;
use App\Modules\par\Models\Apparatus;
use App\Modules\par\Models\Postulate;
use App\Modules\par\Models\Reserve;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\Rule;
use Exception;
use Carbon\Carbon;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Storage;
use Intervention\Image\Facades\Image;

class PaService extends PublicService
{
    protected $company_id;
    protected $user_id;
    protected $timezone;


    public function __construct()
    {
        $this->company_id = Session::get('CompanyId');
        $this->user_id = Session::get('employee_id');
        $this->timezone = Session::get('timezone');
    }

    public function validatorImage($files)
    {
        $rules =  [
            'upload' =>  'required',
            'upload.*' =>  'image|mimes:jpg,jpeg,heif,hevc,png,HEIF,HEVC|max:10240',
        ];

        $msg = [
            'upload.required' => '最少1張圖片!',
            'upload.*.max' => '圖片最大只能10mb!',
            '*.image' => '只能上傳上傳圖片!',

        ];
        $validator = Validator::make($files, $rules, $msg);

        return $validator;
    }
    // update ，$id必填
    public function validatorPostulate($form, $isUpdate = false, $id = 0)
    {
        $companyId = $this->company_id;
        $rules =  [
            'name' => [
                'required',
                Rule::unique('App\Modules\par\Models\Postulate', 'name')
                    ->where(function ($query) use ($companyId) {
                        return $query->where('company_id', $companyId);
                    })->whereNull('deleted_at'),
            ],
            'people' => 'required|numeric',
            'place' => 'required',
            'remark' => 'required|max:255',
            'images' =>  'required',
        ];
        if ($isUpdate) {
            $rules['name'] = [
                'required',
                Rule::unique('App\Modules\par\Models\Postulate', 'name')->where(function ($query) use ($companyId) {
                    return $query->where('company_id', $companyId);
                })->whereNull('deleted_at')->ignore($id),
            ];
        }

        $msg = [
            'name.required' => '請輸入名稱!',
            '*.unique' => ':input已存在，請重新輸入!',
            'people.required' => '請輸入人數!',
            'people.numeric' => '人數請輸入數字!',
            'place.required' => '請輸入地點!',
            'remark.required' => '請輸入說明!',
            'remark.max' => '說明字數不可超過:max字',
            'images.required' => '最少1張圖片!',
        ];
        $validator = Validator::make($form, $rules, $msg);

        return $validator;
    }
    // update ，$id必填
    public function validatorApparatus($form, $isUpdate = false, $id = 0)
    {
        $companyId = $this->company_id;
        $rules =  [
            'name' =>  [
                'required',
                Rule::unique('App\Modules\par\Models\Apparatus', 'name')
                    ->where(function ($query) use ($companyId) {
                        return $query->where('company_id', $companyId);
                    })->whereNull('deleted_at'),
            ],
            'no' => [
                'required',
                Rule::unique('App\Modules\par\Models\Apparatus', 'payload->no')
                    ->where(function ($query) use ($companyId) {
                        return $query->where('company_id', $companyId);
                    }),
            ],
            'type' => 'required',
            'amount' => 'required|numeric',
            'remark' => 'required|max:255',
            'images' => 'required',

            'org_unit_id' => 'required|numeric',
        ];
        if ($isUpdate) {
            $rules['name'] = [
                'required',
                Rule::unique('App\Modules\par\Models\Apparatus', 'name')
                    ->where(function ($query) use ($companyId) {
                        return $query->where('company_id', $companyId);
                    })->ignore($id),
            ];
            $rules['no'] = [
                'required',
                Rule::unique('App\Modules\par\Models\Apparatus', 'payload->no')
                    ->where(function ($query) use ($companyId) {
                        return $query->where('company_id', $companyId);
                    })->ignore($id),
            ];
        }
        $msg = [
            'name.required' => '請輸入名稱!',
            '*.unique' => ':input已存在，請重新輸入!',
            'type.required' => '請輸入設備分類!',
            'no.required' => '請輸入編號!',
            'amount.required' => '請輸入數量!',
            'amount.numeric' => '數量請輸入數字!',
            'remark.required' => '請輸入說明!',
            'remark.max' => '說明字數不可超過:max字',

            'images.required' => '最少1張圖片!',
            // '*.image' => '只能上傳上傳圖片!',
            // 'upload.*.max' => '圖片最大只能10mb!',
            // 'images.*.max' => '圖片最大只能10mb!',
            'org_unit_id.required' => '請輸入所屬部門!',
            'org_unit_id.numeric' => '部門資料輸入錯誤!',
        ];
        $validator = Validator::make($form, $rules, $msg);

        return $validator;
    }
    // update ，$id必填
    public function validatorRecipients($form)
    {
        $companyId = $this->company_id;
        $rules =  [
            'employee_id' =>  [
                'required'
            ],
            'apparatus_id' => [
                'required'
            ]
        ];
        $msg = [
            'employee_id.required' => '請輸入名稱!',
            'apparatus_id.required' => '請輸入設備分類!',
        ];
        $validator = Validator::make($form, $rules, $msg);

        return $validator;
    }
    // 上傳圖片
    public function uploadImage($file)
    {
        $filePath = [];
        $path = storage_path('app/public/par/image/temp');
        if (!File::exists($path)) File::makeDirectory($path, 777, true);


        foreach ($file as $key => $value) {
            $fileNewName = md5(time() . '_' . $value->getClientOriginalName()) . '.' . $value->getClientOriginalExtension();

            Image::make($value)->fit(208, 160, function ($constraint) {
                $constraint->upsize();
            })->save($path . '/' . $fileNewName);

            $filePath[] = [
                'file_name' => $value->getClientOriginalName(),
                'file_path' => '/par/image/temp/' . $fileNewName,
                'file_show' => '/storage/par/image/temp/' . $fileNewName
            ];
        }
        return $filePath;
    }

    // 將temp圖片移到正確資料夾，若有移除的圖片也要移除
    public function updateImageName($filePath, $directoryName, $item = null)
    {
        if ($item) {
            // 取差集
            $result = array_column(array_diff_key($item->payload['picture'], $filePath), 'file_path');
            Storage::disk('public')->delete($result);
        }
        foreach ($filePath as $key => &$value) {
            if (str_contains($value['file_path'], 'temp')) {
                $fileName = str_replace('temp',  $directoryName, $value['file_path']);
                Storage::disk('public')->move($value['file_path'], $fileName);

                $value['file_path'] = $fileName;
                $value['file_show'] = str_replace('temp',  $directoryName, $value['file_show']);
            }
        }

        return $filePath;
    }

    public function checkApparatusReserve($apparatus, $amount)
    {
        return  $this->checkReserve('apparatus', $apparatus->id, true)['hasNotStarted'] &&
            $apparatus->payload['amount'] >  $amount;
    }

    public function cleanNotification($ids)
    {
        Notification::where('employee_id', 0)
            ->where('payload->method', 'email')
            ->wherein('payload->reserve_id', $ids)
            ->delete();
    }

    /**
     * 取消設施/設備的預約
     * @param  int $pa_id
     * @param  string $type
     */
    public function cancelPAReserve($reserve, $type)
    {
        if ($type == 'postulate') {
            $reservesId = Reserve::wherein('payload->master_reserve_id', $reserve->pluck('id'))->select('id')->get()->pluck('id');
            if ($reservesId->isNotEmpty()) {
                $this->cleanNotification($reservesId);
                Reserve::wherein('id', $reservesId)->delete();
            }
        }
        Reserve::wherein('id', $reserve->pluck('id'))->delete();
    }
    /**
     * 取消設施/設備的預約，然後寄送通知
     * @param  collection $reserve
     * @param  string $type apparatus|postulate
     * @param  bool $close true=關閉 , false=刪除
     */
    public function cancelReserveAndNotify($reserve, $type, $close)
    {
        $this->cancelPAReserve($reserve, $type);
        $this->cleanNotification($reserve->pluck('id'));
        $emails = $this->getEmail($reserve->pluck('employee'));
        CancelReserveNotify::dispatch($emails, $reserve->first()->payload['name'], $close);
    }
}
