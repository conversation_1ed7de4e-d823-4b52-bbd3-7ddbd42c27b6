<?php

namespace App\Modules\Demand\Controllers;


use Illuminate\Http\Request;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\DB;
use App\Http\Controllers\Controller;
use App\Modules\Demand\Models\FuncAuth;
use App\Modules\Demand\Models\DataAuth;
use App\Modules\Demand\Models\DemandLayout;
use Illuminate\Support\Facades\Log;
use stdClass;


class AuthController extends Controller
{
    protected $company_id;
    protected $user_id;

    public function __construct()
    {
        $this->company_id = Session::get('CompanyId');
        $this->user_id = Session::get('employee_id');
    }
    public function fetchDataAuths()
    {
        $auths = DemandLayout::with([
            'dataAuth:layout_id,data_auths.payload->user_list as user_list,payload',
            'dataAuth.users:id,payload->name as name'
        ])->whereHas('dataAuth', function ($q) {
            $q->whereJsonContains('payload->user_list', (int) $this->user_id);
        })
            ->select('id', 'name')
            ->orderBy('id', 'asc')
            ->get();

        return $auths->each(function ($auth) {
            $auth['user_names'] = isset($auth['dataAuth']['users']) ? $auth['dataAuth']['users'] : [];
            $auth['users'] = isset($auth['dataAuth']['users']) ? $auth['dataAuth']['payload']['user_list'] : [];
            unset($auth['dataAuth']);
        });
    }

    public function addOrUpdateDataAuth(Request $request)
    {
        $payload = new stdClass();
        $auth = DataAuth::where('layout_id', $request->get('id'))->first();
        $payload->user_list = $request->get('users');
        $metadata = $auth->metadata;
        $metadata['updated_by'] = intval($this->user_id);
        DataAuth::updateOrCreate(
            ['layout_id' => $request->get('id')],
            [
                'payload' => $payload,
                'metadata' => $metadata
            ]
        );
        return 1;
    }

    public function fetchFuncAuths()
    {
        $funcAuth = FuncAuth::where('company_id', $this->company_id)
            ->select('id', 'payload')
            ->with('users:id,payload->name as name')
            ->whereNotNull('payload->user_list')
            ->where('type', 'like', 'demand%')
            ->whereJsonContains('payload->user_list', (int) $this->user_id)
            ->get();

        return $funcAuth->each(function ($auth) {
            $auth->user_list = $auth->payload['user_list'];
            $auth->auth_name = $auth->payload['auth_name'];
            unset($auth->payload);
        });
    }

    public function updateFuncAuth(Request $request)
    {
        $user_list = $request->get('users');
        $funcAuth = FuncAuth::where('company_id', $this->company_id)
            ->where('id', $request->id)
            ->first();

        $funcAuth
            ->forceFill(['payload->user_list' => $user_list])
            ->save();

        // $payload = [
        //     'user_list' =>$user_list,
        //     'par_list' => $funcAuth->payload["par_list"] ?? []
        // ];
        // FuncAuth::updateOrCreate(
        //     ['id' => $request->get('id')],
        //     [
        //         'company_id' => intval($this->company_id),
        //         'payload' => $payload
        //     ]
        // );
        return 1;
    }
}
