<?php

namespace App\Modules\par\Controllers;

use App\Exceptions\ParException;
use App\Modules\par\Models\Apparatus;
use App\Modules\par\Models\Postulate;
use App\Modules\par\Models\Reserve;
use App\Modules\Demand\Models\Code;
use App\Modules\Demand\Models\OrgUnitMember;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Cache;
use App\Http\Controllers\Controller;
use App\Mail\notification as MailNotification;
use App\Modules\Demand\Models\Employee;
use App\Modules\Demand\Models\Notification;
use App\Modules\Demand\Models\OrgUnit;
use App\Modules\par\Events\notificationEvent;
use App\Modules\par\Services\PublicService;
use App\Modules\par\Services\ReserveService;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;
use Carbon\CarbonPeriod;
use Exception;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Mail;

class ReserveController extends Controller
{
    protected $reserveService;
    protected $company_id;
    protected $user_id;
    protected $timezone;
    protected $code;

    protected int $originReserveId = 0;

    public function __construct()
    {
        $this->reserveService = new ReserveService();
        $this->company_id = Session::get('CompanyId');
        $this->user_id = Session::get('employee_id');
        $this->timezone = Session::get('timezone');
        // $this->timezone = 'Asia/Taipei';
    }

    // 依日期查詢預約狀況
    public function fetchPostulate(Request $request)
    {
        // 取得現在時間區段的code id
        $id = $request->id;
        if (!$request->date)
            return 0;

        $date = Carbon::parse($request->date)->toISOString();

        // 要加現在時段的判斷
        $postulate = Postulate::with([
            'reserve' => function ($query) use ($date) {
                $query->date($date)
                    ->notRelease();
            },
            'reserve.employee'
        ])
            ->company()
            ->isOpen()
            ->when($id, function ($query) use ($id) {
                return $query->where('id', $id);
            })
            ->orderby('id')
            ->get();

        $timePeriod = CommonController::fetchReserveTimePeriod();
        $orgUnitMember = OrgUnitMember::with('orgUnit')->get();

        $list = $postulate->map(function ($item) use ($timePeriod, $orgUnitMember) {
            $reserves = $timePeriod->map(function ($period) use ($item, $orgUnitMember) {
                $timeId = $period->id;
                $detail = $item->reserve
                    ->filter(function ($value) use ($timeId) {
                        return in_array($timeId, $value->payload['time']);
                    })
                    ->map(function ($value) use ($orgUnitMember) {
                        $orgUnit = $orgUnitMember->firstWhere('employee_id', $value->employee_id) ? $orgUnitMember->firstWhere('employee_id', $value->employee_id)->orgUnit : null;
                        return [
                            'subscribers' => $orgUnit->payload['name'] . ' ' . $value->employee->payload['name'],
                            'reason' => $value->payload['reason'],
                        ];
                    })
                    ->values();

                return [
                    'id' => $period->id,
                    'duration' => $period->name,
                    'detail' => $detail,
                    'disabled' => $detail->isNotEmpty(),
                ];
            });

            return [
                "id" => $item->id,
                "image_url" => $item->payload['picture'],
                "name" => $item->name,
                'people' => $item->payload['people'],
                'place' => $item->payload['place'],
                "remark" => $item->payload['remark'],
                "apparatus_type" => $item->payload['apparatus_type'] ?? [],
                "reserves" => $reserves,
            ];
        });
        return $list;
    }

    /** 依會議室查詢可預約時段
     */
    public function fetchPostulateByDate(Request $request)
    {
        // 取得現在時間區段的code id
        $pa_id = $request->id;
        $start_date = Carbon::parse($request->start_date)->setTimezone('Asia/Taipei');
        $end_date = Carbon::parse($request->end_date)->setTimezone('Asia/Taipei');

        // 找出區間內所有時段
        $code = Code::where('code_kind', 'AA')->get()->pluck('nm_zh_tw', 'nm_en_us')->toArray();
        $period = CarbonPeriod::create($start_date, $end_date);

        // 移除六日
        $period = $period->filter(fn($date) => !$date->isWeekend());

        $periods = [];
        foreach ($period as $date) {
            $uniformDate = sprintf("%s(%s)", $date->timezone('Asia/Taipei')->format('m/d'), $code[$date->format('l')]);
            $ISOSDate = $date->toISOString();
            $periods[] = ['uniformDate' => $uniformDate, 'ISOSDate' => $ISOSDate];
        }

        // 最長只能查詢七天
        if (count($periods) > 7) {
            $periods = array_slice($periods, 0, 7);
        }

        // 尋找在區間內的該設施資料
        $postulate = Postulate::with([
            'reserve' => function ($query) use ($start_date, $end_date) {
                $query->wherebetween('payload->date', [$start_date->toISOString(), $end_date->toISOString()])
                    ->notRelease();
            },
            'reserve.employee'
        ])
            ->company()
            ->isOpen()
            ->where('id', $pa_id)
            ->orderby('id')
            ->first();
        $timePeriods = CommonController::fetchReserveTimePeriodPerHour();

        $orgUnitMember = OrgUnitMember::withTrashed()->with('orgUnit')->get()
            ->pluck('orgUnit', 'employee_id');

        // 依據時間區段天數回傳資料筆數
        $list = collect($periods)
            ->map(function ($period) use ($postulate, $timePeriods, $orgUnitMember) {

                // 處理設施的每日預約資料
                $reserves = $timePeriods->map(function ($timePeriod) use ($postulate, $orgUnitMember, $period) {
                    $timeId = $timePeriod->ids;
                    $detail = $postulate->reserve
                        ->filter(function ($value) use ($timeId, $period) {
                            return $value->payload['date'] === $period['ISOSDate'] && (in_array($timeId[0], $value->payload['time']) || in_array($timeId[1], $value->payload['time']));
                        })
                        ->map(function ($value) use ($orgUnitMember) {
                            $orgUnit = $orgUnitMember[$value->employee_id];

                            return [
                                'subscribers' => $orgUnit->payload['name'] . ' ' . $value->employee->payload['name'],
                                'reason' => $value->payload['reason'],
                            ];
                        })
                        ->values();
                    return [
                        'ids' => $timePeriod->ids,
                        'duration' => $timePeriod->name,
                        'detail' => $detail,
                        'disabled' => $detail->isNotEmpty(),
                    ];
                });

                return [
                    "id" => $postulate->id,
                    "image_url" => $postulate->payload['picture'],
                    "name" => $postulate->name,
                    "ISOSDate" => $period['ISOSDate'],
                    "date" => $period['uniformDate'],
                    'people' => $postulate->payload['people'],
                    'place' => $postulate->payload['place'],
                    "remark" => $postulate->payload['remark'],
                    "apparatus_type" => $postulate->payload['apparatus_type'] ?? [],
                    "reserves" => $reserves,
                ];
            });

        return $list;
    }

    public function fetchChangePostulate(Request $request)
    {
        // 取得現在時間區段的code id
        $id = $request->id;
        if (!$request->date)
            return 0;

        $date = Carbon::parse($request->date)->toISOString();

        // 要加現在時段的判斷
        $postulate = Postulate::with([
            'reserve' => function ($query) use ($date) {
                $query->date($date)
                    ->notRelease();
            },
            'reserve.employee'
        ])
            ->company()
            ->isOpen()
            ->when($id, function ($query) use ($id) {
                return $query->where('id', $id);
            })
            ->orderby('id')
            ->get();

        $timePeriod = CommonController::fetchReserveTimePeriod();

        $list = $postulate->map(function ($item) use ($timePeriod) {
            $reserves = $timePeriod->map(function ($period) use ($item) {
                $timeId = $period->id;
                $detail = $item->reserve
                    ->filter(function ($value) use ($timeId) {
                        return in_array($timeId, $value->payload['time']);
                    })
                    ->map(function ($value) {
                        return [
                            'subscribers' => $value->employee->payload['job_title'] . '  ' . $value->employee->payload['name'],
                            'reason' => $value->payload['reason'],
                        ];
                    })
                    ->values();

                return [
                    'id' => $period->id,
                    'duration' => $period->name,
                    'detail' => $detail,
                    'disabled' => $detail->isNotEmpty(),
                ];
            });
            return [
                "id" => $item->id,
                "image_url" => $item->payload['picture'],
                "name" => $item->name,
                'people' => $item->payload['people'],
                'place' => $item->payload['place'],
                "remark" => $item->payload['remark'],
                "apparatus_type" => $item->payload['apparatus_type'] ?? [],
                "reserves" => $reserves,
            ];
        });
        return $list;
    }
    public function fetchApparatus(Request $request)
    {
        // try {
        $id = $request->id;
        // $today = now()->setTimezone($this->timezone)->format('Y-m-d');
        if (!$request->date)
            return 0;
        $date = Carbon::parse($request->date)->toISOString();
        // 要加現在時段的判斷
        $apparatus = Apparatus::with([
            'reserve' => function ($query) use ($date) {
                $query->date($date)
                    ->notRelease();
            },
            'reserve.employee:id,payload->job_title as job_title,payload->name as name',
            'notified:id,payload->name as name',
            'notified.orgs',
        ])
            ->isOpen()
            ->company()
            ->when($id, function ($query) use ($id) {
                return $query->where('id', $id);
            })
            ->orderby('id')
            ->get();
        $timePeriod = CommonController::fetchReserveTimePeriod();
        $list = $apparatus->map(function ($item) use ($timePeriod) {
            $reserves = $timePeriod->map(function ($period) use ($item) {

                $timeId = $period->id;
                $amount = $item->reserve->sumReservesAmount($timeId);
                $detail = $item->reserve
                    ->filter(function ($value) use ($timeId) {
                        return in_array($timeId, $value->payload['time']);
                    })
                    ->map(function ($value) {
                        return [
                            'subscribers' => $value->employee->job_title . '  ' . $value->employee->name,
                            'amount' => $value->payload['amount'],
                            'reason' => $value->payload['reason'],
                        ];
                    })
                    ->values();

                return [
                    'id' => $period->id,
                    'duration' => $period->name,
                    'amount' => $item->payload['amount'] - $amount,
                    'detail' => $detail->toArray(),
                    'disabled' => $item->payload['amount'] - $amount == 0 ? true : false,
                ];
            });
            return [
                "id" => $item->id,
                "image_url" => $item->payload['picture'],
                "name" => $item->name,
                "total_amount" => $item->payload['amount'],
                // "org_unit" => $item->orgs->name,
                // 'notifieds'=>count($item->notified)>1?($item->notified[0]->orgs[0]->payload['name'].' '. $item->notified[0]->name.' ...'):($item->notified[0]->orgs[0]->payload['name'].' '. $item->notified[0]->name),
                'notifieds' => $item->notified->map(function ($value) {
                    return [
                        'org' => $value->orgs[0]->payload['name'],
                        'name' => $value->name,
                    ];
                }),
                "remark" => $item->payload['remark'],
                "reserves" => $reserves,
            ];
        });
        return $list;
        // } catch (Exception $e) {
        //     Log::error($e);
        // }
    }

    public function fetchDetail(Request $request)
    {
        $id = $request->input('id');
        $type = $request->input('type');
        if (
            !isset($id) ||
            !isset($type) ||
            !($type == 'postulate' || $type == 'apparatus')
        )
            return $this->errorReturn('系統錯誤');

        $reserve = Reserve::where('id', $id)
            ->when(
                $type == 'postulate',
                function ($query) use ($id) {
                    return $query->with('postulate')->has('postulate');
                },
                function ($query) use ($id) {
                    return $query->with('apparatus', 'apparatus.orgs')->has('apparatus');
                }
            )
            ->first();

        if (!isset($reserve))
            return $this->errorReturn('此設施/設備，已不存在!');

        if ($type == 'postulate') {
            $postulate = $reserve->postulate;
            $list = [
                'image_url' => $postulate->payload['picture'],
                'name' => $postulate->name,
                'remark' => $postulate->payload['remark'],
                'place' => $postulate->payload['place'],
                'people' => $postulate->payload['people'],
                'total_amount' => null,
                'org_unit' => null,
                'reserves' => []
            ];
        } else {
            $apparatus = $reserve->apparatus;
            $notified = Employee::whereIn('id', $apparatus->payload['notified'])
                ->select(
                    DB::raw("payload->>'job_title' as org"),
                    DB::raw("payload->>'name' as name")
                )
                ->get();
            $list = [
                'image_url' => $apparatus->payload['picture'],
                'name' => $apparatus->name,
                'remark' => $apparatus->payload['remark'],
                'place' => null,
                'people' => null,
                'total_amount' => $apparatus->payload['amount'],
                'org_unit' => $apparatus->orgs->name,
                'reserves' => [],
                'notifieds' => $notified
            ];
        }
        $list['pa_id'] = $reserve->pa_id;

        return [
            'list' => $list,
            'amount' => $reserve->payload['amount'] ?? null,
            'date' => $reserve->payload['date'],
            'times' => $reserve->payload['time'],
            'reason' => $reserve->payload['reason'],
            'remark' => $reserve->payload['remark'],
            'apparatus' => $reserve->payload['apparatus'] ?? null,
        ];
    }

    /**
     * 預約公設
     * @param \Illuminate\Http\Request $request
     * @throws \App\Exceptions\ParException
     */
    public function reservePostulate(Request $request)
    {
        try {
            // 1. 驗證與準備資料
            [$error, $form] = $this->validateAndPrepareForm($request);

            if ($error) {
                return $error;
            }

            $this->reserveService->createReserveLock($form['form']['name'], $form['date']);

            DB::beginTransaction();
            // 2. 檢查時間可用性
            $error = $this->checkTimeAvailability($form);
            if ($error) {
                $this->reserveService->releaseReserveLock($form['form']['name'], $form['date']);
                return $error;
            }

            // 3. 建立預約
            // 取得修改預約的id
            $reId = $request->input('id');

            // 3.1 處理 ICS
            [$form, $subject, $uidForDb] = $this->handleICS($form, $reId);

            // 將 $sequence (此時已是遞增後的值，用於ICS) 傳遞給 createReserve
            $reserve = $this->createReserve($form, $uidForDb);

            // 3.5 回填最新的 reId
            // if (empty($reId)) {
            //     $reId = $reserve->id;
            // }

            // 4. 處理通知
            $this->handleNotifications($form, $reId, $reApparatus ?? null, $subject);

            // 5. 更新 sequence 存入資料庫
            // $this->updateSequence($reId, $sequence);

            DB::commit();
            // 釋放預約鎖定
            $this->reserveService->releaseReserveLock($form['form']['name'], $form['date']);
            return ['state' => 1];
        } catch (Exception $e) {
            Log::error($e->getMessage());
            Log::error($e);
            DB::rollBack();
            throw new ParException($e->getMessage());
        }
    }

    /**
     * 驗證表單並準備資料
     * @param Request $request
     * @return array
     */
    private function validateAndPrepareForm(Request $request)
    {
        $form = $request->form;
        $validator = $this->reserveService->validatorPostulate($form);

        if ($validator->fails()) {
            return [
                $this->errorReturn([$validator->errors()->first()]),
                null
            ];
        }

        $date = Carbon::parse($form['date']);
        sort($form['time_ids']);

        $apparatus = array_filter(
            array: $form['apparatus'] ?? [],
            // $item['type'] 是設施的類型，例如 '器材'、'場地'，這裡判斷 $item['type'] 存在且不為空
            // 這樣可以避免 $form['apparatus'] 中的空元素
            callback: fn($item) => !empty($item) && (isset($item['type']) && !empty($item['type']))
        );
        $apparatus = array_values($apparatus);

        return [
            null,
            [
                'form' => $form,
                'date' => $date,
                'apparatus' => $apparatus
            ]
        ];
    }

    /**
     * 檢查時間可用性
     * @param array $formData
     */
    private function checkTimeAvailability(array $formData)
    {
        $errorTime = $this->reserveService->checkPostulateTimeReserve(
            'postulate',
            $formData['form']['pa_id'],
            $formData['date']->toISOString(),
            $formData['form']['time_ids']
        );

        if (count($errorTime) > 0) {
            return $this->errorReturn($errorTime);
        }
        return null;
    }

    /**
     * 處理 ICS
     * @param array $form
     * @param int $reId
     * @return array
     */
    private function handleICS(array $form, int $reId = null)
    {
        $icsType = 'CREATE';
        // $sequence = 0;
        $subject = '';

        /* 處理再次預約的情況 */
        if (isset($reId)) {
            $isReReserve = Reserve::withTrashed()->find($reId);
            $reApparatus = $isReReserve->payload->get('apparatus');
            // $sequence = intval($isReReserve->payload->get('sequence', 0)); // 取得原始 SEQUENCE.

            if (isset($isReReserve->payload['ics_uid'])) {
                $icsType = 'UPDATE';
            }
        }

        /* 處理 ICS */
        /* 
            有勾選設備/再次預約有勾選設備 
        */
        if (!empty($form['apparatus']) || !empty($reApparatus)) {
            $ics = $this->createICS($form, $icsType);
            $subject = $this->reserveApparatusSubject($form['date'], $icsType, $form['form']['reason']);

            /* 如果有 isReReserve，ics_uid 存在，回填 ics_uid ，代表為同一事件。 */
            if (isset($isReReserve->payload['ics_uid'])) {
                $ics->VEVENT->UID = $isReReserve->payload['ics_uid'];
                // $ics->VEVENT->SEQUENCE = $sequence;
            }
        }

        /* 原本有勾選設備，現在取消設備，發送取消通知郵件 */
        if (empty($form['apparatus']) && !empty($reApparatus)) {
            $ics->METHOD = 'CANCEL';
            $ics->VEVENT->STATUS = 'CANCELLED';


            $subject = $this->reserveApparatusSubject($form['date'], 'REMOVE', $form['form']['reason']);
            $ics->VEVENT->SUMMARY = $subject;
            $form['ics'] = $ics;
        }


        /* 確保 $ics 存在且 VEVENT 和 UID 也存在才嘗試獲取 UID */
        $uidForDb = null;
        if (isset($ics) && isset($ics->VEVENT) && isset($ics->VEVENT->UID)) {
            $uidForDb = $ics->VEVENT->UID->getValue();
            $form['ics'] = $ics;
        }


        return [$form, $subject, $uidForDb];
    }
    /**
     * 處理 ICS 行事曆
     * @param array $formData
     * @param string $icsType 支持 'CREATE', 'UPDATE', 'CANCEL'
     * @param int $sequence
     * @return \Sabre\VObject\Component\VCalendar
     */
    private function createICS(array $formData, string $icsType = 'CREATE', int $sequence = 0)
    {
        return $this->reserveService->newIcs(
            $icsType,
            $formData['form']['name'],
            $formData['date'],
            $formData['form']['reason'],
            $formData['form']['remark'],
            $sequence,
        );
    }

    /**
     * 建立預約記錄
     * @param array $formData
     * @param string $icsUid
     * @param int $sequence
     * @return Reserve
     */
    private function createReserve(array $formData, $icsUid = null)
    {
        $payload = [
            "name" => $formData['form']['name'],
            "apparatus" => $formData['apparatus'],
            "date" => $formData['date']->toISOString(),
            "time" => $formData['form']['time_ids'],
            "reason" => $formData['form']['reason'],
            "remark" => $formData['form']['remark'],
        ];

        // 如果ics_uid存在，則增加ics_uid
        if ($icsUid) {
            $payload['ics_uid'] = $icsUid;
            // $payload['sequence'] = $sequence;
        }


        // 建立預約記錄
        return Reserve::create([
            'company_id' => $this->company_id,
            'employee_id' => $this->user_id,
            'pa_id' => $formData['form']['pa_id'],
            'type' => 'postulate',
            'payload' => $payload,
            'metadata' => [],
            'created_by' => $this->user_id,
            'updated_by' => $this->user_id
        ]);
    }

    /**
     * 處理通知
     * @param int $reId 原先預約id
     * @param array $formData 表單資料
     * @param array|null $reApparatus 再次預約的設備
     */
    private function handleNotifications(array $formData, int $reId = null, $reApparatus = null, string $subject = '')
    {
        // 有勾選設備 或 原先預約有勾選設備
        if (
            !empty($formData['apparatus']) || empty($reApparatus)
        ) {
            \Log::info('有勾選設備 或 原先預約有勾選設備');
            return;
        }

        // 設備也要預約，由後端處理
        /*

            // 暫時關閉設備
            foreach ($apparatus as $key => $value) {
                // 此項設備支援未在設備table中，固只有純文字
                if (empty($value['id'])) continue;
                $form['master_reserve_id'] =  $reserveId;
                $form['pa_id'] = $value['id'];
                $form['name'] = $value['name'];
                $form['amount'] = 1;
                $form['type'] = 'apparatus';

                $request->merge(['form' => $form]);

                $result = $this->reserveApparatus($request);

                if ($result['state'] == 0) {
                    DB::rollBack();
                    return $this->errorReturn($value['name'] . '<br>' . $result['error']);
                }
            }
        */


        $postulates = Postulate::with([
            'notified:id,payload->email as email'
        ])
            ->select('id', 'name', 'postulates.payload as payload')
            ->where('id', $formData['form']['pa_id'])
            ->company()
            ->first();

        $emails = $this->reserveService->getEmail($postulates->notified);

        // 如果沒有email，則不發送通知
        if ($emails->isEmpty()) {
            \Log::info('沒有email，不發送通知');
            return;
        }


        $this->sendNotificationEmail($formData, $emails, $postulates, $reId, $subject);
    }

    /**
     * 發送通知郵件
     */
    private function sendNotificationEmail(array $formData, $emails, $postulates, int $reId = null, string $subject = '')
    {
        $employee = Employee::find($this->user_id);
        $organizerMail = $employee->payload->get('email');

        $apparatus = array_map(function ($value) {
            return isset($value['name']) ? $value['name'] : $value['type'];
        }, $formData['apparatus']);

        /**
         * 準備郵件內容
         */
        $emailMsg = [
            [
                'user' => $employee ? $employee->payload->get('name') : '',
                'name' => $formData['form']['name'],
                'date' => $formData['date']->format('Y/m/d'),
                'time' => PublicService::idToTimePeriod($formData['form']['time_ids'])->toArray(),
                'reason' => $formData['form']['reason'],
                'remark' => nl2br(htmlspecialchars($formData['form']['remark'], ENT_QUOTES, 'UTF-8')),
                'notified' => $apparatus,
            ]
        ];

        if (isset($reId)) {
            $emailMsg[0]['isRevised'] = '設施';
        }

        $this->sendEmail($emailMsg, $emails, $formData['ics'], $subject, $organizerMail);

    }
    private function sendEmail($emailMsg, $emails, $ics = null, string $subject = '', string $organizerMail = '')
    {
        $data = [
            'subject' => empty($subject) ? config('mail.sys_title') : $subject,
            'msg' => $emailMsg,
            'email' => $emails,
            'ics' => $ics ?? null,
            'organizerMail' => empty($organizerMail) ? config('mail.from.address') : $organizerMail,
        ];

        Mail::to(config('mail.from.address'))
            ->bcc($emails)
            ->queue(new MailNotification($data));
    }

    /**
     * 預約設備
     * @param \Illuminate\Http\Request $request
     * @throws \App\Exceptions\ParException
     * @return array{error: mixed, state: int|array{state: int}}
     */
    public function reserveApparatus(Request $request)
    {
        $form = $request->form;
        $validator = $this->reserveService->validatorApparatues($form);

        if ($validator->fails())
            return $this->errorReturn([$validator->errors()->first()]);

        $this->reserveService->createReserveLock($form['name'], $form['date']);
        $date = Carbon::parse($form['date']);

        sort($form['time_ids']);
        $payload = [
            "name" => $form['name'],
            "amount" => $form['amount'],
            "date" => $date->toISOString(),
            "time" => $form['time_ids'],
            "reason" => $form['reason'],
            "remark" => $form['remark'],
        ];
        try {
            DB::beginTransaction();

            // 確認這時間是否可以預約
            $errorTime = $this->reserveService->checkApparatusTimeReserve('apparatus', $form['pa_id'], $date->toISOString(), $form['time_ids'], $form['amount']);
            if (count($errorTime) > 0) {
                $this->reserveService->releaseReserveLock($form['form']['name'], $form['date']);
                return $this->errorReturn($errorTime);
            }


            if (isset($form['master_reserve_id'])) {
                $payload['master_reserve_id'] = $form['master_reserve_id'];
            }
            $reserveId = Reserve::create([
                'company_id' => $this->company_id,
                'employee_id' => $this->user_id,
                'pa_id' => $form['pa_id'],
                'type' => 'apparatus',
                'payload' => $payload,
                'metadata' => [],
                'created_by' => $this->user_id,
                'updated_by' => $this->user_id
            ])->id;

            $apparatus = Apparatus::with([
                'notified:id,payload->email as email'
            ])
                ->select('id', 'name', 'apparatuses.payload as payload')
                ->where('id', $form['pa_id'])
                ->company()
                ->first();

            $employee = Employee::find($this->user_id);
            // 預約前一小排程寄一次
            // $notifyInfo = [
            //     'reserveId' => $reserveId,
            //     'date' => $date,
            //     'time' => $payload['time'],
            // ];
            // $this->reserveService->reserveNotify($notifyInfo);
            // 預約當下先寄送一次email
            $emails = $this->reserveService->getEmail($apparatus->notified);

            if ($emails->isNotEmpty()) {
                // ReserveNotify 是多層的，為了讓view一致，所以這邊要改成多層
                $emailMsg = [
                    [
                        'user' => $employee ? $employee->payload->get('name') : '',
                        'name' => $payload['name'],
                        'amount' => $payload['amount'],
                        'date' => $date->format('Y/m/d'),
                        'time' => PublicService::idToTimePeriod($payload['time'])->toArray(),
                        'reason' => $payload['reason'],
                        'remark' => nl2br(htmlspecialchars($payload['remark'], ENT_QUOTES, 'UTF-8')),
                    ]
                ];

                $this->sendEmail(
                    emailMsg: $emailMsg,
                    emails: $emails,
                    subject: Config::get('mail.sys_title')
                );
            }
            DB::commit();
            return ['state' => 1,];
        } catch (Exception $e) {
            Log::error('設備預約失敗');
            Log::error($e);
            DB::rollBack();
            throw new ParException('設備預約失敗');
        }
    }

    public function reserveAgain(Request $request)
    {
        try {
            DB::beginTransaction();

            // 確認這時間是否可以預約
            $date = Carbon::parse($request->form['date']);
            $errorTime = $this->reserveService->reCheckPostulateTimeReserve('postulate', $request->form['pa_id'], $date->toISOString(), $request->form['time_ids'], $request->input('id'));
            if (count($errorTime) > 0) {
                return $this->errorReturn($errorTime);
            }


            // @TODO 這邊可能會有 race condition 的問題.
            $this->reserveService->deleteReserve($request->input('id'));
            $this->originReserveId = $request->input('id');

            $result = $request->input('form')['type'] == 'postulate' ?
                $this->reservePostulate($request) :
                $this->reserveApparatus($request);
            DB::commit();
            return $result;
        } catch (Exception $e) {
            Log::error($e);
            DB::rollBack();
            return $this->errorReturn('預約失敗');
        }
    }
    public function personReserve(Request $request)
    {
        // $today = now()->setTimezone($this->timezone)->format('Y-m-d');
        $today = today(Session::get('timezone'))->toISOString();
        $timeId = $this->reserveService->getNowTimePeriodId();
        $reserve = Reserve::company()
            // ->with('postulate','apparatus')
            ->leftjoin('postulates', function ($join) {
                $join->on('reserves.pa_id', '=', 'postulates.id')
                    ->where('reserves.type', 'postulate');
            })
            ->leftjoin('apparatuses', function ($join) {
                $join->on('reserves.pa_id', '=', 'apparatuses.id')
                    ->where('reserves.type', 'apparatus');
            })
            ->where('employee_id', $this->user_id) //
            ->select(
                'reserves.*',
                'postulates.payload->picture->0->file_show as pos',
                'apparatuses.payload->picture->0->file_show as ap'
            )
            ->liveReserve($today)
            ->notRelease()
            ->orderBy('reserves.payload->date', 'asc')
            ->get();
        $reserve = $reserve->filter(function ($item) use ($today, $timeId) {
            if ($item->payload['date'] > $today)
                return true;
            // 當最後時段大於等於現在時段，就出現在列表
            return max($item->payload['time']) >= $timeId ? true : false;
        })->map(function ($item) use ($today, $timeId) {

            $date = Carbon::parse($item->payload['date'])->settimezone($this->timezone)->format('Y/m/d');
            $time = ReserveService::idToTimePeriod($item->payload['time']);

            //  0:提前結束; 1:取消預約
            if ($item->payload['date'] > $today)
                $cancelBtn = 1;
            else if ($timeId >= min($item->payload['time']))
                $cancelBtn = 0;
            else if ($timeId < min($item->payload['time']))
                $cancelBtn = 1;

            $apparatus = [];
            if ($item->type == 'postulate')
                $apparatus = array_map(function ($value) {
                    return isset($value['name']) ? $value['name'] : $value['type'];
                }, $item->payload['apparatus']);

            return [
                'id' => $item->id,
                'type' => $item->type,
                'image_url' => $item->pos ?? $item->ap,
                'name' => $item->payload['name'],
                'amount' => $item->type == 'apparatus' ? $item->payload['amount'] : null,
                'apparatus' => $apparatus,
                'date' => $date,
                'time' => $time,
                'reason' => $item->payload['reason'],
                'remark' => nl2br(htmlspecialchars($item->payload['remark'], ENT_QUOTES, 'UTF-8')),
                'cancel_btn' => $cancelBtn,
            ];
        })->values();

        return $reserve;
    }

    public function cancelReserve(Request $request)
    {
        $form = $request->all();

        $validator = $this->reserveService->validatorCancel($form);

        if ($validator->fails()) {
            return $this->errorReturn([$validator->errors()->first()]);
        }


        $id = $form['id'];
        $cancelType = $form['cancel_type'];

        // 提前釋出時是null 不用寄信
        $reserveList = $form['list'] ?? null;


        try {
            DB::beginTransaction();
            // 取消通知
            Notification::where('employee_id', 0)
                ->where('payload->method', 'email')
                ->when(
                    is_array($id),
                    function ($query) use ($id) {
                        return $query->wherein('payload->reserve_id', $id);
                    },
                    function ($query) use ($id) {
                        return $query->where('payload->reserve_id', $id);
                    }
                )->delete();


            $reserveData = Reserve::where('id', $id)->first();
            $paId = $reserveData->pa_id;
            $payload = json_decode($reserveData->payload);
            $sequence = $payload->sequence ?? 0;

            // 設施取消信 有勾選設播才要寄
            if ($reserveList && $reserveData->type === 'postulate' && !empty($payload->apparatus)) {
                $reserveApparatus = [];
                foreach ($payload->apparatus as $apparatus) {
                    if (isset($apparatus->id)) {
                        $reserveApparatus[] = [
                            'name' => $apparatus->name,
                            'id' => $apparatus->id,
                        ];
                    } elseif (isset($apparatus->type)) {
                        $reserveApparatus[] = [
                            'type' => $apparatus->type,

                        ];
                    }
                }

                $reservePostulate = $payload->name;
                $postulates = Postulate::select('id', 'name', 'postulates.payload as payload')
                    ->where('id', $paId)
                    ->company()
                    ->first();

                $emails = $this->reserveService->getEmail($postulates->notified);

                if ($emails->isNotEmpty()) {
                    $employee = Employee::find($this->user_id);
                    $organizerMail = $employee->payload->get('email');

                    // 建立ics
                    $ics = $this->reserveService->newIcs('CANCEL', $reservePostulate, Carbon::parse($payload->date), $payload->reason, $payload->remark, ++$sequence);

                    // 如果ics_uid存在，則更新ics_uid
                    if (isset($payload->ics_uid)) {
                        $ics->VEVENT->UID = $payload->ics_uid;
                        $ics->METHOD = 'CANCEL';
                        $ics->VEVENT->STATUS = 'CANCELLED';

                    }

                    // ReserveNotify 是多層的，為了讓view一致，所以這邊要改成多層
                    $emailMsg = [
                        [
                            'user' => $employee ? $employee->payload->get('name') : '',
                            'name' => $reservePostulate,
                            'date' => $reserveList['date'],
                            'time' => $reserveList['time'],
                            'reason' => $reserveList['reason'],
                            'remark' => nl2br(htmlspecialchars($reserveList['remark'], ENT_QUOTES, 'UTF-8')),
                            'notified' => array_map(
                                callback: fn($apparatus) => $apparatus['name'] ?? $apparatus['type'],
                            array: $reserveApparatus
                            ),
                            'isCanceled' => '設施'
                        ]
                    ];

                    $this->sendEmail(
                        emailMsg: $emailMsg,
                        emails: $emails,
                        ics: $ics,
                        subject: $this->reserveApparatusSubject($reserveList['date'], 'CANCEL', $reserveList['reason']),
                        organizerMail: $organizerMail
                    );

                }
                // 設施取消-若有勾選設備也要寄取消信
                // if (isset($reserveApparatus)) {
                //     foreach ($reserveApparatus as $key => $value) {
                //         // 處理先取消設施才取消勾選設備例外
                //         $isCanceledApparatus = Reserve::where('pa_id', $value['id'])->whereJsonContains('payload->master_reserve_id', $id)->first();
                //         if (!$isCanceledApparatus) continue;
                //         $apparatus = Apparatus::select('id', 'name', 'apparatuses.payload as payload')
                //             ->where('id',  $value['id'])
                //             ->company()
                //             ->first();
                //         $employee = Employee::find($this->user_id);
                //         $emails = $this->reserveService->getEmail($apparatus->notified);
                //         if ($emails->isNotEmpty()) {
                //             $employee = Employee::find($this->user_id);
                //             $emailMsg = [
                //                 [
                //                     'user' => $employee ? $employee->payload->get('name') : '',
                //                     'name' =>   $value['name'],
                //                     'date' =>  $reserveList['date'],
                //                     'time' => $reserveList['time'],
                //                     'reason' =>  $reserveList['reason'],
                //                     'remark' =>  $reserveList['remark'],
                //                     'isCanceled' => '設備'
                //                 ]
                //             ];
                //             $data = ['subject' => Config::get('mail.sys_title'), 'msg' => $emailMsg, 'email' => $emails];
                //             Mail::to(Config::get('mail.from.address'))
                //                 ->bcc($emails)
                //                 ->queue(new MailNotification($data));
                //         }
                //     }
                // }
            }
            // 設備取消信
            // elseif ($reserveData->type === 'apparatus') {
            //     $apparatus = Apparatus::select('id', 'name', 'apparatuses.payload as payload')
            //         ->where('id',  $paId)
            //         ->company()
            //         ->first();
            //     $employee = Employee::find($this->user_id);
            //     $emails = $this->reserveService->getEmail($apparatus->notified);
            //     if ($emails->isNotEmpty()) {
            //         $employee = Employee::find($this->user_id);
            //         $emailMsg = [
            //             [
            //                 'user' => $employee ? $employee->payload->get('name') : '',
            //                 'name' =>   $reserveList['name'],
            //                 'date' =>  $reserveList['date'],
            //                 'time' => $reserveList['time'],
            //                 'reason' =>  $reserveList['reason'],
            //                 'remark' =>  $reserveList['remark'],
            //                 'isCanceled' => '設備'
            //             ]
            //         ];
            //         $data = ['subject' => Config::get('mail.sys_title'), 'msg' => $emailMsg, 'email' => $emails];
            //         Mail::to(Config::get('mail.from.address'))
            //             ->bcc($emails)
            //             ->queue(new MailNotification($data));
            //     }
            // }
            // 0:提前結束; 1:取消預約
            switch ($cancelType) {
                case 0:
                    is_array($id) ?
                        $this->reserveService->releaseReserveByIds($id) :
                        $this->reserveService->releaseReserve($id);
                    break;
                case 1:
                    $this->reserveService->forceDeleteReserve($id);
                    break;
            }
            DB::commit();
            return ['state' => 1];
        } catch (Exception $e) {
            Log::error($e);
            DB::rollBack();
            $errorText = $cancelType ? '刪除失敗' : '釋出失敗';
            return $this->errorReturn($errorText);
        }
    }

    public function paDropdown(Request $request)
    {
        $type = $request->type;
        if (!$type)
            return $this->errorReturn('下拉顯示資料');

        $model = $this->reserveService->fetchModel($request->input('type'));
        return $model->select('id', 'name')->IsOpen()->orderBy('id')->get();
    }

    public function apparatusDropdown(Request $request)
    {
        if (empty($request->date) || empty($request->time) || empty($request->name))
            return $this->errorReturn('請輸入預約日期');


        $date = carbon::parse($request->date)->toISOString();
        // 檢查設備數量，若無法預約 回傳空陣列
        // 若屬於非設備中類別，回傳null
        return $this->reserveService->checkReservesApparatusWithType($date, $request->time, $request->name);
    }

    /**
     * 更新 sequence - 針對 ics 進行特別紀錄 SEQUENCE
     * @param int $reserveId
     * @param int $sequence
     * @return void
     */
    protected function updateSequence(int $reserveId, int $sequence): void
    {
        $reserve = Reserve::withTrashed()->find($reserveId, ['id', 'payload']);
        $reserve->setAttribute('payload->sequence', $sequence);
        $reserve->save();
    }

    /**
     * Summary of reserveApparatusSubject
     * @param \Carbon\Carbon $date
     * @param string $type
     * @param string|null $reason
     * @return string
     */
    protected function reserveApparatusSubject(
        string|Carbon $date,
        ?string $type = 'CANCEL', // 支援: CREATE, UPDATE, CANCEL, REMOVE
        ?string $reason = '',
    ) {
        if (empty($type)) {
            return config('mail.sys_title');
        }

        if (is_string($date)) {
            $date = Carbon::parse($date);
        }

        $prefix = match ($type) {
            'CREATE' => '【會議室預約】',
            'UPDATE' => '【會議室預約修改】',
            'CANCEL' => '【會議室預約取消】',
            'REMOVE' => '【會議室預約修改】- 取消設備支援 ',
            default => '',
        };

        return $prefix . $date->setTimezone('Asia/Taipei')->format('Y/m/d') . ' ' . $reason;
    }
}
