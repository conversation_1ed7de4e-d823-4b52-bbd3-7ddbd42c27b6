<?php

namespace App\Modules\Demand\Models;

use Illuminate\Database\Eloquent\Model;
use App\Modules\Demand\Models\Company;
// use App\Modules\Demand\Models\DataAuth;
use Illuminate\Database\Eloquent\SoftDeletes;

class OrgUnit extends Model
{
    // use \Staudenmeir\EloquentJsonRelations\HasJsonRelationships;
    use SoftDeletes;

    protected $fillable = ['id','company_id','payload', 'metadata'];
    protected $casts =['payload' => 'collection','metadata' => 'collection'];
    public function childs()
    {
        return $this->hasMany(OrgUnit::class,'metadata->parent');
    }
    public function employees()
    {
        return $this->hasManyThrough(Employee::class,OrgUnitMember::class,'org_unit_id','id','id','employee_id');
    }

    public function company()
    {
        return $this->belongsTo(Company::class);
    }

}
