<?php

namespace App\Modules\Demand\Controllers;


use App\Http\Controllers\Controller;
use App\Modules\Demand\Models\Company;
use App\Modules\Demand\Models\DataAuth;
use App\Modules\Demand\Models\Demand;
use App\Modules\Demand\Models\DemandLayout;
use App\Modules\Demand\Models\DemandLog;
use App\Modules\Demand\Models\Employee;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Session;

class HomeController extends Controller
{
    public function main(Request $request)
    {
        // if(!Session::has('employee_id'))
        // $id = $request->get('id');
        // Session::put('employee_id', $id);
        return redirect()->route('demand');
    }

    public function index(Request $request)
    {
        if (config('app.debug')) {
            $id = $request->get('id');
            $employee = Employee::where('id', $id)->first();
            if (!$employee) {
                return redirect('/');
            }
            Session::put('employee_id', $id);
            $e = Employee::find($id);
            Session::put('CompanyId', $e->company_id);

            $c = Company::find($employee->company_id);
            if ($c && $c->payload->has('timezone')) {
                $timezone = $c->payload->get('timezone');
                Session::put('timezone', $timezone);
            }

            return redirect()->route('list');
        }
    }
    public function reBuildDemandLayouts()
    {
        $ds = Demand::get();
        $ds->each(function ($d) {
            $payload = $d->payload;
            $columns = collect($payload['forms'][0]['columns'])->map(function ($column) {
                unset($column['value']);
                return $column;
            });
            $name = $payload['layout_name'];
            $sign_role = $payload['sign_roles'];
            $newPayload = [
                'columns' => $columns,
                'sort' => 0,
                'remarks' => [],
                'import_from_others' => false
            ];
            $dl = DemandLayout::firstOrCreate(
                ['name' => $name],
                [
                    'group_id' => 106,
                    'open' => true,
                    'payload' => $newPayload,
                    'sign_role' => $sign_role,
                    'can_multi' => true,
                    'auto_finished' => false,
                    'created_by' => '11',
                    'updated_by' => '11'
                ]
            );
            DataAuth::firstOrCreate([
                'layout_id' => $dl->id
            ], [
                'payload' => [
                    'user_list' => [569, 11, 497, 7, 4, 10, 8]
                ],
                'metadata' => ['created_by' => 11]
            ]);
        });
        $dlgs = DemandLog::get();
        $dlgs->each(function ($d) {
            $payload = $d->payload;
            $columns = collect($payload['forms'][0]['columns'])->map(function ($column) {
                unset($column['value']);
                return $column;
            });
            $name = $payload['layout_name'];
            $sign_role = $payload['sign_roles'];
            $newPayload = [
                'columns' => $columns,
                'sort' => 0,
                'remarks' => [],
                'import_from_others' => false
            ];
            $dl = DemandLayout::firstOrCreate(
                ['name' => $name],
                [
                    'group_id' => 106,
                    'open' => true,
                    'payload' => $newPayload,
                    'sign_role' => $sign_role,
                    'can_multi' => true,
                    'auto_finished' => false,
                    'created_by' => '11',
                    'updated_by' => '11'
                ]
            );
            DataAuth::firstOrCreate([
                'layout_id' => $dl->id
            ], [
                'payload' => [
                    'user_list' => [569, 11, 497, 7, 4, 10, 8]
                ],
                'metadata' => ['created_by' => 11]
            ]);
        });
    }
}
