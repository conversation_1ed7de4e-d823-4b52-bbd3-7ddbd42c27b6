<?php

namespace App\Modules\Demand\Controllers;


use Illuminate\Http\Request;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Log;
use App\Http\Controllers\Controller;
use App\Modules\Demand\Models\Database;
use App\Modules\Demand\Models\ListLayout;
use App\Modules\Demand\Models\Code;
use App\Modules\Demand\Repositories\DemandQueryRepository;
use \PhpOffice\PhpSpreadsheet\IOFactory;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use \PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use App\Modules\Demand\Services\FastExcelService;
use Carbon\Carbon;
use Exception;




class ExcelController extends Controller
{
    protected $company_id;
    protected $user_id;


    public function __construct()
    {
        $this->company_id = Session::get('CompanyId');
        $this->user_id = Session::get('employee_id');
    }

    public function exportDatabaseToPage(Request $request)
    {
        $databases = Database::where(
            'payload->company_id',
            $this->company_id
        )->where('id', $request->get('id'))
            ->select('payload->columns as columns', 'id', 'name')
            ->orderBy('id', 'asc')
            ->get();

        $data = $databases->each(function ($db) {
            $db->columns = collect(json_decode($db['columns']));
        })->first();

        $request->docName = $data->name; //檔名
        $request->names = $data->columns->pluck('name'); //欄位
        $request->mata = $data->columns->pluck('data'); //資料
        return $this->export($request);
    }

    public function exportListLayoutToPage(Request $request)
    {
        $lists = ListLayout::with('layout:id,name')
            ->where(
                'payload->company_id',
                $this->company_id
            )->where('id', $request->get('id'))
            ->select('payload->columns as columns', 'demand_layout_id', 'payload->column_id as column_id')
            ->get();

        $data = $lists->each(function ($list) {
            $list->columns = collect(json_decode($list['columns']));
            $list->name = $list->layout['name'];
        })->first();

        $rowNames = [];
        $rowCount = [];
        $rowUnit = [];
        $rowPrice = [];
        $rowsTotal = [];
        $rowsMemo = [];
        $names = [];
        $getFirstColumns = 0;
        $codeTable = Code::all();
        $itemNames = $codeTable->where('code_kind', 'AF');
        foreach ($data->columns as $col) {

            if (isset($col->name)) {
                if (!$getFirstColumns) array_push(
                    $names,
                    $itemNames->where('code_id', 'name')->first() ? $itemNames->where('code_id', 'name')->first()->nm_zh_tw : ""
                );
                array_push($rowNames, $col->name);
            }
            if (isset($col->count)) {
                if (!$getFirstColumns) array_push(
                    $names,
                    $itemNames->where('code_id', 'count')->first() ? $itemNames->where('code_id', 'count')->first()->nm_zh_tw : ""
                );
                array_push($rowCount, $col->count);
            }
            if (isset($col->unit)) {
                if (!$getFirstColumns) array_push(
                    $names,
                    $itemNames->where('code_id', 'unit')->first() ? $itemNames->where('code_id', 'unit')->first()->nm_zh_tw : ""
                );
                array_push($rowUnit, $col->unit);
            }
            if (isset($col->price)) {
                if (!$getFirstColumns) array_push(
                    $names,
                    $itemNames->where('code_id', 'price')->first() ? $itemNames->where('code_id', 'price')->first()->nm_zh_tw : ""
                );
                array_push($rowPrice, $col->price);
            }
            if (isset($col->total)) {
                if (!$getFirstColumns) array_push(
                    $names,
                    $itemNames->where('code_id', 'total')->first() ? $itemNames->where('code_id', 'total')->first()->nm_zh_tw : ""
                );
                array_push($rowsTotal, $col->total);
            }
            if (isset($col->memo)) {
                if (!$getFirstColumns) array_push(
                    $names,
                    $itemNames->where('code_id', 'memo')->first() ? $itemNames->where('code_id', 'memo')->first()->nm_zh_tw : ""
                );
                array_push($rowsMemo, $col->memo);
            }
            if (!$getFirstColumns) $getFirstColumns++;
        }

        $colName = $codeTable->where('code_kind', 'AE')->where('code_id', 'column')->first();
        $colName = $colName ? $colName->nm_zh_tw : '';
        $listName = $codeTable->where('code_kind', 'AB')->where('code_id', 'list')->first();
        $listName = $listName ? $listName->nm_zh_tw : '';
        $request->docName = $data->name . $colName . $data->column_id . $listName; //檔名
        $request->names = $names;
        //資料欄位們固定排序
        $request->mata = [
            $rowNames,
            $rowCount,
            $rowUnit,
            $rowPrice,
            $rowsTotal,
            $rowsMemo
        ];
        return $this->export($request);
    }

    //  ### Export Reviewed Demand History [GET /api/demand/reviewed/history/export]
    // Exports the demand history based on the provided filters.

    // + Request (application/json)
    //    + Attributes
    //        + start: `2024-02-14T09:02:29.714Z` (string, required) - The start date and time for the demand history.
    //        + end: `2024-03-14T09:02:29.714Z` (string, required) - The end date and time for the demand history.
    //        + type: 0 (number, required) - The type of demand.
    //        + layout_id: 8 (number, required) - The layout ID for the demand.
    //        + review: 0 (number, required) - The review status of the demand.
    public function exportSignHistory(Request $request, FastExcelService $fastExcelService, DemandQueryRepository $demandQuery)
    {
        //檢查變數是否有缺失
        $validatedData = $request->validate([
            'start' => 'required|date',
            'end' => 'required|date|after:start',
            'type' => 'required|integer',
            'layout_id' => 'required|integer',
            'review' => 'required|integer',
            'docName' => 'required|string',

        ]);
        // 結束日期要加一天
        $end = Carbon::parse($request->get('end'))->addDay()->toIso8601String();
        $docName = $request->input('docName');
        $data = [];

        $getDemands = $demandQuery->fetchDemandQuery(0, (int)$request->review, (int)$request->type, $request->key_word, $request->layout_id, $request->start, $end);

        $getDemands->chunk(500, function ($demands) use (&$data) {
            foreach ($demands as $demand) {
                $applicant =  $demand->payload->get('applicant');
                if (!$applicant) {
                    $applicant['name'] = $demand->employee['payload->name'];
                    $applicant['title'] = $demand->employee['payload->job_title'];
                    $applicant['dep'] = $demand->employee->orgs[0]->payload['name'] ?? '-';
                }
                foreach ($demand->payload['forms'] as $form) {
                    $columns = [];
                    foreach ($form['columns'] as $column) {
                        if ($column['type'] === 'customList' || $column['type'] === 'list') continue;
                        $columns[$column['name']] = isset($column['value']) ? (is_array($column['value']) ? $column['value']['name'] ?? $column['value'][0] : $column['value']) : '-';
                    }
                    $data[] = [
                        '需求單名稱' => $demand->payload->get('layout_name'),
                        '申請日期' => $demand->created_at->format('Y-m-d H:i:s'),
                        '需求單號' => $demand->no,
                        '姓名' => $applicant['name'],
                        '部門' => $applicant['dep'],
                        '職稱' => $applicant['title'],
                    ] + $columns;
                }
            }
        });

        return $fastExcelService->export($data, $docName);
    }

    public function importDatabase(Request $request)
    {
        $errors = [];
        $file = $request->file;
        $codeTable = Code::where('code_kind', 'AG')->get();
        $isExcel = strtolower($file->getClientOriginalExtension());
        if ($isExcel != 'xlsx') {
            $err = $codeTable->where('code_id', 'err2')->first() ? $codeTable->where('code_id', 'err2')->first()->nm_zh_tw : '';
            array_push($errors, $err);
            return ['success' => 0, 'errors' => $errors];
        };
        if (!$request->id) {
            $err = $codeTable->where('code_id', 'err4')->first() ? $codeTable->where('code_id', 'err2')->first()->nm_zh_tw : '';
            array_push($errors, $err);
            return ['success' => 0, 'errors' => $errors];
        };
        $reader = IOFactory::createReader('Xlsx');
        $reader->setReadDataOnly(TRUE);
        $spreadsheet = $reader->load($file); //載入excel表格

        $worksheet = $spreadsheet->getActiveSheet();
        $highestRow = $worksheet->getHighestDataRow(); // 总行数
        $highestColumn = $worksheet->getHighestDataColumn(); // 总列数
        $highestColumnIndex = \PhpOffice\PhpSpreadsheet\Cell\Coordinate::columnIndexFromString($highestColumn); // 幾個欄位
        try {
            $database = Database::where('id', $request->get('id'))->first();
            $database->setAttribute('payload->columns', []);
            for ($col = 1; $col <= $highestColumnIndex; $col++) {
                $index = $col - 1;
                $columnIndex = 'payload->columns->' . $index;
                $names = $columnIndex . '->name';
                $ids = $columnIndex . '->id';
                $dataRows = $columnIndex . '->data';
                $database->setAttribute($names, $worksheet->getCellByColumnAndRow($col, 1)->getValue());
                $database->setAttribute($ids, $col);
                $rows = [];
                for ($row = 2; $row <= $highestRow; ++$row) {
                    array_push($rows, $worksheet->getCellByColumnAndRow($col, $row)->getValue());
                }
                $database->setAttribute($dataRows, $rows);
                $database->save();
            }
            return ['success' => 1, 'errors' => $errors, 'cols' => $database->payload['columns']];
        } catch (Exception $e) {
            Log::error($e);
            $err = $codeTable->where('code_id', 'err4')->first() ? $codeTable->where('code_id', 'err1')->first()->nm_zh_tw : '';
            array_push($errors, $err);
            return ['success' => 0, 'errors' => $errors];
        }
    }

    public function importListLayout(Request $request)
    {
        $errors = [];
        $file = $request->file;
        $isExcel = strtolower($file->getClientOriginalExtension());
        $codeTable = Code::where('code_kind', 'AG')->get();
        if ($isExcel != 'xlsx') {
            $err = $codeTable->where('code_id', 'err2')->first() ? $codeTable->where('code_id', 'err2')->first()->nm_zh_tw : '';
            array_push($errors, $err);
            return ['success' => 0, 'errors' => $errors];
        };
        if (!$request->id) {
            $err = $codeTable->where('code_id', 'err3')->first() ? $codeTable->where('code_id', 'err2')->first()->nm_zh_tw : '';
            array_push($errors, $err);
            return ['success' => 0, 'errors' => $errors];
        };
        $reader = IOFactory::createReader('Xlsx');
        $reader->setReadDataOnly(TRUE);
        $spreadsheet = $reader->load($file); //載入excel表格

        $worksheet = $spreadsheet->getActiveSheet();
        // $highestRow = $worksheet->getHighestDataRow(); // 总行数
        $highestColumn = $worksheet->getHighestDataColumn(); // 总列数
        $highestColumnIndex = \PhpOffice\PhpSpreadsheet\Cell\Coordinate::columnIndexFromString($highestColumn); // 幾個欄位

        try {
            $list = ListLayout::where('id', $request->get('id'))->first();
            $list->setAttribute('payload->columns', []);
            for ($col = 1; $col <= $highestColumnIndex; $col++) {
                $index = $col - 1;
                $columnIndex = 'payload->columns->' . $index;
                //從A2開始
                $A2 = $col + 1;
                $names = $worksheet->getCellByColumnAndRow(1, $A2)->getValue();
                $count = $worksheet->getCellByColumnAndRow(2, $A2)->getValue();
                $unit = $worksheet->getCellByColumnAndRow(3, $A2)->getValue();
                $price = $worksheet->getCellByColumnAndRow(4, $A2)->getValue();
                $total = $worksheet->getCellByColumnAndRow(5, $A2)->getValue();
                $memo = $worksheet->getCellByColumnAndRow(6, $A2)->getValue();
                if (!is_null($names)) {
                    $list->setAttribute($columnIndex . '->name', $names);
                }
                if (!is_null($count)) {
                    $list->setAttribute($columnIndex . '->count', $count);
                }
                if (!is_null($unit)) {
                    $list->setAttribute($columnIndex . '->unit', $unit);
                }
                if (!is_null($price)) {
                    $list->setAttribute($columnIndex . '->price', $price);
                }
                if (!is_null($total)) {
                    $list->setAttribute($columnIndex . '->total', $total);
                }
                if (!is_null($memo)) {
                    $list->setAttribute($columnIndex . '->memo', $memo);
                }
                $list->save();
            }
            return ['success' => 1, 'errors' => $errors, 'menu_form' => $list->payload['columns']];
        } catch (Exception $e) {
            Log::error($e);
            $err = $codeTable->where('code_id', 'err4')->first() ? $codeTable->where('code_id', 'err1')->first()->nm_zh_tw : '';
            array_push($errors, $err);
            return ['success' => 0, 'errors' => $errors];
        }
    }

    public function export(Request $request)
    {
        $spreadsheet = new Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();

        $docName = $request->docName;
        $columnp_names = $request->names;
        $namesCount = count($columnp_names);
        $mata = $request->mata;

        $columnp_pioritys = [];
        for ($x = ord('A'); $x <= ord('Z'); $x++) {
            array_push($columnp_pioritys, chr($x));
        }
        $columnp_pioritys = array_splice($columnp_pioritys, 0, $namesCount);

        for ($i = 0; $i < $namesCount; $i++) {
            $sheet->getCell($columnp_pioritys[$i] . '1')->getStyle()->getFont()->setBold(true);
            $sheet->setCellValue($columnp_pioritys[$i] . '1', $columnp_names[$i]);
        };

        //先Y軸 從A2開始
        $baseRow = 2;
        foreach ($mata as $key => $items) {
            //Excel沒有第0欄，以下格式會報錯，故先移除
            // $i = 0;
            // $sheet->getStyle('A' . $i)->getNumberFormat()
            //     ->setFormatCode(\PhpOffice\PhpSpreadsheet\Style\NumberFormat::FORMAT_TEXT);
            foreach ($items as $index => $itemRow) {
                $sheetKey = $index + $baseRow;
                $sheet->setCellValue($columnp_pioritys[$key] . $sheetKey,  $itemRow);
            }
        }
        $getCodeTable = Code::where('code_kind', 'AE')->where('code_id', 'export')->first();
        $exportName = $getCodeTable ? $getCodeTable->nm_zh_tw : '';
        //html回傳excel需寫標頭,訂定格式為UTF-8
        $encoded_fname = rawurlencode(date('Y-m-d') . $docName . time() . $exportName . '.xlsx');
        header('Content-Type:application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        header('Content-Disposition: attachment; filename="' . $encoded_fname . '"; filename*=utf-8\'\'' . $encoded_fname);
        header('Pragma:no-cache');
        header('Expires:0');

        $writer = new Xlsx($spreadsheet);
        //  $writer = IOFactory::createWriter($spreadsheet, "Xls");
        $writer->save('php://output');
        exit;
    }
}
