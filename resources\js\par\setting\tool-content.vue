<template>
    <div>
        <div class="text-center relative mb-10">
            <span
                @click="($parent.action = 0), ($parent.id = 0)"
                class="cursor-pointer absolute left-0 top-2"
            >
                <i class="fas fa-arrow-left"></i>
            </span>
            <p class="text-2xl font-semibold">
                {{ this.isAdd ? "新增設備" : "編輯設備" }}
            </p>
        </div>
        <div class="demandList">
            <div class="pb-8 border-b border-gray-200">
                <div class="block md:flex">
                    <p class="my-auto mr-8 whitespace-nowrap">
                        設備圖片<span class="text-red-500">*</span>
                    </p>
                    <div>
                        <div class="flex flex-wrap mb-2">
                            <div
                                v-for="(image, index) of form.images"
                                :key="index"
                                class="text-right"
                            >
                                <img
                                    class="w-52 h-40 mr-4"
                                    :class="
                                        index == form.images.length - 1
                                            ? 'mr-0'
                                            : ''
                                    "
                                    :src="form.images[index].file_show"
                                    width="200"
                                />
                                <i
                                    @click="form.images.splice(index, 1)"
                                    class="fa-solid fa-trash-can mr-4 cursor-pointer"
                                ></i>
                            </div>
                            <div v-if="form.images.length < 5">
                                <div
                                    class="border-2 rounded-md w-52 h-40 mr-4 text-center"
                                    :class="
                                        formInvalid.images
                                            ? 'border-solid border-red-300'
                                            : 'border-dashed border-gray-200'
                                    "
                                >
                                    <i
                                        style="line-height: 10rem"
                                        class="fa-solid fa-image fa-2xl text-gray-200"
                                    ></i>
                                </div>
                                <small
                                    v-if="formInvalid.images"
                                    class="text-red-500 block"
                                    >最少上傳1張圖片</small
                                >
                            </div>
                            <div
                                v-for="(n, index) in 4 - form.images.length < 0
                                    ? 0
                                    : 4 - form.images.length"
                                :key="'n' + index"
                                class="border-2 border-dashed rounded-md border-gray-200 w-52 h-40 mr-4 text-center"
                                :class="index == 4 ? 'mr-0' : ''"
                            >
                                <i
                                    style="line-height: 10rem"
                                    class="fa-solid fa-image fa-2xl text-gray-200"
                                ></i>
                            </div>
                        </div>
                        <!-- <div class=" w-52"> -->
                        <!-- POST 打api -->
                        <!-- <FileUpload
              id="fileUp"
              :class="{ 'p-component': false }"
              name="upload[]"
              :customUpload="true"
              @uploader="uploader"
              :showUploadButton="false"
              :showCancelButton="false"
              :multiple="true"
              :auto="true"
              accept="image/*"
              :maxFileSize="1000000"
            /> -->
                        <!-- mode="basic" -->
                        <!-- </div> -->
                        <Button
                            @click="$refs.fileInput.click()"
                            label="上傳圖片"
                            class="p-button-outlined p-button-secondary w-28"
                        />
                        <input
                            id="files"
                            class="hidden"
                            type="file"
                            ref="fileInput"
                            multiple
                            accept=".jpg,.jpeg,.heif,.hevc,.png,.HEIF,.HEVC"
                            @change="uploadFile($event)"
                        />
                    </div>
                </div>
            </div>
            <div class="py-8 border-b border-gray-200">
                <div class="block md:flex">
                    <p class="my-auto mr-8">
                        設備名稱<span class="text-red-500">*</span>
                    </p>
                    <div>
                        <InputText
                            class="w-72"
                            :class="{ 'p-invalid': formInvalid.name }"
                            v-model="form.name"
                            placeholder="輸入設備名稱"
                            @input="formInvalid.name = false"
                        />
                        <small
                            v-if="formInvalid.name"
                            class="text-red-500 block"
                            >請輸入設備名稱</small
                        >
                    </div>
                </div>
            </div>
            <div class="py-8 border-b border-gray-200">
                <div class="block md:flex">
                    <p class="my-auto mr-8">
                        設備分類<span class="text-red-500">*</span>
                    </p>
                    <div>
                        <InputText
                            class="w-72"
                            :class="{ 'p-invalid': formInvalid.type }"
                            v-model="form.type"
                            placeholder="輸入設備分類"
                            @input="formInvalid.type = false"
                        />
                        <small
                            v-if="formInvalid.type"
                            class="text-red-500 block"
                            >請輸入設備分類</small
                        >
                    </div>
                </div>
            </div>
            <div class="py-8 border-b border-gray-200">
                <div class="block md:flex">
                    <p class="my-auto mr-8">
                        設備編號<span class="text-red-500">*</span>
                    </p>
                    <div>
                        <InputText
                            class="w-72"
                            :class="{ 'p-invalid': formInvalid.no }"
                            v-model="form.no"
                            placeholder="輸入設備編號"
                            @input="formInvalid.no = false"
                        />
                        <small v-if="formInvalid.no" class="text-red-500 block"
                            >請輸入設備編號</small
                        >
                    </div>
                </div>
            </div>
            <div class="py-8 border-b border-gray-200">
                <div class="block md:flex">
                    <p class="my-auto mr-8">
                        設備數量<span class="text-red-500">*</span>
                    </p>
                    <div>
                        <InputNumber
                            v-model="form.amount"
                            inputClass="w-60"
                            :class="{ 'p-invalid': formInvalid.amount }"
                            :min="0"
                            mode="decimal"
                            incrementButtonClass="bg-white text-black border-gray-300 border-l-0 border-b-0"
                            decrementButtonClass="bg-white text-black border-gray-300 border-l-0 border-t-0"
                            showButtons
                            placeholder="輸入設備數量"
                            @input="formInvalid.amount = false"
                        />
                        <small
                            v-if="formInvalid.amount"
                            class="text-red-500 block"
                            >請輸入設備數量</small
                        >
                    </div>
                </div>
            </div>
            <div class="py-8 border-b border-gray-200">
                <div class="block md:flex">
                    <p class="my-auto mr-8">
                        所屬部門<span class="text-red-500">*</span>
                    </p>
                    <div class="w-60">
                        <Dropdown
                            v-model="form.org_unit_id"
                            :class="{ 'p-invalid': formInvalid.org_unit_id }"
                            :filter="true"
                            placeholder="選擇部門"
                            :options="orgs"
                            optionLabel="name"
                            optionValue="id"
                            @input="formInvalid.org_unit_id = false"
                        />
                        <small
                            v-if="formInvalid.org_unit_id"
                            class="text-red-500 block"
                            >請選擇部門</small
                        >
                    </div>
                </div>
            </div>
            <div class="py-8 border-b border-gray-200">
                <div class="block md:flex">
                    <p class="my-auto mr-8">
                        設備管理<span class="text-red-500">*</span>
                    </p>
                    <div class="w-60">
                        <MultiSelect
                            v-model="form.notified"
                            :filter="true"
                            placeholder="選擇人員"
                            :options="employees"
                            optionLabel="jobName"
                            optionValue="id"
                            class="w-72"
                            @input="formInvalid.notified = false"
                        />
                        <small
                            v-if="formInvalid.notified"
                            class="text-red-500 block"
                            >請選擇管理人</small
                        >
                    </div>
                </div>
            </div>
            <div class="py-8">
                <div class="block md:flex">
                    <p class="my-auto mr-8">
                        設備說明<span class="text-red-500">*</span>
                    </p>
                    <div>
                        <Textarea
                            v-model="form.remark"
                            :class="{ 'p-invalid': formInvalid.remark }"
                            rows="5"
                            cols="30"
                            placeholder="輸入設備說明"
                            @input="formInvalid.remark = false"
                        />
                        <small
                            v-if="formInvalid.remark"
                            class="text-red-500 block"
                            >請輸入設備說明</small
                        >
                    </div>
                </div>
            </div>
            <div class="w-full flex justify-between md:justify-end">
                <Button
                    @click="$parent.action = 0"
                    label="取消"
                    class="p-button-outlined p-button-secondary w-28 mr-5"
                />
                <Button
                    :disabled="$root.loading"
                    v-if="isAdd"
                    @click="save"
                    label="新增"
                    class="w-28"
                />
                <Button
                    :disabled="$root.loading"
                    v-else
                    @click="save"
                    label="儲存"
                    class="w-28"
                />
            </div>
        </div>
        <Toast ref="toast" position="top-center" />
        <div v-if="$root.loading">
            <div
                class="fixed top-1/2 left-1/2 transform -translate-y-1/2 -translate-x-1/2 z-50"
            >
                <ProgressSpinner />
            </div>
        </div>
    </div>
</template>
<script>
import axios from "axios";
import Button from "primevue/button";
import Dropdown from "primevue/dropdown";
import InputText from "primevue/inputtext";
import MultiSelect from "primevue/multiselect";
import InputNumber from "primevue/inputnumber";
import Textarea from "primevue/textarea";
import Toast from "primevue/toast";
import FileUpload from "primevue/fileupload";
import ProgressSpinner from "primevue/progressspinner";

export default {
    components: {
        Button,
        Dropdown,
        InputText,
        MultiSelect,
        InputNumber,
        Textarea,
        Toast,
        FileUpload,
        ProgressSpinner,
    },
    data() {
        return {
            upload: [],
            form: {
                id: 0,
                name: "",
                type: "",
                no: "",
                amount: null,
                org_unit_id: "",
                notified: [],
                remark: "",
                images: [],
                upload: [],
            },
            formInvalid: {
                name: false,
                type: false,
                no: false,
                amount: false,
                org_unit_id: false,
                remark: false,
                images: false,
                notified: false,
            },
            url: "/api/par/apparatus",
            name: "xxx",
            orgs: null,
            employees: null,
            isAdd: true,
            id: 0,
        };
    },
    watch: {
        // "form.images": function (newValue) {
        //   if (newValue.length !== 0) {
        //     this.formInvalid.images = false;
        //   } else {
        //     this.formInvalid.images = true;
        //   }
        // },
    },
    mounted() {
        this.getOrgs();
        this.getEmployees();
        this.fetchDetail();
        this.form.notified.push(Number(sessionStorage.getItem("userId")));
    },
    methods: {
        fetchDetail() {
            if (this.$parent.id == 0) return;
            this.id = this.$parent.id;

            axios
                .get(this.url + "/detail", {
                    params: {
                        id: this.id,
                    },
                })
                .then((response) => {
                    this.isAdd = false;
                    this.form = response.data;
                    this.form.id = this.id;
                })
                .catch((error) => {
                    console.error(error);
                });
        },
        validate() {
            let error = false;
            this.formInvalid.name = this.form.name === "";
            this.formInvalid.type = this.form.type === "";
            this.formInvalid.no = this.form.no === "";
            this.formInvalid.org_unit_id = this.form.org_unit_id === "";
            this.formInvalid.remark = this.form.remark === "";
            this.formInvalid.amount = this.form.amount == null;
            this.formInvalid.images = this.form.images.length == 0;
            this.formInvalid.notified = this.form.notified.length == 0;
            Object.values(this.formInvalid).forEach((item) => {
                if (item == true) {
                    error = true;
                }
            });

            return error;
        },
        imageLoaded(e) {
            this.form.upload.push(e.target.result);
        },
        uploadFile(event) {
            //   const file = this.$refs.fileInput.files[0];
            //   const reader = new FileReader();
            //   reader.addEventListener("load", this.imageLoaded);
            //   reader.readAsDataURL(file);
            //   //
            //   const form = new FormData();
            //   form.append("file", file);
            //   form.append("fileName", file.name);

            let files = document.getElementById("files").files;

            let formData = new FormData();
            for (var i = 0; i < files.length; i++) {
                let file = files[i];
                formData.append("upload[" + i + "]", file);
            }
            const options = {
                method: "POST",
                headers: { "content-type": "multipart/form-data" },
                data: formData,
                url: "/api/par/image?type=apparatus",
            };
            axios(options)
                .then((response) => {
                    //   if (response !== "") {
                    //       this.$refs.toast.add({
                    //         severity: "success",
                    //         summary: "上傳成功",
                    //         life: 3000,
                    //       });
                    //   } else {
                    //     this.$refs.toast.add({
                    //       severity: "error",
                    //       summary: "上傳失敗",
                    //       life: 3000,
                    //     });
                    //   }
                    let list = response.data;
                    list.forEach((x) => {
                        this.form.images.push(x);
                    });
                })
                .catch((error) => {
                    console.error(error);
                    this.$refs.toast.add({
                        severity: "error",
                        summary: "上傳失敗!",
                        life: 3000,
                    });
                });
        },
        save() {
            if (this.validate() == true) {
                return;
            }
            const options = {
                method: "POST",
                // headers: { "content-type": "multipart/form-data" },
                // data: formData,
                data: this.form,
                url: this.isAdd ? this.url : this.url + "/update",
            };
            this.$root.loading = true;
            axios(options)
                .then((response) => {
                    if (response.data.state) {
                        this.$refs.toast.add({
                            severity: "success",
                            summary: "成功",
                            life: 3000,
                        });
                        this.$parent.id = 0;
                        this.$parent.action = 0;
                    } else
                        this.$refs.toast.add({
                            severity: "error",
                            summary: response.data.error,
                            life: 3000,
                        });
                    //   this.$alert(response.data.msg);
                })
                .catch((error) => {
                    console.error(error);
                })
                .finally(() => {
                    this.$root.loading = false;
                });
        },

        getOrgs() {
            axios
                .get("/api/par/org-units", {})
                .then((response) => {
                    this.orgs = response.data;
                })
                .catch((error) => {
                    console.error(error);
                });
        },
        getEmployees() {
            axios
                .get("/api/demand/employees")
                .then((response) => {
                    this.employees = response.data;
                    this.employees.forEach((item) => {
                        this.$set(
                            item,
                            "jobName",
                            item.org_name + " " + item.name
                        );
                    });
                })
                .catch((error) => {
                    console.error(error);
                });
        },
    },
};
</script>
