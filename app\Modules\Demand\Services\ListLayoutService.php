<?php

namespace App\Modules\Demand\Services;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Session;
use App\Modules\Demand\Models\ListLayout;

class ListLayoutService
{

    protected $company_id;
    protected $user_id;

    public function __construct()
    {
        $this->company_id = Session::get('CompanyId');
        $this->user_id = Session::get('employee_id');
    }


    public function createList(Request $request)
    {
        if ($request->get('demand_layout_id')) {
            $ListModel = ListLayout::create(
                [
                    'demand_layout_id' => $request->get('demand_layout_id'),
                    'payload' => (object)[
                        'columns' => $request->get('columns'),
                        'company_id' => $this->company_id,
                        'column_id' => $request->get('column_id'),
                    ],
                    'created_by' => $this->user_id,
                    'updated_by' => $this->user_id
                ]
            );
            return $ListModel;
        }
        return 0;
    }

    public function updateList(Request $request)
    {
        $listRows = $request->get('list_rows');
        if ($listRows) {

            foreach ($listRows as $row) {
                $list = ListLayout::where('demand_layout_id', $request->get('layout_id'))
                    ->where('payload->column_id', $row['column_id'])->first();
                if ($list) {
                    $list->setAttribute('updated_by', $this->user_id)
                        ->setAttribute('payload->columns', json_decode($row['menu_form']));
                    $list->save();
                } else {
                    $request->merge([
                        'demand_layout_id' => $request->get('layout_id'),
                        'columns' => json_decode($row['menu_form']),
                        'column_id' => $row['column_id'],
                    ]);
                    $this->createList($request);
                }
            }
            return 1;
        }
        return 1;
    }
}
