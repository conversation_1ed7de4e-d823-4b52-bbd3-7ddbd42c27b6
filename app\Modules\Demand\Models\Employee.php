<?php

namespace App\Modules\Demand\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Employee extends Model
{
    use SoftDeletes;
    use \Staudenmeir\EloquentJsonRelations\HasJsonRelationships;
    protected $fillable = ['id', 'company_id', 'payload', 'metadata','created_by'];
    protected $casts = ['payload' => 'collection', 'metadata' => 'collection'];

    public function orgs()
    {
        return $this->hasManyThrough(OrgUnit::class,OrgUnitMember::class,'employee_id','id','id','org_unit_id');
    }
    public function represents()
    {
        return $this->hasMany(Employee::class,'payload->agent')->withTrashed()->where('metadata->has_agent', '=', 'true');
    }
}
