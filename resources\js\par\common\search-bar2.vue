<template>
    <div>
        <div class="flex flex-wrap w-full pb-4">
            <div class="pr-6">
                <label class="text-sm text-gray-400">{{
                    $root.reserve == 0 ? "設備" : "設施"
                }}</label>
                <br />
                <Dropdown
                    class="lg:w-44 xl:w-48 rounded-md shadow"
                    optionLabel="name"
                    optionValue="id"
                    data-key="id"
                    placeholder="選擇設施"
                    :filter="true"
                    :options="dropdown_options"
                    @input="sendEmit('updateSelectedId', $event)"
                    :value="selected_id"
                />
            </div>
            <div class="pr-6">
                <label class="text-sm text-gray-400">起始日期</label>
                <br />
                <Calendar
                    class="lg:w-44 xl:w-48 rounded-md shadow"
                    placeholder="選擇起始日期"
                    dateFormat="yy-mm-dd"
                    :manualInput="false"
                    :minDate="todayMidnight"
                    :disabledDays="[0, 6]"
                    @input="
                        sendEmit('updateSelectedBeginDate', $event);
                        sendEmit('updateSelectedEndDate', null);
                    "
                    :value="selected_begin_date"
                />
            </div>
            <div class="pr-6">
                <label class="text-sm text-gray-400">結束日期</label>
                <br />
                <Calendar
                    class="lg:w-44 xl:w-48 rounded-md shadow"
                    placeholder="選擇結束日期"
                    dateFormat="yy-mm-dd"
                    :manualInput="false"
                    :minDate="selected_begin_date ?? todayMidnight"
                    :maxDate="maxEndDate"
                    :disabledDays="[0, 6]"
                    @input="sendEmit('updateSelectedEndDate', $event)"
                    :value="selected_end_date"
                />
            </div>
            <div>
                <label></label>
                <br />
                <Button
                    @click="$emit('search2')"
                    label="查詢"
                    class="w-28"
                    :disabled="if_search"
                />
            </div>
        </div>
    </div>
</template>
<script>
import Button from "primevue/button";
import Dropdown from "primevue/dropdown";
import Calendar from "primevue/calendar";
export default {
    components: {
        Button,
        Dropdown,
        Calendar,
    },
    mounted() {
        this.fetchList();
    },
    props: [
        "selected_id",
        "selected_begin_date",
        "selected_end_date",
        "if_search",
        "todayMidnight",
    ],
    data() {
        return {
            dropdown_options: [],
        };
    },

    computed: {
        maxEndDate() {
            // 最大的結束日期不能超過起始日期+9天 (要扣掉假日)
            if (this.selected_begin_date) {
                let startDate = new Date(this.selected_begin_date);
                startDate.setHours(0, 0, 0, 0);
                startDate.setDate(startDate.getDate() + 8);

                return startDate;
            }
            return this.selected_end_date;
        },
    },
    methods: {
        fetchList() {
            axios
                .get("/api/par/reserve/pa/dropdown", {
                    params: {
                        type:
                            this.$root.reserve == 0 ? "apparatus" : "postulate",
                    },
                })
                .then((response) => {
                    this.dropdown_options = response.data;
                })
                .catch((error) => {
                    console.error(error);
                });
        },
        invalidSearch() {
            if (
                this.selected_id != null &&
                this.selected_begin_date != null &&
                this.selected_end_date != null
            ) {
                this.$emit("disableButton", false);
            }
        },
        async sendEmit(emitName, $event) {
            await this.$emit(emitName, $event);
            this.invalidSearch();
        },
        // 將該日期的時間重置
        resetDateTime(date) {
            date.setHours(0);
            date.setMinutes(0);
            date.setSeconds(0);
            date.setMilliseconds(0);
        },
    },
};
</script>
