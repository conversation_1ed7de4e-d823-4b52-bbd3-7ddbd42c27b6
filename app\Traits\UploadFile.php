<?php

namespace App\Traits;

use Carbon\Carbon;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Storage;

trait UploadFile
{
    // protected $company_id;
    // protected $user_id;
    protected $timezone;

    public function __construct()
    {
        // $this->company_id = Session::get('CompanyId');
        // $this->user_id = Session::get('employee_id');
        $this->timezone = Session::get('timezone');
    }

    /**
     *  將檔案上傳到目標，且檔名hash
     * @param object $file request->file()
     * @param string $direct 目標位址
     *
     * @return mixed
     */
    public function uploadTrait($file, $direct)
    {
        $fileName = $this->hashNameTrait($file->getClientOriginalName());

        Storage::putFileAs(
            $direct,
            $file,
            $fileName
        );
        // dd(storage_path($path));

        return $fileName;
    }

    /**
     *  檔名hash
     * @param string $fileName $file->getClientOriginalName()
     *
     * @return string
     */
    public function hashNameTrait($fileName)
    {
        $hashName = time() . '_' . md5($fileName . \Str::random(3));
        // 副檔名
        $subName = pathinfo($fileName, PATHINFO_EXTENSION);

        return $hashName . "." . $subName;
    }

}
