<?php

namespace App\Traits;

use Carbon\Carbon;


trait FormatDate
{
    public function dateToISO($date)
    {
        return Carbon::parse($date)->toISOString();
    }

    public function formatDate($date, $type)
    {
        $date = Carbon::parse($date)->setTimezone($this->timezone);

        switch ($type) {
            case 'date_time':
                $date = $date->format('Y/m/d H:i');
                break;
            case 'date':
                $date = $date->format('Y/m/d');
                break;
            case 'time':
                $date = $date->format('H:i');
                break;
            default:
                $date = $date->format('Y/m/d');
                break;
        }
        return $date;
    }
    // public function formatTime($date)
    // {
    //     return Carbon::parse($date)->setTimezone($this->timezone)->format('H:i');
    // }

    // public function formatDate($date)
    // {
    //     return Carbon::parse($date)->setTimezone($this->timezone)->format('Y/m/d');
    // }

    public function formatDateWeek($date, $type, $codes)
    {
        $date = Carbon::parse($date)->setTimezone($this->timezone);

        $day = $date->format('Y/m/d');
        $w = 'W' . Carbon::parse($date)->dayOfWeek;
        $codes = $codes->where('code_id', $w)->first()->nm_zh_tw;

        if ($type == 'date_time') {
            $time = $date->format('H:i');
            $result = $day . "(" .  $codes . ")" . $time;
        } else
            $result = sprintf($day . "(%s)", $codes);

        return $result;
    }

    // public function formatDateTimeWeek($date, $codes)
    // {
    //     // 07/22(四)17:00
    //     $date = Carbon::parse($date)->setTimezone($this->timezone);
    //     $day = $date->format('Y/m/d');
    //     $w = 'W' . Carbon::parse($date)->dayOfWeek;
    //     $codes = $codes->where('code_id', $w)->first()->nm_zh_tw;

    //     $time = $date->format('H:i');
    //     $result = $day . "(" .  $codes . ")" . $time;
    //     return $result;
    // }
}
