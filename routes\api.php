<?php

use App\Modules\explorer\Controllers\AuthController;
use App\Modules\explorer\Controllers\ExplorerController;
use App\Modules\explorer\Controllers\OcrFileRuelController;
use App\Modules\par\Controllers\CommonController;
use App\Modules\par\Controllers\PaController;
use App\Modules\par\Controllers\ReserveController;
use App\Modules\par\Controllers\ReserveSettingController;
use Illuminate\Http\Request;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/

// Route::middleware('auth:api')->get('/user', function (Request $request) {
//     return $request->user();
// });


Route::prefix('sso')->namespace('\App\Modules\SSO\Controllers')->group(function () {
    Route::post('/acc_submit', 'AuthController@acc_submit');
    Route::post('/acc_cancel_pay', 'AuthController@acc_cancel_pay');
});


Route::prefix('demand')->middleware('check.login')->namespace('\App\Modules\Demand\Controllers')->group(function () {
    //需求單設定
    Route::prefix('/setting')->group(function () {
        //需求單 群組設定 畫面
        Route::get('/groups', 'DemandGroupController@fetchGroupSetting');
        Route::post('/groups', 'DemandGroupController@createGroup');
        Route::put('/groups', 'DemandGroupController@updateGroup');
        //刪除整包類別需求單
        Route::delete('/groups/{id}', 'DemandGroupController@deleteGroups');
        //需求單功能開關
        Route::put('/groups/change/layout/switch', 'DemandLayoutController@changeLayoutSwitch');
        //刪除某一需求單
        Route::delete('/groups/layout/{id}', 'DemandLayoutController@deleteLayout');

        //申請單 自訂欄位 畫面
        Route::get('/layouts', 'DemandLayoutController@fetchLayoutSetting');
        Route::post('/layouts', 'DemandLayoutController@createLayout');
        Route::put('/layouts', 'DemandLayoutController@updateLayout');
        Route::get('/layouts/code', 'DemandLayoutController@validateSettingCode');
        //自訂欄位類型下拉
        Route::get('/layouts/type/cas-select', 'DemandLayoutController@fetchColumnTypeCascadeSelect');
        // 需求單流程設定撈取該單欄位資料
        Route::get('/layouts/fetch-list', 'DemandLayoutController@fetchColumnData');

        //項目清單
        Route::put('/layouts/list', 'ListLayoutController@updateList');
        //匯出匯入
        Route::get('/layouts/list/export', 'ExcelController@exportListLayoutToPage');
        Route::post('/layouts/list/import', 'ExcelController@importListLayout');

        //帶入其他需求單功能
        Route::put('/layouts/import', 'ImportDemandController@CreateOrUpdate');
        Route::get('/layouts/import', 'ImportDemandController@fetch');



        //資料庫設定
        Route::get('/databases', 'DatabaseController@fetchDbSetting');
        Route::post('/databases', 'DatabaseController@add');
        Route::put('/databases', 'DatabaseController@update');
        Route::delete('/databases/{id}', 'DatabaseController@delete');
        //匯出匯入
        Route::get('/databases/export', 'ExcelController@exportDatabaseToPage');
        Route::post('/databases/import', 'ExcelController@importDatabase');
        //權限設定
        Route::get('/auth/data', 'AuthController@fetchDataAuths');
        Route::post('/auth/data', 'AuthController@addOrUpdateDataAuth');
        Route::get('/auth/func', 'AuthController@fetchFuncAuths');
        Route::post('/auth/func', 'AuthController@updateFuncAuth');
        //通知設定
        Route::get('/signature-notification', 'NotifySettingsController@fetchNotification');
        Route::put('/signature-notification', 'NotifySettingsController@updateNotification');
    });

    Route::prefix('/notify')->group(function () {
        //通知
        Route::get('/', 'NotificationController@fetchNotify');
        Route::put('/read-at', 'NotificationController@updateReadAtNotify');
        Route::delete('/', 'NotificationController@deleteNotifies');
    });

    //常用
    Route::get('/employees', 'CommonController@fetchEmployees');
    //主管職等下拉
    Route::get('/manager/rank/dropdown', 'DemandLayoutController@fetchManagerRankDropdown');

    //user資訊 個人設定
    Route::get('/user/data', 'UserController@fetchEmployeeData');
    //user待簽核開關
    Route::put('/user/agent/switch', 'UserController@changeUserAgentSwitch');
    //user設定代理人
    Route::put('/agent', 'CommonController@settingEmployeeAgent');
    //upload or download file
    Route::post('/upload/temp', 'UploadController@uploadTemp');
    Route::post('/upload/templete', 'UploadController@uploadTemplete');
    Route::post('/delete/temp', 'UploadController@deleteTemp');
    Route::get('/get/file/{no}/{file}/{name}', 'UploadController@downloadFile');
    //匯出需求單
    Route::get('word', 'PdfController@export');
    //需求單申請
    Route::prefix('/submit')->middleware('check.login')->group(function () {
        //撈設定
        Route::get('/groups', 'SubmitController@fetchGroupSetting');
        Route::get('/layout', 'DemandLayoutController@fetchLayoutSetting');

        // 撈資料庫欄位資料
        Route::get('/layout/db', 'DemandLayoutController@fetchDBColumnData');

        // 需求單快取
        Route::get('/cache/{layout_id}', 'DemandLayoutController@getDemandCache');
        Route::put('/cache/{layout_id}', 'DemandLayoutController@putDemandCache');
        Route::delete('/cache/{layout_id}', 'DemandLayoutController@delDemandCache');

        //提交 => 進度 => 紀錄
        Route::post('/', 'SubmitController@submit');
        Route::get('/lists', 'SubmitController@fetchLists');
        Route::get('/history', 'SubmitController@fetchHistory');
        // 取得申請時的layout
        Route::get('/original-layout', 'DemandLayoutController@fetchOriginalLayout');
        // 再次申請
        Route::post('/again', 'SubmitController@againSubmit');
        //取消申請
        Route::delete('/lists/{id}', 'SubmitController@delete');
        //該申請單簽核關卡們
        Route::get('/lists/sign/roles/dropdown', 'SubmitController@fetchSignRolesDropdown');
        //再審BtN
        Route::post('/lists/review/again', 'SubmitController@addNodeToSignRols');
        //結案Btn
        Route::post('/lists/check/close', 'SubmitController@closeCase');
        //申請單類型下拉
        Route::get('/demand/type/dropdown', 'SubmitController@fetchDemandTypeDropdown');
        //需求單查詢類型下拉
        Route::get('/demand/query/type/dropdown', 'SubmitController@fetchDemandQueryTypeDropdown');
        //需求單類型下拉
        Route::get('/import/dropdown', 'ImportDemandController@fetchDropdown');
        //指定需求單欄位帶入
        Route::get('/import/value', 'ImportDemandController@fetchValue');
    });

    Route::prefix('reviewed')->group(function () {
        //待審核
        Route::get('/lists', 'SigningController@fetchSignOffListsToPage');
        // 待審核 明細
        Route::get('/lists/detail', 'SigningController@SignOffDetail');
        //提交
        Route::post('/lists', 'SigningController@submit');
        //全部同意
        Route::post('/lists/all-agree', 'SigningController@allAgree');
        //退回
        Route::post('/lists/back', 'SigningController@fallback');
        //送出指定會簽
        Route::post('/lists/countersigned', 'SigningController@countersigned');
        //審核紀錄
        Route::get('/history', 'SigningController@fetchSignHistoryLists');
        //匯出
        Route::get('/history/export', 'ExcelController@exportSignHistory');
    });
});

// Route::post('par/postulate', 'PaController@createPostulate');

Route::prefix('par')->middleware('check.login')->namespace('\App\Modules\par\Controllers')->group(function () {
    // 常用
    // 部門下拉
    Route::get('/org-units', [CommonController::class, 'fetchOrgUnits']);
    // 預約時段
    Route::get('/reserve-time', [CommonController::class, 'fetchReserveTimePeriod']);
    Route::get('/apparatus-type', [CommonController::class, 'fetchApparatusesType']);

    //設施
    Route::get('/postulate', 'PaController@fetchPostulate');
    Route::get('/postulate/detail', 'PaController@showPostulate');
    Route::post('/postulate', 'PaController@createPostulate');
    Route::post('/postulate/update', 'PaController@updatePostulate');
    // Route::put('/postulate/reserve/{id}', 'PaController@releasePostulate');
    Route::post('/postulate/delete', 'PaController@deletePostulate');

    //設備
    Route::get('/apparatus', 'PaController@fetchApparatus');
    Route::get('/apparatus/detail', 'PaController@showApparatus');
    Route::post('/apparatus', 'PaController@createApparatus');
    Route::post('/apparatus/update', 'PaController@updateApparatus');
    // Route::put('/apparatus/reserve/{id}', 'PaController@releaseApparatus');
    Route::post('/apparatus/delete', 'PaController@deleteApparatus');
    Route::put('/switch', [PaController::class, 'close']);
    //領取人
    Route::post('/apparatus/recipients', 'PaController@createRecipients');
    Route::delete('/apparatus/recipients', 'PaController@deleteRecipients');
    // 上傳圖片
    Route::post('/image', 'PaController@updateImage');


    // 預約
    Route::prefix('reserve')->group(function () {
        // Route::get('/test', 'ReserveController@reservenoti');
        // 下拉
        Route::get('/pa/dropdown', 'ReserveController@paDropdown');
        Route::post('/apparatus/dropdown', 'ReserveController@apparatusDropdown');
        // 列表
        Route::get('/postulate', 'ReserveController@fetchPostulate');
        Route::get('/postulate/date', 'ReserveController@fetchPostulateByDate');
        Route::get('/postulate/change', 'ReserveController@fetchChangePostulate');
        Route::get('/apparatus', 'ReserveController@fetchApparatus');
        Route::get('/own', 'ReserveController@personReserve');
        // 預約
        Route::post('/apparatus', 'ReserveController@reserveApparatus');
        Route::post('/postulate', 'ReserveController@reservePostulate');
        Route::get('/edit', [ReserveController::class, 'fetchDetail']);
        Route::post('/again', [ReserveController::class, 'reserveAgain']);
        // 取消/釋出預約
        Route::post('/cancel', 'ReserveController@cancelReserve');
    });

    //權限設定
    Route::get('/auth/func', [ReserveSettingController::class, 'list']);
    Route::post('/auth/func', [ReserveSettingController::class, 'updateAndCreate']);
});


// 接收文件系統的response
Route::prefix('explorer')->group(function () {
    // ocr
    Route::prefix('/notify/response/ocr')->group(function () {
        // service-file api 回傳結果
        Route::post('/', [\App\Modules\Demand\Controllers\NotificationController::class, 'uploadResponse'])->name('upload.response');
        // service-file api unauthorized
        Route::post('/unauthorized', [\App\Modules\Demand\Controllers\NotificationController::class, 'uploadResponseUnauthorized'])->name('upload.response.unauthorized');
    });

    // general upload resopnse
});

Route::prefix('explorer')->middleware('check.login')->namespace('\App\Modules\explorer\Controllers')->group(function () {

    Route::get('/test-file', [ExplorerController::class, 'testFile']);
    /* explorer* */
    //  獲取根目錄資訊及底下資料夾
    Route::get('/root', [ExplorerController::class, 'fetchRoot']);
    // 獲取目標路徑的資料夾和檔案資料
    Route::get('/current/{folderId}', [ExplorerController::class, 'fetchCurrentDir']);
    // 獲取目錄
    Route::get('/dir', [ExplorerController::class, 'fetchAllDir']);
    // 獲取檔案
    Route::get('/file-info', [ExplorerController::class, 'getFileInfo']);
    // 新增資料夾
    Route::post('/folder', [ExplorerController::class, 'createDir']);
    // 刪除
    Route::post('/delete', [ExplorerController::class, 'delete']);
    // Route::delete('/{id}', [ExplorerController::class, 'delete'])->where('id', '[0-9]+');
    Route::put('/rename/{id}', [ExplorerController::class, 'rename']);
    Route::put('/copy', [ExplorerController::class, 'copy']);
    Route::put('/move', [ExplorerController::class, 'move']);
    // Route::put('/copy/{id}', [ExplorerController::class, 'copy'])->where('id', '[0-9]+');
    // Route::put('/move/{id}', [ExplorerController::class, 'move'])->where('id', '[0-9]+');
    Route::post('/upload', [ExplorerController::class, 'uploadFile']);
    Route::get('/search', [ExplorerController::class, 'search']);
    Route::get('/version/{fileId}', [ExplorerController::class, 'getfileVersionList']);


    /* 規則* */
    // 獲取所有建檔規則
    Route::get('/rules', [OcrFileRuelController::class, 'fetchRules']);
    // 新增建檔規則
    // Route::post('/rules', [OcrFileRuelController::class, 'createRule']);
    // 更新建檔規則
    Route::put('/rules', [OcrFileRuelController::class, 'updateRule']);
    // 套用新規則
    // {id}= rules->id
    Route::put('/rule/{id}', [OcrFileRuelController::class, 'changeRule']);
    Route::delete('/rule/{id}', [OcrFileRuelController::class, 'deleteRule']);


    /* 測試用* */
    Route::get('/ocr', [ExplorerController::class, 'ocr']);


    Route::prefix('/notify')->group(function () {
        //建立上傳狀態通知
        Route::post('/upload', [\App\Modules\Demand\Controllers\NotificationController::class, 'createUploadNotify']);
    });

    /* 權限* */
    Route::prefix('/permission')->group(function () {
        // 權限設定
        Route::get('/function', [AuthController::class, 'fetchAuthSettingList']);
        Route::put('/function/{id}', [AuthController::class, 'updateAndCreate']);

        // 資料夾權限
        Route::get('/folder', [AuthController::class, 'fetchAllDirAuth']);
        Route::get('/folder/{folderId}', [AuthController::class, 'getDirAuth']);
        // 設定資料夾權限
        Route::put('/folder/{folderId}', [AuthController::class, 'setDirAuth']);
        Route::put('/folder/remove/{folderId}', [AuthController::class, 'cleanDirAuth']);
        // 繼承上層
        Route::post('/folder/inherit/{folderId}', [AuthController::class, 'extendAuth']);
    });
});
