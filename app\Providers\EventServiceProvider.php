<?php

namespace App\Providers;

use App\Events\DemandUpdated;
use App\Listeners\ResponseDemand;
use Illuminate\Support\Facades\Event;
use Illuminate\Auth\Events\Registered;
use Illuminate\Auth\Listeners\SendEmailVerificationNotification;
use Illuminate\Foundation\Support\Providers\EventServiceProvider as ServiceProvider;

class EventServiceProvider extends ServiceProvider
{
    /**
     * The event listener mappings for the application.
     *
     * @var array
     */
    protected $listen = [
        Registered::class => [
            SendEmailVerificationNotification::class,
        ],
        'App\Modules\par\Events\notificationEvent' => [
            'App\Modules\par\Listeners\SendEmailNotification',
        ],
        DemandUpdated::class=>[
            ResponseDemand::class
        ]
    ];

    /**
     * Register any events for your application.
     *
     * @return void
     */
    public function boot()
    {
        parent::boot();

        //
    }
}
