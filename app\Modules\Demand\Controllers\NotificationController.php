<?php

namespace App\Modules\Demand\Controllers;


use App\Modules\Demand\Services\UploadNotifyService;
use App\Traits\FormatDate;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Session;
use App\Http\Controllers\Controller;
use App\Modules\Demand\Models\Notification;
use App\Modules\Demand\Models\Code;
use Exception;
use Illuminate\Support\Facades\Log;

class NotificationController extends Controller
{
    use FormatDate;
    protected $company_id, $user_id, $timezone, $uploadNotifyService;

    public function __construct(UploadNotifyService $uploadNotifyService = null)
    {
        $this->company_id = Session::get('CompanyId');
        $this->user_id = Session::get('employee_id');
        $this->uploadNotifyService = $uploadNotifyService;
        $this->timezone = Session::get('timezone');
    }

    //根據時間排序
    public function fetchNotify()
    {
        $codeTable = Code::where('code_kind', 'AH')->get();
        $status = [
            1 => $codeTable->where('code_id', 'notify1')->first() ? $codeTable->where('code_id', 'notify1')->first()->nm_zh_tw : '',
            2 => $codeTable->where('code_id', 'notify2')->first() ? $codeTable->where('code_id', 'notify2')->first()->nm_zh_tw : '',
            3 => $codeTable->where('code_id', 'notify3')->first() ? $codeTable->where('code_id', 'notify3')->first()->nm_zh_tw : '',
            4 => $codeTable->where('code_id', 'notify4')->first() ? $codeTable->where('code_id', 'notify4')->first()->nm_zh_tw : ''
        ];

        $urls = [
            //前往審核
            1 => 'signing',
            //完成
            2 => 'history',
            //駁回
            3 => 'list',
            //上傳檔案
            4 => 'explorer',
        ];

        $notifies = Notification::where('employee_id', $this->user_id)
            ->get();

        $explorer = $notifies->filter(function ($item) {
            return $item->payload->get('type') == 4 && $item->payload->get('is_finished') == 1;
        });

        $general = $notifies->diff($explorer);

        // 一般通知
        $general->transform(function ($notify) use ($status, $urls) {
            $payload = $notify->payload;


            return [
                'id' => $notify->id,
                'content' => str_replace('name', $payload->get('name'), $status[$payload->get('type')]),
                'url' => $urls[$payload->get('type')],
                'read' => $payload->get('read'),
            ];
        });

        // 上傳檔案結果
        $explorer->transform(function ($notify) use ($status, $urls) {

            $payload = $notify->payload;
            $time = $this->formatDate($payload->get('time'), 'date_time');
            $content = str_replace('time', $time, $status[$payload->get('type')]);
            $content = str_replace('success', count($payload->get('success')), $content);
            $content = str_replace('failed', count($payload->get('failed')), $content);

            if ($payload->get('code') == 401) {
                $content = '上傳所在資料夾已存在，且您無管理權限!';
            }

            return [
                'id' => $notify->id,
                'time' => $time,
                'content' => $content,
                'code' => $payload->get('code') ?? 200,
                'url' => $urls[$payload->get('type')],
                'read' => $payload->get('read'),
                'success' => $payload->get('success'),
                'failed' => $payload->get('failed'),
            ];
        });

        return $general->concat($explorer)->sortByDesc('id')->values();
    }

    public function deleteNotifies()
    {
        $notifies = Notification::where('employee_id', $this->user_id)->get();

        return $notifies->each(function ($n) {
            $n->delete();
        });
    }

    public function updateReadAtNotify(Request $request)
    {
        if ($request->get('id')) {
            try {
                $notify = Notification::where('id', $request->get('id'))->first();
                $notify->setAttribute('payload->read', true);
                $notify->save();
                return 1;
            } catch (Exception $e) {
                Log::error($request);
                Log::error($e);
                return 0;
            }
        }
        return 0;
    }

    //只放一筆消息
    public function createNotify($id, $layoutName, $type)
    {
        if ($id) {
            $NotificationModel = Notification::create([
                'employee_id' => $id,
                'payload' => (object) [
                    'name' => $layoutName,
                    'read' => false,
                    'type' => $type
                ],
            ]);
            return 1;
        }
        return 0;
    }
    // 公設預約通知
    public static function createReserveNotify($id, $startTime, $endTime)
    {
        // if (!$id)
        //     return 0;
        // Notification::create([
        //     'employee_id' => 0,
        //     'payload' => [
        //         'method' => 'email',
        //         'start' => $startTime,
        //         'end' => $endTime,
        //         'reserve_id' => $id
        //     ],
        // ]);
        // return 1;
    }

    public function createUploadNotify(Request $request)
    {
        if (!$request->has(['key', 'fileNames']))
            return 0;

        // dd($request->all());
        Notification::create([
            'employee_id' => 0,
            'payload' => [
                'read' => false,
                'is_finished' => 0,
                'code' => 0,
                'type' => 4,
                'employee_id' => (int) $this->user_id,
                'patc_no' => $request->input('key'),
                'file_names' => $request->input('fileNames'),
                'success' => [],
                'failed' => [],
            ],
        ]);


        return 1;
    }

    /**
     * @param string $patchNo
     * @param int|boolean $status 0/1
     * @param string $fileName
     *
     */
    public function uploadResponse(Request $request)
    {
        // \Log::error('class :' . __CLASS__ . ' function :' . __FUNCTION__);
        // dd($request->all());
        if (!$request->has(['patchNo', 'status', 'fileName']))
            return response(400);

        $patchNo = $request->input('patchNo');
        $status = $request->input('status');
        $fileName = $request->input('fileName');
        try {
            $this->uploadNotifyService->updateUploadResponse($patchNo, $status, $fileName);
            return response()->json(['success' => 1]);
        } catch (Exception $e) {
            \Log::error($e);
            return response()->json(['success' => 0]);
        }

    }
    public function uploadResponseUnauthorized(Request $request)
    {
        \Log::error('class :' . __CLASS__ . ' function :' . __FUNCTION__);
        // dd($request->all());
        if (!$request->has(['patchNo']))
            return response(400);

        $patchNo = $request->input('patchNo');
        try {
            $this->uploadNotifyService->updateUploadResponseUnauthorized($patchNo);
            return 1;
        } catch (Exception $e) {
            \Log::error($e);
            return response(400);
        }

    }
}
