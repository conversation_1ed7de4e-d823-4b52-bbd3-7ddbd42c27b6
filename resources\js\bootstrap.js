import Vue from "vue";
import PrimeVue from "primevue/config";
// import ConfirmationService from "primevue/confirmationservice";
// import ToastService from "primevue/toastservice";
import "primevue/resources/primevue.min.css";
import "primeicons/primeicons.css";
import "primevue/resources/themes/tailwind-light/theme.css";
import "tailwindcss/tailwind.css";
import axios from "axios";
import lodash from 'lodash';

window.Vue = Vue;
Vue.use(PrimeVue);
// Vue.use(ConfirmationService); vite 無法使用
// Vue.use(ToastService); vite 無法使用

window._ = lodash;
window.axios = axios;
// window.axios.defaults.headers.common['X-Requested-With'] = 'XMLHttpRequest';
