<template>
  <div
    style="min-height: calc(100vh - 148px)"
    class="w-full h-full bg-white rounded-lg"
  >
    <Toast ref="toast" position="top-center" :baseZIndex="9999" />

    <EditMembers
      editType="function"
      :title="editDialogTitle"
      :isShow="isShowEditDialog"
      :selectedMemberIds="selectedMemberIds"
      @onUpdate="updatePermission"
      @toggleEditDialog="toggleEditDialog"
      @togglePermissionDialog="togglePermissionDialog"
      @updateSelectedMemberIds="updateSelectedMemberIds"
    />

    <table class="w-full h-full">
      <caption
        class="text-xl font-bold text-left text-explorerPrimary border-b border-gray-200 px-6 py-7"
      >
        {{
          tableTitle
        }}
      </caption>
      <thead
        class="text-left font-bold text-explorerPrimary border-b border-gray-200 bg-gray-50"
      >
        <tr class="flex justify-between items-center">
          <th
            v-for="header in tableHeader"
            :key="header"
            class="w-28 md:w-80 lg:w-96 last:w-5 last:mr-7 first:pl-6 py-7"
          >
            {{ header }}
          </th>
        </tr>
      </thead>
      <tbody>
        <tr
          v-for="data in tableData"
          :key="data.id"
          class="border-b border-gray-200 flex justify-between items-center"
        >
          <td
            class="w-28 md:w-80 lg:w-96 pl-6 py-7 text-left font-bold text-explorerPrimary"
          >
            {{ data.name }}
          </td>
          <td
            class="w-28 md:w-80 lg:w-96 py-7 text-left font-bold text-explorerPrimary"
          >
            <div v-if="data.members?.length !== 0">
              <span
                v-for="(member, index) of data.members.slice(0, 5)"
                :key="member.id"
              >
                <template v-if="index > 0">、</template>
                {{ member.name }}
              </span>
              <template v-if="data.members.length > 5">...</template>
            </div>
            <div v-else>
              <span>-</span>
            </div>
          </td>
          <td class="w-5 mr-7">
            <button
              class="hover:bg-gray-200 rounded-full w-6 h-6 transition p-1"
              @click="openEditDialog(data)"
            >
              <img
                src="@images/icon/edit.svg"
                alt="edit-icon"
                class="mx-auto"
              />
            </button>
          </td>
        </tr>
      </tbody>
    </table>
  </div>
</template>

<script>
import Toast from "primevue/toast";
import EditMembers from "../common/edit-members.vue";
import { permissionMixin } from "../../mixins/permissionMixin";
import {
  getFunctionPermissions,
  updateFunctionPermissions,
} from "@/axios/explorerApi.js";

export default {
  name: "FunctionPermissions",
  components: { Toast, EditMembers },
  mixins: [permissionMixin],
  data() {
    return {
      tableTitle: "功能權限",
      tableHeader: ["功能名稱", "可使用人員", ""],
      tableData: [],
      id: null,
    };
  },
  methods: {
    async fetchTableData() {
      try {
        const data = await getFunctionPermissions();
        this.id = data.id;
        this.tableData = [
          { type: "auth", name: "權限設定", members: data.auth },
          { type: "createWhite", name: "建立檔案", members: data.createWhite },
        ];
      } catch (error) {
        this.$refs.toast.add({
          severity: "error",
          summary: error.message,
          life: 3000,
        });
      }
    },
    async updatePermission(newMemberIds) {
      try {
        this.isShowEditDialog = false;
        this.computedPermissionSettingsVisible = true;
        await updateFunctionPermissions(this.id, {
          type: this.editOption,
          users: newMemberIds,
        });
        this.$refs.toast.add({
          severity: "success",
          summary: "編輯成功",
          life: 3000,
        });
        this.selectedMemberIds = [];
        this.fetchTableData();
      } catch (error) {
        this.$refs.toast.add({
          severity: "error",
          summary: error.message,
          life: 3000,
        });
      }
    },
  },
  created() {
    this.fetchTableData();
  },
};
</script>
