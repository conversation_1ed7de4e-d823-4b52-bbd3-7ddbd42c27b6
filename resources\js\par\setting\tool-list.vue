<template>
    <div ref="outBox">
        <div
            style="height: calc(100vh - 160px)"
            class="w-full bg-white rounded-xl shadow pt-6 overflow-y-auto relative"
        >
            <div class="flex px-6">
                <Button
                    @click="$parent.action = 1"
                    label="新增"
                    class="w-28 mr-4"
                />
                <div class="w-1/4 p-inputgroup">
                    <InputText
                        v-model="key"
                        class="w-72"
                        placeholder="搜尋設備"
                    />
                    <Button icon="pi pi-search" @click="fetchList(1, 10)" />
                </div>
            </div>
            <div
                ref="infoBox"
                class="overflow-auto whitespace-nowrap block mt-8"
            >
                <table class="min-w-full">
                    <thead>
                        <tr
                            class="text-left text-gray-400 border-b border-gray-200"
                        >
                            <th class="py-2 leading-3 pl-6">
                                <Checkbox
                                    v-model="checkedAll"
                                    @change="checkAll"
                                    :binary="true"
                                />
                            </th>
                            <th class="py-2">設備圖片</th>
                            <th class="py-2">設備名稱</th>
                            <th class="py-2">設備編號</th>
                            <th class="py-2">剩餘/總數</th>
                            <th class="py-2">預約狀態</th>
                            <th class="py-2">領取人</th>
                            <th class="py-2">當前預約人</th>
                            <th class="py-2">開放狀態</th>
                            <th class="py-2 pr-6">&emsp;&emsp;&emsp;</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr
                            v-for="(list, index) of lists.data"
                            :key="list.id"
                            class="text-left bg-white even:bg-gray-100"
                        >
                            <td class="py-2 leading-3 pl-6">
                                <Checkbox v-model="checks" :value="list.id" />
                            </td>
                            <td class="py-2">
                                <img width="100" :src="list.image_url" alt="" />
                            </td>
                            <td class="py-2">{{ list.name }}</td>
                            <td class="py-2">{{ list.no }}</td>
                            <td class="py-2">
                                {{ list.amount }} / {{ list.total_amount }}
                            </td>
                            <td class="py-2">
                                <div class="flex">
                                    <p
                                        class="rounded-full py-1.5 border text-sm px-2"
                                        :class="
                                            list.state == '預約中'
                                                ? 'bg-indigo-50 text-indigo-400 border-indigo-400'
                                                : 'bg-green-50 text-green-400 border-green-300'
                                        "
                                    >
                                        {{ list.state }}
                                    </p>
                                </div>
                            </td>
                            <td class="py-2">
                                <div
                                    v-for="(rep, rep_index) of list.recipients"
                                >
                                    <p
                                        v-if="rep"
                                        class="underline cursor-pointer mb-1"
                                        @click="
                                            displayRecipient = true;
                                            list_index = index;
                                            list_recipients_index = rep_index;
                                        "
                                    >
                                        {{ changeIdToName(rep["employee_id"]) }}
                                    </p>
                                </div>
                                <div
                                    class="underline cursor-pointer"
                                    @click="
                                        list_index = index;
                                        displayAddRecipient = true;
                                        form.apparatus_id = list.id;
                                    "
                                >
                                    新增
                                </div>
                                <p class="underline cursor-pointer"></p>
                            </td>

                            <td class="py-2">
                                <div
                                    v-for="(sub, sub_index) of list.subscribers"
                                    :key="sub.reserveId"
                                    class="flex flex-col"
                                >
                                    <p
                                        class="underline cursor-pointer"
                                        @click="
                                            displaySub = true;
                                            list_index = index;
                                            list_subscribers_index = sub_index;
                                        "
                                    >
                                        {{ sub.org_unit + " " + sub.name }}
                                    </p>
                                </div>
                            </td>
                            <td>
                                <InputSwitch
                                    @change="
                                        switch_delete = 1;
                                        list.is_open
                                            ? openPa(list.id, list.is_open)
                                            : (displayDelete = true);
                                        list_index = index;
                                    "
                                    v-model="list.is_open"
                                />
                            </td>
                            <td class="py-2 pr-6">
                                <div class="flex justify-between">
                                    <button>
                                        <img
                                            src="@images/icon/edit_enable.png"
                                            alt="edit"
                                            @click="edit(list.id)"
                                        />
                                    </button>
                                    <button
                                        :disabled="list.subscribers.length !== 0"
                                        :class="{ 'cursor-not-allowed': list.subscribers.length !== 0 }"
                                        @click="
                                            list.subscribers.length == 0
                                                ? (displayDelete = true)
                                                : (displayDelete = false);
                                            list_index = index;
                                            switch_delete = 0;
                                        "
                                    >
                                        <img
                                            v-if="list.subscribers.length == 0"
                                            src="@images/icon/delete_enable.png"
                                            alt="delete"
                                        />
                                        <img
                                            v-else
                                            src="@images/icon/delete_disable.png"
                                            alt="delete_disable"
                                        />
                                    </button>
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
            <div :style="white_space"></div>
            <Paginator
                class="sticky bottom-0 left-0"
                :data="lists"
                @page="fetchList"
            />
            <div class="w-full sticky bottom-0 z-40">
                <transition name="slide">
                    <div
                        v-if="checks.length !== 0"
                        class="flex p-5 justify-end bg-white sticky bottom-0 shadow"
                    >
                        <div class="flex">
                            <p class="my-auto">已選取{{ checks.length }}個</p>
                            <p
                                @click="(checks = []) && (checkedAll = false)"
                                class="my-auto mx-4 underline cursor-pointer"
                            >
                                取消選取
                            </p>
                            <Button
                                class="w-28"
                                label="全部刪除"
                                @click="multipleRemove"
                            />
                        </div>
                    </div>
                </transition>
            </div>
        </div>
        <Toast ref="toast" position="top-center" />
        <!-- 新增領取人彈窗 -->
        <Dialog
            header="新增領取人"
            :visible.sync="displayAddRecipient"
            :containerStyle="{}"
            :closable="true"
            :modal="true"
        >
            <div
                class="py-8 border-b border-gray-200"
                v-if="displayAddRecipient"
            >
                <div class="block md:flex">
                    <p class="my-auto mr-8">
                        人員<span class="text-red-500">*</span>
                    </p>
                    <div class="w-60">
                        <Dropdown
                            v-model="form.employee_id"
                            :filter="true"
                            placeholder="選擇人員"
                            :options="people"
                            optionLabel="name"
                            optionValue="employee_id"
                        />
                        <small
                            v-if="formInvalid.employee_id"
                            class="text-red-500 block"
                            >請選擇人員</small
                        >
                    </div>
                </div>
            </div>
            <div>
                <div class="py-8 border-b border-gray-200">
                    <div class="block md:flex">
                        <p class="my-auto mr-8">備註</p>
                        <div>
                            <InputText
                                class="w-72"
                                placeholder="輸入備註"
                                v-model="form.remark"
                            />
                            <small
                                class="text-red-500 block"
                                v-if="formInvalid.remark"
                                >請輸入備註</small
                            >
                        </div>
                    </div>
                </div>
            </div>
            <div class="w-full flex justify-between md:justify-end">
                <Button
                    label="取消"
                    class="p-button-outlined p-button-secondary w-28 mr-5"
                    @click="displayAddRecipient = false"
                />
                <Button label="新增" class="w-28" @click="addRecipient" />
            </div>
        </Dialog>
        <!-- 領取人彈窗 -->
        <Dialog
            header=" "
            :visible.sync="displayRecipient"
            :containerStyle="{
                width: '20vw',
            }"
            :dismissableMask="true"
            :closable="true"
            :modal="true"
        >
            <div v-if="displayRecipient">
                <p class="py-2">
                    預約人&emsp;&emsp;|&emsp;{{
                        changeIdToName(
                            lists.data[list_index].recipients[
                                list_recipients_index
                            ].employee_id
                        )
                    }}
                </p>
                <p class="py-2">
                    領取時間&emsp;|&emsp;{{
                        changeISOTime(
                            lists.data[list_index].recipients[
                                list_recipients_index
                            ].time
                        )
                    }}
                </p>
                <p class="py-2">
                    備註&emsp;&emsp;&emsp;|&emsp;{{
                        lists.data[list_index].recipients[list_recipients_index]
                            .remark
                    }}
                </p>
                <!-- 確認功能 -->
                <div
                    class="buttons mt-4 w-full flex justify-between md:justify-end card relative"
                >
                    <Button
                        class="cursor-pointer"
                        @click="confirmReturn($event)"
                        label="確認歸還"
                    />
                </div>
            </div>
            <template #footer> <div></div></template>
        </Dialog>
        <!-- 當前預約人彈窗 -->
        <Dialog
            header=" "
            :visible.sync="displaySub"
            :containerStyle="{
                width: '20vw',
            }"
            :dismissableMask="true"
            :closable="true"
            :modal="true"
        >
            <div v-if="displaySub">
                <p class="py-2">
                    預約人&emsp;&emsp;|&emsp;{{
                        lists.data[list_index].subscribers[
                            list_subscribers_index
                        ].name
                    }}
                </p>
                <p class="py-2">
                    預約日期&emsp;|&emsp;{{
                        lists.data[list_index].subscribers[
                            list_subscribers_index
                        ].date
                    }}
                </p>
                <p class="py-2">
                    預約數量&emsp;|&emsp;{{
                        lists.data[list_index].subscribers[
                            list_subscribers_index
                        ].amount
                    }}
                </p>
                <p class="py-2">
                    預約時段&emsp;|&emsp;
                    <span>{{
                        combineTime(
                            lists.data[list_index].subscribers[
                                list_subscribers_index
                            ].time
                        )
                    }}</span>
                </p>
                <p class="py-2">
                    預約事由&emsp;|&emsp;{{
                        lists.data[list_index].subscribers[
                            list_subscribers_index
                        ].reason
                    }}
                </p>
                <p class="py-2">
                    備註&emsp;&emsp;&emsp;|&emsp;{{
                        lists.data[list_index].subscribers[
                            list_subscribers_index
                        ].remark
                    }}
                </p>
                <!-- 完成預約功能 -->
                <div
                    class="buttons mt-4 w-full flex justify-between md:justify-end card relative"
                >
                    <Button
                        class="cursor-pointer"
                        @click="
                            release_reserveId =
                                lists.data[list_index].subscribers[
                                    list_subscribers_index
                                ].reserveId;
                            confirmReserve($event);
                        "
                        label="完成預約"
                    />
                    <!--     displaySubOptions = true; -->
                </div>
            </div>
            <template #footer> <div></div></template>
        </Dialog>
        <ConfirmPopup
            ref="confirmPopup"
            style="z-index: 2000; transform: translate(-80px)"
        ></ConfirmPopup>
        <!-- 提前釋出彈窗 -->
        <!-- <Dialog
            header="選擇釋出人員"
            :visible.sync="displaySubOptions"
            :containerStyle="{
                width: '20vw',
            }"
            :dismissableMask="true"
            :closable="true"
            :modal="true"
        >
            <div v-if="displaySubOptions">
                <div
                    v-for="(sub, index) in lists.data[list_index].subscribers"
                    :key="index"
                    class="py-2"
                >
                    <Checkbox
                        v-model="release_reserveId"
                        class="mb-1"
                        :value="sub.reserveId"
                    />
                    <label> {{ sub.org_unit + " " + sub.name }}</label>
                </div>
            </div>
            <template #footer>
                <div class="flex justify-end">
                    <Button
                        @click="displaySubOptions = false"
                        label="取消"
                        class="p-button-outlined p-button-secondary w-28 mr-5"
                    />
                    <Button @click="release()" label="確定" class="w-28" />
                </div>
            </template>
        </Dialog> -->
        <Dialog
            :visible.sync="displayDelete"
            :containerStyle="{
                width: '25vw',
            }"
            :dismissableMask="true"
            :closable="false"
            :modal="true"
        >
            <div class="pb-8">
                <img class="mx-auto" src="@images/icon/notify.png" />
                <br />
                <p v-if="switch_delete == 0" class="text-center">
                    是否刪除此設備?
                </p>
                <div v-else>
                    <p class="text-center">是否關閉此設備?</p>
                    <p class="text-center" style="color: red">
                        *如關閉設備，系統將自動取消後續預約
                    </p>
                </div>
            </div>
            <template #footer>
                <div class="flex justify-evenly">
                    <Button
                        @click="
                            switch_delete !== 0 &&
                            lists.data[list_index].is_open
                                ? (lists.data[list_index].is_open = false)
                                : (lists.data[list_index].is_open = true);
                            displayDelete = false;
                        "
                        label="取消"
                        class="p-button-outlined p-button-secondary w-28"
                    />
                    <Button
                        @click="
                            switch_delete == 0
                                ? remove()
                                : openPa(
                                      lists.data[list_index].id,
                                      lists.data[list_index].is_open
                                  )
                        "
                        label="確定"
                        class="w-28"
                    />
                </div>
            </template>
        </Dialog>
    </div>
</template>
<script>
import axios from "axios";
import Button from "primevue/button";
import Checkbox from "primevue/checkbox";
import InputText from "primevue/inputtext";
import Paginator from "@/demand/common/Paginator";
import Dialog from "primevue/dialog";
import Sidebar from "primevue/sidebar";
import InputSwitch from "primevue/inputswitch";
import Toast from "primevue/toast";
import ConfirmPopup from "primevue/confirmpopup";
import Dropdown from "primevue/dropdown";

export default {
    components: {
        Button,
        Checkbox,
        InputText,
        Paginator,
        Dialog,
        Sidebar,
        InputSwitch,
        Toast,
        ConfirmPopup,
        Dropdown,
    },
    data() {
        return {
            lists: [],
            checks: [],
            checkedAll: false,
            visibleBottom: false,
            key: "",
            displaySub: false,
            displaySubOptions: false,
            displayDelete: false,
            displayRecipient: false,
            displayAddRecipient: false,
            list_index: null,
            list_subscribers_index: null,
            list_recipients_index: null,
            release_reserveId: [],
            white_space: {},
            switch_delete: 0,
            form: {
                apparatus_id: null,
                employee_id: null,
                remark: "",
            },
            formInvalid: {
                apparatus_id: null,
                employee_id: null,
                remark: "",
            },
            people: [],
        };
    },
    watch: {},
    updated() {
        this.computedWhite();
    },
    mounted() {
        this.fetchList(1, 10);
        this.getEmployees();
    },
    methods: {
        fetchList(page, per) {
            this.checks = [];
            this.checkedAll = false;
            axios
                .get("/api/par/apparatus", {
                    params: {
                        page: page,
                        per: per,
                        key: this.key,
                    },
                })
                .then((response) => {
                    //   console.log(response);
                    this.lists = response.data;
                    // this.getEmployees = response.data ?? [];
                    this.computedWhite();
                })
                .catch((error) => {
                    console.error(error);
                });
        },
        checkAll() {
            this.checks = [];
            if (this.checkedAll) {
                for (var i in this.lists.data) {
                    this.checks.push(this.lists.data[i].id);
                }
            }
        },

        edit(id) {
            this.$parent.id = id;
            this.$parent.action = 1;
        },
        release() {
            axios
                .post("/api/par/reserve/cancel", {
                    id: this.release_reserveId,
                    cancel_type: 0,
                })
                .then((response) => {
                    if (response.data) {
                        this.$refs.toast.add({
                            severity: "success",
                            summary: "完成預約成功",
                            life: 3000,
                        });
                        this.fetchList(1, 10);
                    } else
                        this.$refs.toast.add({
                            severity: "error",
                            summary: response.data.error,
                            life: 3000,
                        });
                })
                .catch((error) => {
                    console.error(error);
                })
                .finally(() => {
                    this.displaySubOptions = false;
                });
        },
        async returnItem() {
            try {
                const response = await axios.delete(
                    "/api/par/apparatus/recipients",
                    {
                        params: {
                            apparatus_id: this.form.apparatus_id,
                            employee_id: this.form.employee_id,
                        },
                    }
                );
            } catch (error) {
                console.error(error);
            }
        },

        showDetail(sub) {
            console.log(sub);
        },
        remove(ids) {
            if (this.lists.data[this.list_index].subscribers.length == 0) {
                axios
                    .post("/api/par/apparatus/delete", {
                        id:
                            this.checks.length !== 0
                                ? ids
                                : this.lists.data[this.list_index].id,
                    })
                    .then((response) => {
                        this.fetchList();
                        this.$refs.toast.add({
                            severity: "success",
                            summary: "刪除成功",
                            life: 3000,
                        });
                    })
                    .catch((error) => {
                        console.error(error);
                        this.$refs.toast.add({
                            severity: "error",
                            summary: "刪除失敗",
                            life: 3000,
                        });
                    })
                    .finally(() => {
                        this.displayDelete = false;
                    });
            }
        },

        multipleRemove() {
            this.remove(this.checks);
        },
        computedWhite() {
            var computed = 204 + this.$refs.infoBox.offsetHeight;
            var out = this.$refs.outBox.offsetHeight;
            var calc = out - computed + "px";
            this.$set(this.white_space, "height", calc);
        },
        openPa(id, isOpen) {
            axios
                .put("/api/par/switch", {
                    id: id,
                    type: "apparatus",
                    open: isOpen,
                })
                .then((response) => {
                    if (response.data) {
                        if (isOpen) {
                            this.$refs.toast.add({
                                severity: "success",
                                summary: "設施已開啟",
                                life: 3000,
                            });
                        } else {
                            this.$refs.toast.add({
                                severity: "success",
                                summary: "設施已關閉",
                                life: 3000,
                            });
                        }
                    } else {
                        this.$refs.toast.add({
                            severity: "error",
                            summary: "修改失敗",
                            life: 3000,
                        });
                    }
                })
                .catch((error) => {
                    console.error(error);
                    if (error.response) {
                        this.$refs.toast.add({
                            severity: "error",
                            summary: "修改失敗!",
                            life: 3000,
                        });
                    }
                })
                .finally(() => {
                    this.displayDelete = false;
                });
        },
        // 完成預約
        confirmReserve(event) {
            this.$refs.confirmPopup.visible = true;
            this.$refs.confirmPopup.target = event.currentTarget;
            this.$refs.confirmPopup.confirmation = {
                target: event.currentTarget,
                message: "是否確定完成預約",
                acceptLabel: "確定",
                rejectLabel: "取消",
                accept: async () => {
                    await this.release();
                    this.displaySub = false;
                },
                reject: () => {
                    this.displaySub = false;
                },
            };
        },
        confirmReturn(event) {
            this.changeConfirmReturn();
            this.$refs.confirmPopup.visible = true;
            this.$refs.confirmPopup.target = event.currentTarget;
            this.$refs.confirmPopup.confirmation = {
                target: event.currentTarget,
                message: "是否確認歸還",
                acceptLabel: "確認",
                rejectLabel: "取消",
                accept: async () => {
                    await this.returnItem();
                    this.fetchList(1, 10);
                    this.displayRecipient = false;
                    this.$refs.toast.add({
                        severity: "success",
                        summary: "確認歸還成功",
                        life: 3000,
                    });
                },
                reject: () => {
                    this.displayRecipient = false;
                },
            };
        },
        getEmployees() {
            axios
                .get("/api/demand/employees")
                .then((response) => {
                    this.people = response.data.map((item) => ({
                        employee_id: item.id,
                        name: item.org_name + " " + item.name,
                    }));
                })
                .catch((error) => {
                    console.error(error);
                });
        },
        validate() {
            let error = false;
            this.formInvalid.apparatus_id = this.form.apparatus_id === null;
            this.formInvalid.employee_id = this.form.employee_id === null;
            this.formInvalid.remark = this.form.remark === "";
            Object.values(this.formInvalid).forEach((item) => {
                if (item === true) {
                    error = true;
                }
            });
            return error;
        },
        async addRecipient() {
            if (!this.validate()) {
                try {
                    const response = await axios.post(
                        "/api/par/apparatus/recipients",
                        {
                            apparatus_id: this.form.apparatus_id,
                            employee_id: this.form.employee_id,
                            remark: this.form.remark,
                        }
                    );
                    this.form.remark = "";
                    this.form.employee_id = null;
                    this.displayAddRecipient = false;
                    this.$refs.toast.add({
                        severity: "success",
                        summary: "新增領取人成功",
                        life: 3000,
                    });
                    this.fetchList(1, 10);
                } catch (error) {
                    console.error(error);
                }
            }
        },
        changeIdToName(employee_id) {
            const { name } = this.people.find(
                (item) => item.employee_id === employee_id
            );
            return name;
        },
        changeISOTime(isoString) {
            return new Date(isoString).toLocaleString("sv");
        },
        changeConfirmReturn() {
            this.form.apparatus_id = this.lists.data[this.list_index].id;
            this.form.employee_id =
                this.lists.data[this.list_index].recipients[
                    this.list_recipients_index
                ].employee_id;
        },
        combineTime(time) {
            return time[0].slice(0, 6) + time[time.length - 1].slice(6);
        },
    },
};
</script>
<style>
.slide-leave-active,
.slide-enter-active {
    transition: all 0.9s ease;
}
.slide-enter-from {
    transform: translateY(-100%);
}
.slide-leave-to {
    transform: translateY(100%);
}
</style>
