<template>
  <div>
    <!-- 檔案版本歷史紀錄 -->
    <Dialog
      :visible.sync="computedFileHistoryVisible"
      :modal="true"
      :containerStyle="{
        maxWidth: '90%',
        width: '440px',
        height: '440px',
      }"
      dismissableMask
    >
      <template #header>
        <h1 class="text-3xl font-bold text-explorerPrimary">版本歷程記錄</h1>
      </template>
      <div class="w-full h-full">
        <p class="border-b border-gray-200 pb-6 font-bold text-explorerPrimary">
          {{ selectedItem?.name }}
        </p>
        <div
          v-if="!versionHistoryRecord.length"
          class="mt-6 flex justify-center items-center w-full h-64 text-xl font-bold text-explorerPrimary"
        >
          無歷程記錄
        </div>
        <div v-else class="h-64">
          <ul
            v-for="(recode, index) of versionHistoryRecord"
            :key="index"
            class="mt-6 text-explorerPrimary"
          >
            <li class="flex items-center justify-between">
              <div class="w-14">
                <img
                  :src="selectedItem?.iconPath"
                  alt="file-icon"
                  class="w-full"
                />
              </div>
              <div class="ml-2 w-40">
                <div>{{ recode.timeCreated }}</div>
                <div>上傳人：{{ recode.uploader }}</div>
              </div>
              <button
                :disabled="
                  !currentFolderData?.admin && !currentFolderData?.download
                "
                class="flex items-center justify-center w-20 ml-2 hover:bg-gray-200 rounded-lg px-2 py-1"
                :class="
                  !currentFolderData?.admin &&
                  !currentFolderData?.download &&
                  'cursor-not-allowed'
                "
                @click="$emit('onDownload', true, recode.id)"
              >
                <img
                  src="@images/icon/download.svg"
                  alt="download"
                  class="w-6 mr-2"
                />
                <span class="whitespace-nowrap">下載 </span>
              </button>
              <button
                :disabled="
                  !currentFolderData?.admin && !currentFolderData?.read
                "
                class="flex items-center justify-center w-20 before: ml-2 hover:bg-gray-200 rounded-lg px-2 py-1"
                :class="
                  !currentFolderData?.admin &&
                  !currentFolderData?.read &&
                  'cursor-not-allowed'
                "
                @click="$emit('onOpen', selectedItem, true, recode.id)"
              >
                <img src="@images/icon/open.svg" alt="open" class="w-6 mr-2" />
                <span class="whitespace-nowrap">開啟 </span>
              </button>
            </li>
          </ul>
        </div>
      </div>

      <template #footer>
        <div></div>
      </template>
    </Dialog>

    <!-- 檔案內容 -->
    <Dialog
      :visible.sync="computedFileDetailsVisible"
      :modal="true"
      :containerStyle="{
        maxWidth: '90%',
        maxHeight: '90%',
        width: '440px',
      }"
      dismissableMask
    >
      <template #header>
        <h1 class="text-3xl font-bold text-explorerPrimary">檔案內容</h1>
      </template>
      <div class="text-explorerPrimary" v-if="selectedItem">
        <div class="py-3">檔案名稱： {{ selectedItem?.name }}</div>
        <div class="py-3">檔案格式： {{ selectedItem?.type }}</div>
        <div class="py-3">
          上傳日期： {{ selectedItem?.timeCreated?.split("T")[0] }}
        </div>
        <div class="py-3">
          檔案大小： {{ Math.floor((selectedItem?.size * 1) / 1024) + " KB" }}
        </div>
      </div>
      <template #footer>
        <div></div>
      </template>
    </Dialog>
  </div>
</template>

<script>
import Dialog from "primevue/dialog";

export default {
  name: "ContextMenuDialogs",
  components: {
    Dialog,
  },
  props: {
    isShow: { type: Object, required: true },
    selectedItem: { type: Object },
    currentFolderData: { required: true },
    versionHistoryRecord: { type: Array, required: true },
  },
  computed: {
    computedFileHistoryVisible: {
      get() {
        return this.isShow.fileHistory;
      },
      set(value) {
        this.$emit("toggleDialog", value, "fileHistory");
      },
    },
    computedFileDetailsVisible: {
      get() {
        return this.isShow.fileDetails;
      },
      set(value) {
        this.$emit("toggleDialog", value, "fileDetails");
      },
    },
  },
};
</script>

<style scoped>
.p-dialog::-webkit-scrollbar {
  width: 8px !important;
  height: 8px !important;
}
.p-dialog::-webkit-scrollbar-track {
  border-radius: 10px !important;
}
</style>
