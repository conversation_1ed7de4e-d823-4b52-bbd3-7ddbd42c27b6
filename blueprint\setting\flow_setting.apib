## fetchMustMul [api/demand/fetch/mul]
獲取需求單單選題(dropdown, single)必填欄位
### fetchMustMul [GET]

Request (application/json)
    Attributes
        + id : 424 (number, required) - 需求單的Id
Response 200 (application/json)
    Attributes
        (Array)
            (object)
                + id : 1 - 欄位Id
                + name : 選擇支付方式 - 欄位名稱


## fetchMustMul [api/demand/fetch/mul]
獲取欄位選項
### fetchMustMul [GET]

Request (application/json)
    Attributes
        + id : 1 (number, required) - 欄位的Id
Response 200 (application/json)
    Attributes
        (Array)
            (object)
                + id : 1 - 選項的Id
                + name : 信用卡支付 - 選項名稱
                + sign_role_models (Array) - 選項審核人流程
                + todo_role_models (Array) - 選項經辦人流程


## updateLayout [api/demand/setting/layout]
獲取layouts相關資訊
### saveRoleSetting [PUT]

Request (application/json)
    Attributes
        id : 424 (number, required) - 需求單的Id
        roles (array) - 簽核關卡設定內容
            (object)
                type: 1,
                child: false,
                document: false,
                countersigned: false,
                ....
        + role_type : 0 (number, required) - 設定流程模式
Response 200 (application/json)
    Attributes
        1 or 0 (number, required) - 成功或失敗

