<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddSoftDeleteToTables extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('demand_groups', function (Blueprint $table) {
            $table->softDeletes();
        });
        Schema::table('list_layouts', function (Blueprint $table) {
            $table->softDeletes();
        });
        Schema::table('databases', function (Blueprint $table) {
            $table->softDeletes();
        });
        Schema::table('demands', function (Blueprint $table) {
            $table->softDeletes();
        });
        Schema::table('demand_logs', function (Blueprint $table) {
            $table->softDeletes();
        });
        Schema::table('codes', function (Blueprint $table) {
            $table->softDeletes();
        });
        Schema::table('notifications', function (Blueprint $table) {
            $table->softDeletes();
        });
        Schema::table('data_auths', function (Blueprint $table) {
            $table->softDeletes();
        });
        Schema::table('func_auths', function (Blueprint $table) {
            $table->softDeletes();
        });
        Schema::table('import_demands', function (Blueprint $table) {
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
//
    }
}
