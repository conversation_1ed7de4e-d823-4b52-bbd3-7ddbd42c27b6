<?php

namespace Database\Factories;

/** @var \Illuminate\Database\Eloquent\Factory $factory */

use App\Modules\par\Models\Apparatus;
use Faker\Generator as Faker;

$factory->define(Apparatus::class, function (Faker $faker) {
    return [
        'company_id' => 1,
        'name' => $faker->unique()->word,
        'payload' => [
            "types" => $faker->word,
            "no" => $faker->unique()->word,
            "amount" => $faker->numberBetween(9, 150),
            "remark" => $faker->text(50),
            // "picture" => '',
            "org_unit_id" => $faker->randomDigitNotNull
        ],
        'metadata' => [],
        'created_by' => 1,
        'updated_by' => 1,
    ];
});
