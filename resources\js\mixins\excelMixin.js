export const excelMixin = {
    data() {
        return {
            isExporting: false,
        };
    },
    methods: {
        /**
         * 匯出 Excel
         * @param {String} apiURL API URL
         * @param {Object} params 參數
         * @param {String} docName 檔案名稱
         * @param {Boolean} disabledButton 按鈕狀態
         */
        async exportExcel(apiURL, params) {
            try {
                this.isExporting = true;
                const response = await axios.get(apiURL, {
                    params,
                    responseType: "blob",
                });

                let fileName = "download.xlsx";

                const disposition = response.headers["content-disposition"];
                if (disposition && disposition.indexOf("attachment") !== -1) {
                    fileName = decodeURIComponent(
                        disposition.split("filename*=UTF-8''")[1]
                    );
                }

                const blob = new Blob([response.data], {
                    type: response.headers["content-type"],
                });

                const url = URL.createObjectURL(blob);

                const link = document.createElement("a");
                link.href = url;
                link.setAttribute("download", fileName);
                link.click();
                URL.revokeObjectURL(url);
            } catch (error) {
                console.error("Download failed:", error);
            } finally {
                this.isExporting = false;
            }
        },
    },
};
