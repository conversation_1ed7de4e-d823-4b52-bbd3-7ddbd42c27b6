<?php

namespace App\Modules\explorer\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Session;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\BadResponseException;
use App\Modules\Demand\Models\FuncAuth;

class AuthController extends Controller
{
    protected $company_id, $user_id, $file_url, $client;
    public function __construct()
    {
        $this->company_id = (int) Session::get('CompanyId');
        $this->user_id = (int) Session::get('employee_id');
        $this->file_url = config("app.services_file_api_url") . '/permission';
        $this->client = new Client();
    }


    public function index()
    {
        return view('explorer.permission-settings');
    }

    public function fetchAuthSettingList()
    {
        $funcAuth = FuncAuth::where('company_id', $this->company_id)
            ->select('id', 'payload')
            ->where('type', 'explorer')
            ->with([
                'users:id,payload->name as name',
                'createWhite:id,payload->name as name',
            ])
            // ->whereNotNull('payload->user_list')
            ->first();

        return [
            'id' => $funcAuth->id,
            'auth' => $funcAuth->users,
            'createWhite' => $funcAuth->createWhite,
        ];

    }

    public function updateAndCreate(Request $request)
    {
        if (!$request->has(['users', 'type']))
            return response('unsupported', 400);

        $list = $request->get('users');
        $type = $request->get('type');

        $funcAuth = FuncAuth::where('company_id', $this->company_id)
            ->where('type', 'explorer')
            ->first();

        if ($type == 'auth')
            $funcAuth->forceFill(['payload->user_list' => $list])->save();
        elseif ($type == 'createWhite')
            $funcAuth->forceFill(['payload->create_white' => $list])->save();

        return 1;
    }


    public function fetchAllDirAuth()
    {
        $url = $this->urlScope($this->file_url . '/folder');

        try {
            $response = $this->client->get($url);
        } catch (BadResponseException $e) {
            $msg = json_decode($e->getResponse()->getBody()->getContents())->msg;
            return response()->json(['msg' => $msg], 400);
        }
        $result = json_decode((string) $response->getBody());

        return response()->json($result->data, 200);
    }

    public function getDirAuth($folderId)
    {
        $url = $this->urlScope($this->file_url . '/folder/' . $folderId);

        try {
            $response = $this->client->get($url);
        } catch (BadResponseException $e) {

            $msg = json_decode($e->getResponse()->getBody()->getContents())->msg;
            return response()->json(['msg' => $msg], 400);
        }

        $result = json_decode((string) $response->getBody());
        return response()->json($result->data, 200);
    }

    public function setDirAuth(Request $request, $folderId)
    {
        if (!$request->has(['type', 'users']))
            return response()->json(['msg' => 'unsupported'], 400);

        $type = $request->input("type");
        $users = $request->input("users");

        if (!is_array($users) || !is_string($type))
            return response()->json(['msg' => 'unsupported'], 400);

        $url = $this->urlScope($this->file_url . '/folder/' . $folderId);

        try {
            $response = $this->client->put($url, [
                'json' => [
                    'type' => $type,
                    'users' => $users
                ]
            ]);
        } catch (BadResponseException $e) {
            $msg = json_decode($e->getResponse()->getBody()->getContents())->msg;
            return response()->json(['msg' => $msg], 400);
        }

        return response(200);
    }

    public function cleanDirAuth($folderId)
    {
        if (!isset($folderId))
            return response('unsupported', 400);

        $url = $this->urlScope($this->file_url . '/folder/remove/' . $folderId);

        try {
            $response = $this->client->put($url);
        } catch (BadResponseException $e) {
            $msg = json_decode($e->getResponse()->getBody()->getContents())->msg;
            return response()->json(['msg' => $msg], 400);
        }

        return response(200);
    }

    public function extendAuth($folderId)
    {
        if (!isset($folderId))
            return response('unsupported', 400);

        $functauth = FuncAuth::query()
            ->where('company_id', $this->company_id)
            ->where('type', 'explorer')
            ->whereJsonContains('payload->user_list', [$this->user_id])
            ->exists();

        if (!$functauth)
            return response('Unauthorized', 400);

        $url = $this->urlScope($this->file_url . '/folder/inherit/' . $folderId);

        try {
            $response = $this->client->post($url);
        } catch (BadResponseException $e) {
            $msg = json_decode($e->getResponse()->getBody()->getContents())->msg;
            return response()->json(['msg' => $msg], 400);
        }

        return response(200);
    }
}
