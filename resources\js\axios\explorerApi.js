import request from "./request";

export const getRootData = () => {
    return request({
        url: "/root",
        method: "get",
    });
};
export const getExplorerData = (id) => {
    return request({
        url: `/current/${id}`,
        method: "get",
    });
};
export const getDirectory = () => {
    return request({
        url: "/dir",
        method: "get",
    });
};
export const addFolder = (params) => {
    return request({
        url: "/folder",
        method: "post",
        data: params,
    });
};
export const renameItem = (id, params) => {
    return request({
        url: `/rename/${id}`,
        method: "put",
        data: params,
    });
};
export const removeItem = (params) => {
    return request({
        url: `/delete`,
        method: "post",
        data: params
    });
};
export const copyItem = (params) => {
    return request({
        url: `/copy`,
        method: "put",
        data: params,
    });
};
export const cutItem = (params) => {
    return request({
        url: `/move`,
        method: "put",
        data: params,
    });
};
export const getFile = (params) => {
    return request({
        url: "/file-info",
        method: "get",
        responseType: "blob",
        data: params,
    });
};
export const getVersionRecord = (id) => {
    return request({
        url: `/version/${id}`,
        method: "get",
    });
};
export const searchFile = (params) => {
    return request({
        url: "/search",
        method: "get",
        data: params,
    });
};
export const uploadNotify = (params) => {
    return request({
        url: "/notify/upload",
        method: "post",
        data: params,
    });
};
export const uploadFile = (params) => {
    return request({
        url: "/upload",
        method: "post",
        headers: {
            "Content-Type": "multipart/form-data",
        },
        data: params,
    });
};
export const getFileRules = () => {
    return request({
        url: "/rules",
        method: "get",
    });
};
export const updateRules = (params) => {
    return request({
        url: "/rules",
        method: "put",
        data: params,
    });
};
export const useRule = (ruleId) => {
    return request({
        url: `/rule/${ruleId}`,
        method: "put",
    });
};
export const removeRule = (ruleId) => {
    return request({
        url: `/rule/${ruleId}`,
        method: "delete",
    });
};
export const getFunctionPermissions = () => {
    return request({
        url: "/permission/function",
        method: "get",
    });
};
export const updateFunctionPermissions = (id, params) => {
    return request({
        url: `/permission/function/${id}/`,
        method: "put",
        data: params,
    });
};
export const getAFolderPermissions = (id) => {
    return request({
        url: `/permission/folder/${id}`,
        method: "get",
    });
};
export const getAllFolderPermissions = () => {
    return request({
        url: "/permission/folder",
        method: "get",
    });
};
export const updateFolderPermissions = (id, params) => {
    return request({
        url: `/permission/folder/${id}`,
        method: "put",
        data: params,
    });
};
export const inheritPermission = (id) => {
    return request({
        url: `/permission/folder/inherit/${id}`,
        method: "post",
    });
};
export const removePermission = (id) => {
    return request({
        url: `/permission/folder/remove/${id}`,
        method: "put",
    });
};
