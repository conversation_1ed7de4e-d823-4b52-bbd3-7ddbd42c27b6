<?php

namespace App\Jobs;

use Carbon\Carbon;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\Modules\Demand\Models\Demand;
use App\Modules\Demand\Models\DemandLayout;
use App\Events\DemandUpdated;
use Illuminate\Http\Request;
use App\Modules\Demand\Services\SubmitService;
use App\Modules\Demand\Models\Company;
use App\Modules\Demand\Models\Employee;

class CreateACCDemandJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;


    public $request, $employee_id, $companyId, $timezone, $demands = [];
    /**
     * Create a new job instance.
     */
    public function __construct($employee_id, $request)
    {
        $this->employee_id = $employee_id;
        $this->request = $request;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        try {
            $this->setRequiredId($this->employee_id);
            // payload ->>'unique_key'='acc_only'
            $dl = DemandLayout::where('payload->unique_key', 'acc_only')->first();

            DB::beginTransaction();
            Log::info($this->employee_id . ' : CreateACCDemandJob start');

            // 建立需求單
            if ($this->request['isMultiple']) {
                if (!Arr::has($this->request, ['startYm', 'endYm', 'pbatch', 'projects', 'details', 'isMultiple'])) {
                    Log::error('request error');
                    return;
                }
                $this->multiple_submit($this->request, $dl);
            } else {
                if (!Arr::has($this->request, ['startYm', 'endYm', 'pbatch', 'projects', 'details', 'isMultiple'])) {
                    Log::error('request error');
                    return;
                }
                $this->single_submit($this->request, $dl);
            }

            // 發送事件
            $this->demands = array_chunk($this->demands, 100);
            foreach ($this->demands as $key => $value) {
                event(new DemandUpdated($value));
            }
            Log::info($this->employee_id . ' : CreateACCDemandJob end');
            DB::commit();
        } catch (\Exception $th) {
            Log::error($th);
            DB::rollBack();
        }
    }
    private function setRequiredId($employee_id)
    {
        $e = Employee::find($employee_id);
        $this->companyId = $e->company_id;
        $c = Company::find($e->company_id);
        $this->timezone = $c ? $c->payload->get('timezone') : 'Asia/Taipei';
    }
    private function multiple_submit($request, $dl): void
    {
        $pbatchno = $request['pbatch'];
        $projects = $request['projects'];
        $details = $request['details'];

        foreach ($projects as $key => $pj) {
            $pj['pbatchno'] = $pbatchno;

            // 已事先在acc collection gorupby
            // pkey 分類
            $subDetails = $details[$pj['epjacc'] . '_' . $pj['epjno']];

            $request['pjInfo'] = $pj;
            $request['details'] = array_column($subDetails, 'payload');
            // $request->merge([
            //     'pjInfo' => $pj,
            //     'details' => array_column($subDetails, 'payload') //$subDetails->pluck('payload')
            // ]);

            $this->single_submit($request, $dl);
        }
    }
    private function single_submit($request, $dl): void
    {
        try {
            $startYm = $request['startYm'];
            $endYm = $request['endYm'];
            $pbatchno = $request['pbatch'];
            $pjInfo = $request['pjInfo'];
            $details = $request['details'];
            $resopnseUrl = $request['resopnseUrl'];


            // acc 有flatten 過
            // $details = collect($details)->flatten(1);

            $pjInfo['pbatchno'] = $pbatchno;

            $signRole = $this->signRole($pjInfo, $dl->sign_role->toArray());
            $forms = [
                collect($dl->payload->get('columns'))
                    ->map(function ($item) use ($pjInfo, $details) {
                        if (isset($item['acc_name']))
                            return $item = array_merge(
                                $item,
                                [
                                    'value' => $pjInfo[$item['acc_name']]
                                ]
                            );
                        else {
                            // 自訂表單
                            return $this->makeCustomList($item, $details);
                        }
                    })
            ];

            $submitRequest = new Request();
            $submitRequest->merge([
                'id' => $dl->id,
                'layout_original_id' => $dl->payload->get('layout_original_id') ?? $dl->id,
                'is_child' => false,
                "filter_roles" => [],
                'forms' => $forms,
                'sign_role' => $signRole,
                'responseUrl' => $resopnseUrl,
            ]);
            unset($forms);
            unset($signRole);
            $required = [
                'employee_id' => $this->employee_id,
                'CompanyId' => $this->companyId,
                'timezone' => $this->timezone,
            ];
            $result = (new SubmitService())->createDemand($required, $submitRequest);


            /*
                2024-11-27 - 
                - 正式區自動簽核:
                   因財務系統採階段性上線，簽核部分仍維持紙本。故系統自動完成此簽核流程。

                - 測試區手動簽核:
                    需要測試簽核的流程，所以手動簽核
            */
            if (!config('app')['debug'] && $result) {

                $demand = Demand::find($result);
                $sign_role = $demand->payload->get('sign_roles');
                foreach ($sign_role as $key => $value) {
                    $sign_role[$key]['apply_status'] = 2;
                    $sign_role[$key]['isRepresent'] = false;
                    // $sign_role[$key]['role_id'] = $user;
                    $sign_role[$key]['remark'] = '因財務系統採階段性上線，簽核部分仍維持紙本。故系統自動完成此簽核流程。';
                    // $sign_role[$key]['remark'] = '系統自動審核';
                    $sign_role[$key]['timestamp'] = Carbon::now()->format('Y/m/d H:i') . '已審核';
                    $sign_role[$key]['raw_time'] = Carbon::now();
                }

                // 一開始規劃就要的column
                $demand->forceFill([
                    'payload->startYm' => $startYm,
                    'payload->endYm' => $endYm,
                    'payload->pbatchno' => $pbatchno,
                ])
                    ->setAttribute('payload->sign_roles', $sign_role)
                    ->setAttribute('payload->status', 2)
                    ->save();
                $this->demands[] = $demand;

                // 將demand move到DemandLogTable
                app(SubmitService::class)->moveToDemandLog($demand->id);
            }
        } catch (\Exception $e) {
            Log::error('request : ', $request);
            Log::error($e);
        }
    }


    private function signRole($pjInfo, array $sign_role)
    {
        // 第1關: PC (PCM) 工程主管 ->可以修改金額
        // 第2關: 財務/會計主管 ->固定 id=202，除非離職才換人
        // 第3關: PM ->可以修改金額
        // 第4關: PD ->批示
        $sign_role[0]['role_id'] = $pjInfo['pcid'];
        $sign_role[1]['role_id'] = 202;
        $sign_role[2]['role_id'] = $pjInfo['pmid'];
        $sign_role[3]['role_id'] = $pjInfo['pdid'];

        $sign_role = collect($sign_role)

            ->filter(function ($value) {
                // 不會每關都有人，所以要篩選
                return isset($value['role_id']);
            })
            ->values()
            ->transform(function ($item, $key) {
                $item['id'] = $key + 1;
                return $item;
            })
            ->toArray();

        return $sign_role;
    }
    private function makeCustomList($column, $details)
    {
        $custom_list = collect($details)
            // ->flatten(1)
            ->map(function ($value) {
                return [
                    $value['venno'],
                    $value['venna'],
                    $value['payrmk'],
                    $value['vencamt'],
                    $value['tvenpay'],
                    $value['balance'],
                    $value['npay'],
                    $value['ppay'],
                    $value['hpay'],
                    $value['dpay'],
                    $value['venpay'],
                    $value['tax'],
                    $value['venpay'],
                    '',
                    $value['venpay'],
                    '',
                    $value['venpay'],
                    '',
                    $value['venpay'],
                    '',
                    $value['unkey'],
                    $value['copname'],
                    $value['pjctrno'],
                    $value['po'],
                    $value['_unkey'],
                ];
            });
        $column['custom_list'] = $custom_list;
        return $column;
    }
}
