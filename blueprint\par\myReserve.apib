## Fetch My Reserve List [api/reserve/own]
查詢個人預約列表
### Fetch My Reserve List [GET]

+ Request (application/json)
    + Attributes

+ Response 200 (application/json)
    + Attributes (Array) :
        (object) : 設備列表
            id : 12 (number, required) - 預約id
            name : 測量儀 (string, required) - 設備/設施名稱
            type :  postulate / apparatus - 是設備或設施
            date : 2021/4/19 (string, required) - 預約日期
            count : null or 2 (number, required) - 預約數量(無數量回傳null ex: 設施不用數量)
            times (Array, required) - 預約時段
                08:00 - 08:30、08:30 - 09:00、09:00 - 09:30、09:30 - 10:00
            reason : 事由內文 (string, required) - 預約事由
            remark : 備註內文 (string, required) - 備註內容
            cancel_btn : 0 or 1 (number) -  0:提前結束; 1:取消預約


## Cancel Reserve [api/reserve/cancel]
取消預約 or 提前結束
### Cancel Reserve [POST]

+ Request (application/json)
    + Attributes
        id : 12 (number, required) -預約id
        cancel_type : 0 or 1 (number) -  0:提前結束; 1:取消預約
+ Response 200 (application/json)
    + Attributes
        state : 1 or 0 (number) - 成功或失敗
        error (string) 失敗原因 - 失敗原因

## Edit Reserve [api/reserve/edit]
修改個人預約設施/設備資訊
### Edit Reserve [GET]

+ Request (application/json)
    + Attributes
        id : 12 (number, required) -預約id
        type : postulate/apparatus
+ Response 200 (application/json)
    + Attributes (Array) :
        (object) : 設施/設備類別資訊
            list : (object) - 設備或設施資訊
                + pa_id : 12 (number, required) - 設備/設施類別的id
                image_url (string, required) : /storage/par/image/.... (string, required) - 第一張設施/設備圖片
                name : 測量儀 (string, required) - 設備/設備名稱
                total_amount : 2 (number, required) - 設備總數量/設施給null
                org_unit : 工程部 (string, required) - 所屬部門/設施給null
                place : 10樓 (string, required) - 設施地點/設備給null
                people : 10人 (string, required) - 容納人數/設備給null
                remark : 說明內文 (string, required) - 設備/設備說明
            date : Wed Mar 15 2016 08:00:00 GMT+0800 (CST) (string, required) - 預約日期
            times (Array, required) - 預約時段的Ids
                [1,2,3]
            reason : 事由內容 (string, required) - 預約事由
            remark : 備註內容 (string, required) - 備註
            apparatus (Array, required) - 設施預約之設備/設施給null
                (object): 
                    id : 12 (number, required) - 設備類別的id
                    name : 測量儀 (string, required) - 設備名稱

## Again Reserve [api/reserve/again]
再次預約設備/設施
### Again Reserve [POST]

+ Request (application/json)
    + Attributes
        + id : 12 (number, required) -預約id
        + form (object): 預約內容資訊
            pa_id : 12 (number, required) - 設備/設施類別的id
            type : postulate/apparatus
            time (Array) - 預約時段的Ids
                1 (number, required) - 時段的Id
            date : Wed Mar 15 2016 08:00:00 GMT+0800 (CST) (DateObject, required) - 預約日期
            amount : 2 (number, required) - 預約數量
            reason : 事由內容 (string, required) - 預約事由
            remark : 備註內容 (string, required) - 備註
+ Response 200 (application/json)
    + Attributes (object) : 預約成功或失敗及失敗原因
            state : 1 or 0 (number) - 成功或失敗
            error (Array)
                12:00-12:30、12:30-13:00、13:00-13:30、13:30-14:00 (Array) - 無法預約時段