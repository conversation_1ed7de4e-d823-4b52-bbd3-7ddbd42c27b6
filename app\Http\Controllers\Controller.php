<?php

namespace App\Http\Controllers;

use App\Traits\ApiResponder;
use Illuminate\Foundation\Bus\DispatchesJobs;
use Illuminate\Routing\Controller as BaseController;
use Illuminate\Foundation\Validation\ValidatesRequests;
use Illuminate\Support\Facades\Session;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;

class Controller extends BaseController
{
    use AuthorizesRequests, DispatchesJobs, ValidatesRequests, ApiResponder;

    public function errorReturn($msg)
    {

        return  [
            'state' => 0,
            'error' => $msg
        ];
    }

    /**
     * 打api到services-file，加入scope
     * @param string $url "http://services-file/explorer"
     * @return string url
     */
    public function urlScope($url)
    {
        return "$url?ap=ASAP&company_id=".Session::get('CompanyId')."&employee_id=".Session::get('employee_id');
    }
}
