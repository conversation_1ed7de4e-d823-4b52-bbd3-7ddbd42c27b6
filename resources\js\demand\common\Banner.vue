<template>
    <div
        class="banner-list flex justify-between w-full shadow-md px-5 overflow-x-auto md:overflow-x-visible pt-7 bg-white noPrint"
    >
        <ul class="flex">
            <li
                v-for="(name, name_index) in $root.titles"
                :key="name_index"
                class="mr-0 md:mr-3 opacity-50 hover:opacity-100 hover:font-bold hover:border-b-4 whitespace-nowrap cursor-pointer"
                :class="isActive(name_index)"
                @click="changeTab(name_index)"
            >
                <p class="px-2 pb-7">{{ name }}</p>
            </li>
        </ul>
        <div class="hidden lg:block">
            <div
                v-if="isShow !== ''"
                @click="isShow = ''"
                class="fixed h-screen top-0 left-0 w-full z-30 lg:z-auto"
            ></div>
            <!-- 員工資料 -->
            <span @click="showPerson" class="relative mr-9">
                <button title="個人資料" class="hover:text-primary">
                    <i class="far fa-user-circle text-xl"></i>
                </button>
                <div
                    @click.stop
                    v-if="isShow == 'person'"
                    class="absolute top-9 md:top-16 -right-14"
                >
                    <div
                        class="speech-bubble relative p-8 bg-white rounded-lg shadow z-10"
                    >
                        <div class="flex justify-between">
                            <div class="flex">
                                <img
                                    :src="user == null ? '' : user.photo"
                                    class="rounded-full mr-5 w-24"
                                />
                                <div class="my-auto">
                                    <span class="text-xl font-semibold">{{
                                        user == null ? "" : user.name
                                    }}</span>
                                    <br />
                                    <p class="text-sm text-gray-400">
                                        {{ user == null ? "" : user.title }}
                                    </p>
                                </div>
                            </div>
                            <div>
                                <a href="/logout"
                                    ><Button label="登出" class="w-28"
                                /></a>
                            </div>
                        </div>
                        <div class="flex w-4/5 mt-6">
                            <div class="w-1/4 pr-10">
                                <span class="text-sm text-gray-400">
                                    員工編號
                                </span>
                                <br />
                                {{ user == null ? "" : user.no }}
                            </div>
                            <span class="text-3xl text-gray-100">｜</span>
                            <div class="w-1/4 px-10">
                                <span class="text-sm text-gray-400">
                                    部門
                                </span>
                                <br />
                                {{ user == null ? "" : user.dep }}
                            </div>
                            <span class="text-3xl text-gray-100">｜</span>
                            <div class="w-44 pl-10">
                                <span class="text-sm text-gray-400">
                                    代理人
                                </span>
                                <span @click="editAgentModal = true">
                                    <i class="fas fa-pencil-alt"></i>
                                </span>
                                <!-- <span @click="editAgentModal = true">
                                        <i class="fas fa-pencil-alt"></i>
                                    </span> -->
                                <br />
                                {{ user == null ? "" : user.agent }}
                            </div>
                            <div class="w-44">
                                <span> 代理人代簽核 </span>
                                <br />
                                <div>
                                    <InputSwitch
                                        @change="changeAgentSwitch()"
                                        v-model="user.agentSign"
                                    />
                                </div>
                                <!-- <div @click="agentSign()">
                                        <InputSwitch
                                            v-model="
                                                user == null
                                                    ? ''
                                                    : user.agentSign
                                            "
                                        />
                                    </div> -->
                            </div>
                            <span class="text-3xl text-gray-100">｜</span>
                            <div class="w-1/4 px-10">
                                <span class="text-sm text-gray-400">
                                    直屬主管
                                </span>
                                <br />
                                {{ user == null ? "" : user.manager }}
                            </div>
                        </div>
                    </div>
                </div>
            </span>
            <!-- 通知提醒 -->
            <span
                @click="
                    isShow == 'notify' ? (isShow = '') : (isShow = 'notify')
                "
                class="relative"
            >
                <span
                    v-if="notifies.length > 0"
                    class="absolute -right-2 -top-4 text-primary align-top text-2xl"
                    >•</span
                >

                <button title="通知提醒">
                    <i class="far fa-bell text-xl"></i>
                </button>
                <div
                    @click.stop
                    v-if="isShow == 'notify'"
                    class="absolute top-16 -right-3.5"
                >
                    <div
                        class="speech-bubble2 relative bg-white rounded-lg shadow z-10"
                    >
                        <div class="flex justify-between pt-8 px-8">
                            <p class="font-bold text-2xl">通知</p>
                            <button
                                @click="clearNotify()"
                                class="text-base underline mt-auto"
                            >
                                清除通知
                            </button>
                        </div>
                        <div
                            v-if="notifies.length == 0"
                            class="py-6 px-8 text-center"
                        >
                            目前無通知
                        </div>
                        <div v-else class="h-96 px-8 overflow-y-auto">
                            <div
                                v-for="(item, item_index) in notifies"
                                :key="item_index"
                            >
                                <div class="py-6 flex">
                                    <div class="w-5">
                                        <p
                                            v-if="!item.read"
                                            class="w-3 text-primary text-xl"
                                        >
                                            •
                                        </p>
                                    </div>
                                    <a
                                        @click="putNotify(item)"
                                        class="hover:underline"
                                        :href="
                                            item.url === 'explorer'
                                                ? 'javascript:;'
                                                : `/demand/${item.url}`
                                        "
                                        >{{ item.content }}</a
                                    >
                                </div>
                                <hr />
                            </div>
                        </div>
                    </div>
                </div>
            </span>
            <Dialog
                :visible.sync="editAgentModal"
                :dismissableMask="true"
                :closable="true"
                :containerStyle="{
                    width: '30vw',
                    height: '500px',
                }"
                :modal="true"
                header="編輯職務代理人"
                class="heighDialog"
            >
                <div class="pb-8">
                    <span>職務代理人</span>
                    <br />
                    <Dropdown
                        v-model="agent"
                        :options="getEmployees"
                        optionValue="id"
                        optionLabel="name"
                        :filter="true"
                        class="w-44"
                    />
                </div>
                <template #footer>
                    <div class="flex justify-end">
                        <Button
                            @click="editAgentModal = false"
                            label="取消"
                            class="p-button-outlined p-button-secondary"
                        />
                        <Button label="確定" @click="edit" />
                    </div>
                </template>
            </Dialog>
            <Dialog
                :visible.sync="isShowUploadResult"
                :modal="true"
                :closable="true"
                :containerStyle="{ maxWidth: '90%', width: '440px' }"
                :dismissableMask="true"
            >
                <template #header>
                    <h1 class="font-bold text-explorerPrimary text-3xl">
                        建檔結果
                    </h1>
                </template>
                <div class="font-bold text-explorerPrimary pb-6">
                    <p class="text-base">建檔時間：{{ uploadResult.time }}</p>
                    <p class="text-base">
                        建檔結果：{{ uploadResult.content }}
                    </p>
                </div>
                <div
                    v-if="
                        uploadResult.failed?.length ||
                        uploadResult.success?.length
                    "
                >
                    <div class="pt-6 border-t border-gray-300">
                        <h3
                            class="flex items-center text-explorerPrimary text-xl font-bold"
                        >
                            <img
                                src="@images/popup_state/wrong.png"
                                alt="wrong"
                                class="w-6 mr-1"
                            />
                            <span>失敗({{ uploadResult.failed?.length }})</span>
                        </h3>
                        <p
                            v-for="(item, index) in uploadResult.failed"
                            :key="index"
                            class="py-2 text-explorerPrimary text-base"
                        >
                            {{ item }}
                        </p>
                    </div>
                    <div class="mt-6">
                        <h3
                            class="flex items-center text-explorerPrimary text-xl font-bold"
                        >
                            <img
                                src="@images/icon/check.svg"
                                alt="success"
                                class="w-6 mr-1"
                            />
                            <span
                                >成功({{ uploadResult.success?.length }})</span
                            >
                        </h3>
                        <p
                            v-for="(item, index) in uploadResult.success"
                            :key="index"
                            class="py-2 text-explorerPrimary text-base"
                        >
                            {{ item }}
                        </p>
                    </div>
                </div>
                <template #footer>
                    <div></div>
                </template>
            </Dialog>
        </div>
    </div>
</template>
<script>
import axios from "axios";
import Button from "primevue/button";
import Dialog from "primevue/dialog";
import Dropdown from "primevue/dropdown";
import InputSwitch from "primevue/inputswitch";
export default {
    components: {
        Button,
        Dialog,
        Dropdown,
        InputSwitch,
    },
    data() {
        return {
            menu: 0,
            isShow: "",
            notifies: [],
            user: {},
            notifyURL: "/api/demand/notify",
            //代理開關
            agentType: 0,
            agent: 0,
            editAgentModal: false,
            getEmployees: [],
            isShowUploadResult: false,
            uploadResult: {
                time: "",
                content: "",
                failed: [],
                success: [],
            },
        };
    },
    watch: {
        editAgentModal: function (newValue) {
            if (newValue == true) {
                this.getEmployeesDropDown();
            }
        },
    },
    mounted() {
        this.getNotify();
        this.getUserData();
    },
    methods: {
        changeTab(tabIndex) {
            this.$root.currentTab = tabIndex;
        },
        showPerson() {
            this.isShow == "person"
                ? (this.isShow = "")
                : (this.isShow = "person");
            //this.getUser();
        },
        getUserData() {
            axios
                .get("/api/demand/user/data")
                .then((response) => {
                    this.user = response.data ?? {};
                    sessionStorage.setItem("userId", this.user.id);
                })
                .catch((error) => {
                    console.error(error);
                });
        },
        getNotify() {
            axios
                .get(this.notifyURL)
                .then((response) => {
                    this.notifies = response.data ?? [];
                })
                .catch((error) => {
                    console.error(error);
                });
        },

        clearNotify() {
            axios
                .delete(this.notifyURL)
                .then((response) => {
                    this.notifies = [];
                    this.getNotify();
                })
                .catch((error) => {
                    console.error(error);
                });
        },
        putNotify(item) {
            if (item.url === "explorer" && item.code !== 401) {
                this.isShowUploadResult = true;
                this.uploadResult = {
                    time: item.time,
                    content: item.content.match(/成功\d+個、失敗\d+個/)?.[0],
                    failed: item.failed,
                    success: item.success,
                };
            }
            if (item.read) return;
            axios
                .put(this.notifyURL + "/read-at", { id: item.id })
                .then((response) => {})
                .catch((error) => {
                    console.error(error);
                });
        },
        changeAgentSwitch() {
            //改變代簽核開關
            //沒做誤觸防呆
            let getAgent = this.user.agentSign ? 1 : 0;
            axios
                .put("/api/demand/user/agent/switch", { has_agent: getAgent })
                .then((response) => {})
                .catch((error) => {
                    console.error(error);
                });
        },
        changeAgentPerson() {
            //編輯代理人id
            axios
                .put("/api/demand/user/agent/person", {
                    id: this.agent,
                })
                .then((response) => {
                    location.reload();
                })
                .catch((error) => {
                    console.error(error);
                });
        },
        getEmployeesDropDown() {
            if (this.getEmployees.length == 0) {
                axios
                    .get("/api/demand/employees")
                    .then((response) => {
                        this.getEmployees = response.data ?? [];
                    })
                    .catch((error) => {
                        console.error(error);
                    });
            }
        },
        edit() {
            axios
                .put("/api/demand/agent", {
                    id: this.agent,
                })
                .then((response) => {
                    location.reload();
                })
                .catch((error) => {
                    console.error(error);
                });
        },
    },
    computed: {
        isActive() {
            return (tabIndex) => {
                return this.$root.currentTab == tabIndex ? "active" : null;
            };
        },
    },
};
</script>
<style>
.banner-list li:hover {
    font-weight: 700;
    border-bottom: 4px solid rgb(79, 70, 229);
}
.banner-list li.active {
    opacity: 100;
    font-weight: 700;
    border-bottom: 4px solid rgb(79, 70, 229);
}
.speech-bubble {
    width: 1000px;
}
.speech-bubble:after {
    content: "";
    position: absolute;
    top: 0;
    left: 50%;
    width: 0;
    height: 0;
    border: 20px solid transparent;
    border-bottom-color: #fff;
    border-top: 0;
    margin-left: 416px;
    margin-top: -20px;
}
.speech-bubble2 {
    width: 460px;
}
.speech-bubble2:after {
    content: "";
    position: absolute;
    top: 0;
    left: 50%;
    width: 0;
    height: 0;
    border: 20px solid transparent;
    border-bottom-color: #fff;
    border-top: 0;
    margin-left: 186px;
    margin-top: -20px;
}
.heighDialog .p-dialog-content {
    height: 28rem;
}
@media screen and (max-width: 1024px) {
    .speech-bubble {
        width: 760px;
    }
    .speech-bubble:after {
        margin-left: 292px;
    }
    .speech-bubble2:after {
        margin-left: 183px;
    }
}
@media screen and (max-width: 768px) {
    .speech-bubble {
        width: 300px;
    }
    .speech-bubble2 {
        width: 300px;
    }
    .speech-bubble:after {
        margin-left: 63px;
    }
    .speech-bubble2:after {
        margin-left: 105px;
    }
}
</style>
