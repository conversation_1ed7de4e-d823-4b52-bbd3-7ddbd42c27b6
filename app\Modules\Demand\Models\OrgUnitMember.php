<?php

namespace App\Modules\Demand\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
class OrgUnitMember extends Model
{
    use SoftDeletes;
    protected $fillable = ['employee_id','org_unit_id','payload', 'metadata'];
    protected $casts =['payload' => 'collection','metadata' => 'collection'];
    public function employee()
    {
        return $this->hasOne('App\Modules\Demand\Models\Employee','id','employee_id');
    }
    public function orgUnit()
    {
        return $this->hasOne('App\Modules\Demand\Models\OrgUnit','id','org_unit_id');
    }
}
