<?php

namespace App\Modules\Demand\Controllers;


use Illuminate\Http\Request;
use Illuminate\Support\Facades\Session;
use App\Http\Controllers\Controller;
use App\Modules\Demand\Models\ListLayout;
use App\Modules\Demand\Services\ListLayoutService;


class ListLayoutController extends Controller
{
    protected $listLayoutService;
    public function __construct(){
        $this->listLayoutService = $listLayoutService;
    }

    public function createList(Request $request)
    {
        return $this->ListLayoutService->createList($request);
    }

    public function updateList(Request $request)
    {
       return $this->ListLayoutService->updateList($request);
    }
}
