<?php

namespace App\Modules\Demand\Middleware;


use Closure;
use Illuminate\Support\Facades\Session;
use App\Modules\Demand\Models\Employee;
use App\Modules\Demand\Models\Company;
use App\Modules\Demand\Models\FuncAuth;

class CheckAuth
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle($request, Closure $next, $name)
    {
        //判斷是否有登入
        if (!in_array($name, Session::get('authLevel'))) {
            return redirect('/');
        }
        return $next($request);
    }
}
