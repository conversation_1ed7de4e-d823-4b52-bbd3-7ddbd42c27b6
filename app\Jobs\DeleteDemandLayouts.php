<?php

namespace App\Jobs;

use App\Modules\Demand\Models\DemandLayout;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class DeleteDemandLayouts implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        // 只針對舊版DemandLayout 是softdelete
        // 關聯不到demands
        // 的資料
        DemandLayout::onlyTrashed()
            ->wheredoesntHave('demands', function($q){
                return $q->withTrashed();
            })
            ->forceDelete();
        // ->orderBy('id')
        // ->get()
        // ->pluck('id');
    }
}
