<template>
  <div class="w-full flex items-center justify-between p-6">
    <div v-show="isSearched" class="flex justify-center items-center">
      <button
        class="mr-2 p-1 flex items-center justify-center rounded-full hover:bg-gray-200"
        @click="handleReturn"
      >
        <img src="@images/icon/back.svg" class="w-6 p-0.5" />
      </button>
      <span v-show="isSearched">返回文件庫</span>
    </div>

    <button
      v-show="!isSearched"
      class="flex mr-4 p-1 items-center justify-center xs:rounded-full xs:hover:bg-gray-200"
      @click="$emit('updateIsShowDirTree', !isShowDirTree)"
    >
      <img src="@images/icon/hamburger.svg" class="w-6 hidden xs:block" />
      <img
        src="@images/icon/hamburger-disable.svg"
        class="p-0.5 w-6 xs:hidden"
      />
    </button>

    <button
      v-show="!isSearched"
      :disabled="!prevPathIds.length && isSearched"
      :class="prevPathIds.length ? 'hover:bg-gray-200' : 'cursor-default'"
      class="mr-4 p-1 flex items-center justify-center rounded-full"
      @click="handlePrevPage"
    >
      <img
        v-if="prevPathIds.length"
        src="@images/icon/back.svg"
        class="w-6 p-0.5"
      />
      <img
        v-else
        src="@images/icon/back_disable.svg"
        class="w-6 p-0.5"
      />
    </button>

    <button
      v-show="!isSearched"
      :disabled="!nextPathIds.length"
      :class="nextPathIds.length ? 'hover:bg-gray-200' : 'cursor-default'"
      class="p-1 flex items-center justify-center rounded-full"
      @click="handleNextPage"
    >
      <img
        v-if="nextPathIds.length"
        src="@images/icon/next.svg"
        class="w-6 p-0.5"
      />
      <img
        v-else
        src="@images/icon/next_disable.svg"
        class="w-6 p-0.5"
      />
    </button>

    <!-- Breadcrumb -->
    <div
      v-show="!isSearched"
      style="background-color: #f9fafb"
      class="flex-grow h-7 rounded-full ml-6 px-3 py-1 flex items-center text-xs"
    >
      <div class="w-3.5 flex items-center" @click="enterFolder(rootId)">
        <img
          class="w-full cursor-pointer"
          src="@images/icon/folder.png"
          alt="folder"
        />
      </div>
      <div
        v-for="(path, index) of currentPathNames"
        :key="index"
        class="items-center hidden md:flex text-explorerPrimary"
      >
        <i class="pi pi-angle-right p-2"></i>
        <div
          @click="enterFolder(currentPathIds[index + 1])"
          class="cursor-pointer"
        >
          {{ path }}
        </div>
      </div>
    </div>

    <div class="cursor-pointer ml-6 w-7 h-7 p-1 rounded-full hover:bg-gray-200">
      <img
        src="@images/icon/grid.svg"
        alt="gird"
        v-if="layout === 'grid' && !isThumbnail"
        class="w-full"
        @click="updateLayout('list')"
      />
      <img
        src="@images/icon/image.svg"
        alt="image"
        v-if="layout === 'grid' && isThumbnail"
        class="w-full"
        @click="updateIsThumbnail(false)"
      />
      <img
        src="@images/icon/list.svg"
        alt="list"
        v-if="layout === 'list'"
        class="w-full"
        @click="
          updateLayout('grid');
          updateIsThumbnail(true);
        "
      />
    </div>
  </div>
</template>

<script>
export default {
  name: "Navbar",
  props: {
    rootId: { type: Number, required: true },
    isSearched: { type: Boolean, required: true },
    isShowDirTree: { type: Boolean, required: true },
    prevPathIds: { type: Array, required: true },
    nextPathIds: { type: Array, required: true },
    currentPathIds: { type: Array, required: true },
    currentPathNames: { type: Array, required: true },
    layout: { type: String, required: true },
    isThumbnail: { type: Boolean, required: true },
  },
  inject: [
    "fetchDataByFolderId",
    "checkFolderPermission",
    "fetchDirectory",
    "dataInit",
    "updateIsThumbnail",
    "updateLayout",
    "updatePrevPathIds",
  ],
  methods: {
    async handlePrevPage() {
      const id = this.prevPathIds.at(-1);
      if (!this.prevPathIds.length || !this.checkFolderPermission(id)) {
        return;
      }
      this.nextPathIds.push(this.currentPathIds.at(-1));
      await this.fetchDataByFolderId(id);
      this.prevPathIds.pop();
    },
    async handleNextPage() {
      const id = this.nextPathIds.at(-1);
      if (!this.nextPathIds.length || !this.checkFolderPermission(id)) {
        return;
      }
      this.updatePrevPathIds(id);
      await this.fetchDataByFolderId(id);
      this.nextPathIds.pop();
    },
    async enterFolder(id) {
      if (!this.checkFolderPermission(id)) return;
      if (id !== this.currentPathIds.at(-1)) this.nextPathIds.length = 0;
      await this.fetchDirectory();
      this.updatePrevPathIds(id);
      await this.fetchDataByFolderId(id);
    },
    async handleReturn() {
      await this.dataInit();
      this.updateIsThumbnail(false);
    },
  },
};
</script>
