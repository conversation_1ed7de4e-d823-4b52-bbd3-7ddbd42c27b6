<?php

namespace App\Modules\Demand\Controllers;

use App\Http\Controllers\Controller;
use App\Modules\Demand\Models\Demand;
use App\Modules\Demand\Models\DemandLayout;
use App\Modules\Demand\Models\DemandLog;
use App\Modules\Demand\Models\ImportDemand;
use App\Modules\Demand\Services\ImportDemandService;
use Illuminate\Http\Request;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use stdClass;

class ImportDemandController extends Controller
{
    //
    public function CreateOrUpdate(Request $request)
    {
        return $this->importDemandService->createOrUpdate($request);
    }

    public function fetch(Request $request)
    {
        $result = ImportDemand::where('layout_id', $request->layout_id)->first();
        return $result ? $result->list_import : [];
    }
    public function fetchDropdown(Request $request)
    {
        $data = ImportDemand::where('layout_id', $request->layout_id)->first();
        $data ? $result = $data->list_import->map(function ($item, $key) {
            return ['id' => $item['id'], 'name' => $item['name']];
        }) : $result = [];
        return $result;
    }
    public function fetchValue(Request $request)
    {
        $demand_no = $request->demand_no;
        $layout_id = $request->layout_id;
        $demand = Demand::with('customList')->withTrashed()->where('no', $demand_no)->first();
        if ($demand === null) {
            $demand = DemandLog::where('no', $demand_no)->first();
        }
        if ($demand) {
            $importDemand = ImportDemand::where('layout_id', $layout_id)->first();
            if (!$importDemand || !$demand) {
                return 0;
            }
            // 找到原始layout_id
            $original_id = $demand->payload->get('layout_original_id');
            // 目前最新layout_id
            $import_layout_id = DemandLayout::latestVersion($original_id);
            $list_imports = $importDemand ? $importDemand->list_import : [];
            // layout_id 帶入
            $list_import = $list_imports->where('id', $import_layout_id)->first();
            $models = $list_import['models'];
            $forms = $demand->payload['forms'];
            $customList = $demand->customList;
            $result = [];
            $roles = new stdClass();
            foreach ($demand->payload['sign_roles'] as $role) {
                $key = $role['self_name'];
                $value = $role['role_id'];
                $roles->$key = $value;
            }
            foreach ($forms as $formIndex => $form) {
                $formvalue = [];
                foreach ($form['columns'] as $column) {
                    foreach ($models as $model) {
                        if ($model['col']['name'] == $column['name']) {
                            if ($column['type'] == 'customList') {
                                foreach ($customList as $list) {
                                    if ($list['form_index'] == $formIndex && $form['columns'][$list['column_index']]['name'] == $model['col']['name']) {
                                        array_push($formvalue, [
                                            'name' => $model['col']['name'],
                                            'id' => $model['demand_col_id'],
                                            'custom_list' => $list['list'],
                                        ]);
                                    }
                                }
                            } else {
                                array_push($formvalue, [
                                    'name' => $model['col']['name'],
                                    'id' => $model['demand_col_id'],
                                    'value' => isset($column['value']) ? $column['value'] : '',
                                ]);
                            }
                        }
                        // if ($column['id'] == $model['col_id']) {
                        //     if($column['type'] == 'customList') {
                        //         foreach($customList as $list) {
                        //             if($list['form_index'] == $formIndex && explode("-", $list['payload']['form_setting'][0]['id'])[0] == $model['col_id']) {
                        //                 array_push($formvalue, [
                        //                     'id' => $model['demand_col_id'],
                        //                     'custom_list' =>$list['list'],
                        //                 ]);
                        //             }
                        //         }
                        //     } else {
                        //         array_push($formvalue, [
                        //             'id' => $model['demand_col_id'],
                        //             'value' => isset($column['value'])?$column['value']:'',
                        //         ]);
                        //     }
                        // }
                    }
                }
                if (count($formvalue) > 0) {
                    array_push($result, $formvalue);
                }
            }
            $res = [
                'forms' => $result,
                'roles' => $roles
            ];
            return $res;
        }
        return 0;
    }
}
