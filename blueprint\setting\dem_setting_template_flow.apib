# Group Demnad_Setting_Template
需求單匯出模板設定

## import existing Template [/api/demand/setting/layouts]
導入先前模板

### import existing Template [GET]
導入先前模板

    + Request (object)
        + Attributes (object)
            + id: "1229" (string) - demand_layouts id

    + Response (object)
        + Attributes (object)
            + file (object, optional)
                + excel (object, optional)
                    + url: "https://example.com/uploads/1692849072_b2249e6b7bccfcf5edb73dde28ad3ceb.xlsx" (string) - 上次儲存的模板路徑(供使用者點擊下載).
                    + base_name: "1692849072_b2249e6b7bccfcf5edb73dde28ad3ceb.xlsx" (string) - The base name of the uploaded file.
                    + name: "original_name.xlsx" (string) - The name of the uploaded file.
                + word (object, optional)
                    + url: "https://example.com/uploads/1692849072_b2249e6b7bccfcf5edb73dde28ad3ceb.xlsx" (string) - 上次儲存的模板路徑(供使用者點擊下載).
                    + base_name: "1692849072_b2249e6b7bccfcf5edb73dde28ad3ceb.xlsx" (string) - The base name of the uploaded file.
                    + name: "original_name.xlsx" (string) - The name of the uploaded file.

## Upload Template [/api/demand/upload/templete]
上傳匯出模版 (增+改)

### Upload a New Template [POST]
上傳匯出模板 (增+改)

    + Request (multipart/form-data)

        + Attributes 
            + file: (file) The file to upload.
            + fileName: (string) file name

    + Response 200 (application/json)

        + Attributes (object)
            + url: "https://example.com/uploads/1692849072_b2249e6b7bccfcf5edb73dde28ad3ceb.xlsx" (string) - 模板暫存路徑(供使用者點擊下載).
            + base_name: "1692849072_b2249e6b7bccfcf5edb73dde28ad3ceb.xlsx" (string) - The base name of the uploaded file.
            + name: "original_name.xlsx" (string) - The name of the uploaded file.

## Delete Template [/api/demand/delete/temp]
刪除已匯入之匯出模板 (刪)

### delete uploaded Template [POST]
刪除已匯入之匯出模板 (刪)

    + Request (application/json)

        + Attributes (object)
            + base_name: "1692849072_b2249e6b7bccfcf5edb73dde28ad3ceb.xlsx" (string) -  The base name of the uploaded file.

    + Response 200 (application/json)

       + Attributes (object)
            + state : 1 or 0 (number) - 成功或失敗
            + error (string) 失敗原因 - 失敗原因

## Save Template [/api/demand/setting/layouts]
儲存模板至資料庫 

### Save Template [PUT]
儲存模板至資料庫

    + Request (application/json)
        
        + Attributes (object)
            + file (object) 
                + excel (object, optional)
                    + url: "https://example.com/uploads/1692849072_b2249e6b7bccfcf5edb73dde28ad3ceb.xlsx" (string) - 模板儲存路徑(供使用者點擊下載).
                    + base_name: "1692849072_b2249e6b7bccfcf5edb73dde28ad3ceb.xlsx" (string) - The base name of the uploaded file.
                    + name: "original_name.xlsx" (string) - The name of the uploaded file.
                + word (object, optional)
                    + url: "https://example.com/uploads/1692849072_b2249e6b7bccfcf5edb73dde28ad3ceb.xlsx" (string) - 模板儲存路徑(供使用者點擊下載).
                    + base_name: "1692849072_b2249e6b7bccfcf5edb73dde28ad3ceb.xlsx" (string) - The base name of the uploaded file.
                    + name: "original_name.xlsx" (string) - The name of the uploaded file.

    + Response 200 (application/json)
     
        + Attributes (object)
            + state : 1 or 0 (number) - 成功或失敗
            + error (string) 失敗原因 - 失敗原因

## Export with Template [/api/demand/word]
需求單套用模板匯出

### Export with Template [GET]
需求單套用模板匯出

    + Request
    
        + Attributes 
            + id: "334" (number) - demand_logs id
            + type "excel" or "word" (string) - 匯出模板類型

    + Response 200 (application/pdf)
        + If response could be a PDF
        + Body
            (PDF file content here)

    + Response 200 (application/octet-stream)
        + If response could be an Excel file
        + Body
            (Excel file content here)

    + Response 204
        + If there is no content to return

