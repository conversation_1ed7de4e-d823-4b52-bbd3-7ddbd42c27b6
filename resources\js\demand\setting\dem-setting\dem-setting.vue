<template>
    <div>
        <!-- 需求單列表 -->
        <div v-if="action == 0">
            <div class="text-right mb-6">
                <Button
                    @click="addGroup()"
                    label="新增類別"
                    class="w-28"
                ></Button>
            </div>
            <hr class="my-6 text-gray-200" />
            <div v-for="(item, item_index) in dataList" :key="item_index">
                <div class="flex justify-between demandListSelect pt-6 pl-6">
                    <div class="flex">
                        <InputText
                            v-if="item.edit"
                            v-model="item.name"
                            class="w-24 h-7"
                        ></InputText>
                        <p v-else class="mr-4 my-auto font-bold">
                            {{ item.name }}
                        </p>
                        <!-- <button @click="item.edit = !item.edit"> -->
                        <button @click="changeIcon(item_index)">
                            <img
                                v-if="item.edit"
                                src="@images/icon/save.png"
                                alt=""
                            />
                            <img
                                v-else
                                src="@images/icon/edit_enable.png"
                                alt=""
                            />
                        </button>
                    </div>
                    <div>
                        <!-- <button class="mr-4">
                            <img
                                src="@images/icon/add_enable.png"
                                alt=""
                            />
                        </button> -->
                        <button
                            @click="
                                del = 0;
                                delIndexGroup = item_index;
                                displayModal4 = true;
                            "
                        >
                            <img src="@images/icon/delete_enable.png" alt="" />
                        </button>
                    </div>
                </div>
                <div class="my-10 flex flex-wrap">
                    <div
                        v-for="(itemList, itemList_index) in item.layouts"
                        :key="itemList_index"
                        class="flex mr-10 lg:mr-5 xl:mr-10 pb-10"
                    >
                        <Button
                            :title="itemList.name"
                            @click="
                                getLayoutSetting(
                                    itemList.id,
                                    item_index,
                                    itemList_index
                                )
                            "
                            class=""
                            ><p class="w-36 truncate">
                                {{ itemList.name }}
                            </p></Button
                        >
                        <div
                            @click="
                                changeLayoutSwitch(item_index, itemList_index)
                            "
                        >
                            <InputSwitch
                                class="mx-4 mt-4"
                                v-model="itemList.open"
                            />
                        </div>

                        <!-- 按鈕 -->
                        <button
                            @click="
                                del = 1;
                                delIndexGroup = item_index;
                                delIndexLayout = itemList_index;
                                displayModal4 = true;
                            "
                            class="mt-4"
                        >
                            <img src="@images/icon/delete_enable.png" alt="" />
                        </button>
                    </div>
                    <div
                        @click="
                            (action = 1),
                                (settingTitle = '新增需求單'),
                                (settingId = 0),
                                (settingGroupId = item.id),
                                (settingName = ''),
                                (settingCode = ''),
                                (originalCode = '')
                        "
                        class="newSettingBtn"
                    >
                        <i class="fa-solid fa-plus"></i>
                        <span class="my-auto inline-block">新增需求單</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- 新增/修改需求單 -->
        <div v-if="action == 1">
            <span @click="previous" class="cursor-pointer">
                <i class="fas fa-arrow-left"></i>
            </span>
            <div class="text-center">
                <span class="text-xl font-semibold"> {{ settingTitle }} </span>
            </div>
            <p class="text-lg font-semibold">表單設定</p>
            <!-- 欄位設定組件 -->
            <div
                class="w-full my-8 grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 2xl:grid-cols-4"
            >
                <div
                    v-for="(item, item_index) in settingForm"
                    :key="item_index"
                    class="pb-10"
                    @dragenter="dragenter($event, item_index)"
                    @dragover="dragover($event, item_index)"
                    @dragstart="dragstart(item_index)"
                >
                    <div class="w-48">
                        <div class="flex justify-between w-full">
                            <p class="font-semibold cursor-move select-all">
                                欄位{{ item_index + 1 }}
                            </p>
                            <img
                                @click="deleteCol(item_index)"
                                class="cursor-pointer select-none"
                                src="@images/icon/delete_enable.png"
                                alt=""
                            />
                        </div>
                    </div>
                    <div class="w-48 pl-2 border-l-4 border-primary">
                        <Cascadeselect
                            class="w-44 mb-2"
                            v-model="item.selected"
                            @change="settingCol(item)"
                            :options="cascadeForm"
                            optionLabel="fname"
                            optionGroupLabel="name"
                            :optionGroupChildren="['states', 'forms']"
                            placeholder="選擇欄位類型"
                            :class="{ 'p-invalid': item.invalidType }"
                        />
                        <small
                            v-if="item.invalidType"
                            class="text-red-500 block"
                            >請選擇欄位類別</small
                        >
                        <!-- 缺少資料庫欄位判斷type -->
                        <div>
                            <InputText
                                v-if="
                                    item.type !== 'document' &&
                                    item.type !== 'database' &&
                                    isNaN(item.type)
                                "
                                @input="change = 1"
                                class="w-44 mb-2"
                                v-model="item.name"
                                placeholder="輸入標題名稱"
                                :class="{ 'p-invalid': item.invalidName }"
                            />
                            <small
                                v-if="item.invalidName"
                                class="text-red-500 block"
                                >請輸入標題名稱</small
                            >
                        </div>
                        <div>
                            <InputText
                                v-if="item.type == 'input'"
                                @input="
                                    change = 1;
                                    updateDefault($event, item);
                                "
                                class="w-44 mb-2"
                                v-model="item.value"
                                placeholder="輸入預設值(選填)"
                            />
                            <InputNumber
                                v-if="item.type == 'money'"
                                @input="
                                    change = 1;
                                    updateDefault($event, item);
                                "
                                mode="currency"
                                currency="USD"
                                locale="en-US"
                                inputClass="w-44"
                                v-model="item.value"
                                placeholder="輸入預設值(選填)"
                            />
                            <InputNumber
                                v-if="item.type == 'number'"
                                @input="
                                    change = 1;
                                    updateDefault($event, item);
                                "
                                inputClass="w-44"
                                inputId="minmaxfraction"
                                :maxFractionDigits="4"
                                v-model="item.value"
                                placeholder="輸入預設值(選填)"
                            />
                        </div>
                        <div
                            v-if="
                                item.type == 'single' ||
                                item.type == 'multi' ||
                                item.type == 'dropdown' ||
                                item.type == 'cascade'
                            "
                        >
                            <div
                                v-for="(option, option_index) in item.options"
                                :key="option_index"
                                class="relative"
                            >
                                <!-- 內容 -->
                                <div
                                    class="w-56 p-inputgroup"
                                    :class="{
                                        'border border-red-300 rounded-md':
                                            option.invalid,
                                    }"
                                    draggable="true"
                                    @dragstart="
                                        dragOptionStart(
                                            $event,
                                            item_index,
                                            option_index
                                        )
                                    "
                                    @dragenter="
                                        dragOptionEnter(
                                            $event,
                                            item_index,
                                            option_index
                                        )
                                    "
                                    @dragend="dragOptionEnd($event)"
                                >
                                    <span
                                        style="
                                            background: white;
                                            border-bottom-left-radius: 0;
                                            min-width: 2rem;
                                        "
                                        class="p-inputgroup-addon cursor-grab active:cursor-grabbing"
                                    >
                                        {{ option_index + 1 }}
                                    </span>
                                    <!-- 'border-r-0 rounded-r' 切換InputText樣式 -->
                                    <InputText
                                        class="mb-2 rounded-r border-l-0"
                                        @input="
                                            change = 1;
                                            if (
                                                item.type == 'cascade' &&
                                                'states' in
                                                    item.options[
                                                        option_index
                                                    ] &&
                                                item.options[option_index]
                                                    .states.length > 0
                                            ) {
                                                item.options[
                                                    option_index
                                                ].gname =
                                                    item.options[
                                                        option_index
                                                    ].name;
                                                $delete(
                                                    item.options[option_index],
                                                    'iname'
                                                );
                                            } else if (item.type == 'cascade') {
                                                item.options[
                                                    option_index
                                                ].iname =
                                                    item.options[
                                                        option_index
                                                    ].name;
                                                $delete(
                                                    item.options[option_index],
                                                    'gname'
                                                );
                                            }
                                        "
                                        :class="
                                            item.options.length > 1
                                                ? 'border-r-0'
                                                : 'border-r'
                                        "
                                        v-model="
                                            item.options[option_index].name
                                        "
                                        placeholder="輸入選項名稱"
                                        :disabled="
                                            item.other == 1 &&
                                            item.options[option_index].name ==
                                                '其他'
                                        "
                                    />
                                    <span
                                        v-if="item.options.length > 1"
                                        @click="
                                            deleteSelect(item, option_index)
                                        "
                                        style="
                                            background: white;
                                            min-width: 2rem;
                                        "
                                        class="p-inputgroup-addon cursor-pointer"
                                    >
                                        <img
                                            src="@images/icon/delete_enable.png"
                                            alt=""
                                        />
                                    </span>
                                </div>
                                <!-- 選項說明 -->
                                <p
                                    v-if="
                                        (item.type == 'dropdown' &&
                                            (item.mul == 0 ||
                                                !('mul' in item))) ||
                                        item.type == 'cascade'
                                    "
                                    class="absolute top-3 -right-28 underline cursor-pointer"
                                    @click="
                                        displayModal3 = true;
                                        columnIndex = item_index;
                                        optionIndex = option_index;
                                        optionCommit = option.prompt;
                                    "
                                >
                                    選項說明
                                </p>
                                <!-- 子層選項 -->
                                <p
                                    v-if="item.type == 'cascade'"
                                    class="absolute top-3 -right-48 underline cursor-pointer"
                                    @click="
                                        displayModal5 = true;
                                        columnIndex = item_index;
                                        optionIndex = option_index;
                                    "
                                >
                                    子層選項
                                </p>
                                <!-- 預設選項 -->
                                <div
                                    v-if="
                                        item.type == 'single' ||
                                        (item.type == 'dropdown' &&
                                            (!('mul' in item) || item.mul == 0))
                                    "
                                    class="absolute top-3"
                                    :class="
                                        item.type == 'single'
                                            ? '-right-28'
                                            : '-right-44'
                                    "
                                    @dblclick="$delete(item, 'value')"
                                >
                                    <RadioButton
                                        class="mb-1 mr-1"
                                        v-model="item.value"
                                        :value="option"
                                        @input="
                                            change = 1;
                                            updateDefault($event, item);
                                        "
                                        :id="item.id + '-' + option.id"
                                    />
                                    <label
                                        :for="item.id + '-' + option.id"
                                        class="cursor-pointer"
                                        >預設</label
                                    >
                                </div>
                            </div>
                            <!-- 添加選項 & 其他 ... -->
                            <div
                                style="
                                    border-top-left-radius: 0;
                                    border-top-right-radius: 0;
                                "
                                class="w-56 flex justify-between p-inputtext p-component"
                            >
                                <div
                                    @click="addSelect(1, item)"
                                    class="cursor-pointer"
                                >
                                    <i class="fa-solid fa-plus"></i>
                                    <span>添加選項</span>
                                </div>
                                <button
                                    v-if="
                                        item.type !== 'dropdown' &&
                                        item.type !== 'cascade'
                                    "
                                    @click="addSelect(0, item)"
                                    :disabled="item.other == 1"
                                    :class="
                                        item.other == 1
                                            ? 'text-gray-200 cursor-auto'
                                            : 'cursor-pointer'
                                    "
                                >
                                    <i class="fa-solid fa-plus"></i>
                                    <span>添加其他</span>
                                </button>
                                <button
                                    v-else-if="item.type == 'dropdown'"
                                    @click="
                                        !('mul' in item) || item.mul == 0
                                            ? $set(item, 'mul', 1)
                                            : (item.mul = 0)
                                    "
                                >
                                    <i class="fa-solid fa-arrows-rotate"></i>
                                    <span
                                        v-if="item.mul == 0 || !('mul' in item)"
                                        >單選題目</span
                                    >
                                    <span v-else>複選題目</span>
                                </button>
                            </div>
                        </div>
                        <div v-if="item.type == 'date'">
                            <Cascadeselect
                                class="w-44 mb-2"
                                v-model="item.date.selected"
                                :options="dateSelect"
                                optionLabel="dname"
                                optionValue="value"
                                optionGroupLabel="name"
                                :optionGroupChildren="['dates']"
                                placeholder="可選所有日期"
                                @change="selectDate(item)"
                                @input="change = 1"
                            />
                            <InputNumber
                                v-if="item.date.selected == 'auto'"
                                :min="0"
                                :max="30"
                                :step="1"
                                suffix="天後"
                                showButtons
                                inputClass="w-32"
                                v-model="item.date.day"
                            />
                        </div>
                        <div v-if="item.type == 'total'">
                            <Dropdown
                                class="w-44 mb-2"
                                :options="
                                    settingForm.filter(
                                        (form) =>
                                            (form.type == 'money' ||
                                                form.type == 'number' ||
                                                form.type == 'total') &&
                                            form.id !==
                                                (item.cols.col2 !== null
                                                    ? item.cols.col2.id
                                                    : item.cols.col2) &&
                                            form.id !== item.id
                                    )
                                "
                                optionLabel="name"
                                @change="
                                    item.cols.col1 == item.cols.col2
                                        ? (item.cols.col1 = null)
                                        : item.cols.col1
                                "
                                v-model="item.cols.col1"
                            />
                            <Dropdown
                                class="w-44 mb-2"
                                :options="
                                    settingForm.filter(
                                        (form) =>
                                            (form.type == 'money' ||
                                                form.type == 'number' ||
                                                form.type == 'total') &&
                                            form.id !==
                                                (item.cols.col1 !== null
                                                    ? item.cols.col1.id
                                                    : item.cols.col1) &&
                                            form.id !== item.id
                                    )
                                "
                                optionLabel="name"
                                @change="
                                    item.cols.col2 == item.cols.col1
                                        ? (item.cols.col2 = null)
                                        : item.cols.col2
                                "
                                v-model="item.cols.col2"
                            />
                            <Dropdown
                                class="w-44 mb-2"
                                v-model="item.compute"
                                :options="computeOptions"
                                optionLabel="name"
                                optionValue="value"
                            />
                            <Dropdown
                                class="w-44"
                                v-model="item.parseInt"
                                :options="[
                                    {
                                        value: 1,
                                        name: '取整數',
                                    },
                                    {
                                        value: 0,
                                        name: '取小數點後兩位',
                                    },
                                ]"
                                optionLabel="name"
                                optionValue="value"
                            />
                        </div>
                        <div class="flex justify-between">
                            <div
                                v-if="
                                    item.type !== '' &&
                                    item.type !== 'list' &&
                                    item.type !== 'customList' &&
                                    item.type !== 'total'
                                "
                            >
                                <Checkbox
                                    class="mb-1"
                                    v-model="item.must"
                                    :binary="true"
                                    @click="change = 1"
                                />
                                <label for="binary">必填</label>
                            </div>
                            <div
                                v-if="
                                    item.type == 'money' ||
                                    item.type == 'input' ||
                                    item.type == 'number' ||
                                    item.type == 'dropdown' ||
                                    item.type == 'time' ||
                                    item.type == 'date'
                                "
                                class="field-checkbox"
                            >
                                <Checkbox
                                    class="mb-1"
                                    v-model="item.audit_must"
                                    :binary="true"
                                    @click="change = 1"
                                />
                                <label for="binary1">審核人填寫</label>
                            </div>
                            <div
                                @click="openList(item, item_index)"
                                v-if="
                                    item.type == 'list' ||
                                    item.type == 'customList'
                                "
                            >
                                <p
                                    v-if="item.type == 'list'"
                                    class="underline cursor-pointer"
                                >
                                    編輯項目清單
                                </p>
                                <p
                                    v-if="item.type == 'customList'"
                                    class="underline cursor-pointer"
                                >
                                    編輯自訂表單
                                </p>
                            </div>
                            <div
                                v-if="item.type == 'total'"
                                class="field-checkbox"
                            >
                                <Checkbox
                                    class="mb-1"
                                    v-model="item.displayTotalRow"
                                    :binary="true"
                                    @click="change = 1"
                                />
                                <label for="binary1">顯示總計</label>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="mr-36 lg:mr-24 xl:mr-36 2xl:mr-64">
                    <p class="font-semibold">
                        欄位{{ settingForm.length + 1 }}
                    </p>
                    <div @click="addCol()" class="newSettingBtn">
                        <i class="fa-solid fa-plus"></i>
                        <span class="my-auto inline-block">新增欄位</span>
                    </div>
                </div>
            </div>
            <hr class="mb-6 text-gray-200" />
            <p class="text-lg font-semibold">其他設定</p>
            <div class="w-1/2 my-8">
                <div class="block md:flex justify-between my-8">
                    <span class="my-auto"> 需求單名稱 </span>
                    <div>
                        <InputText
                            v-model="settingName"
                            placeholder="輸入需求單名稱"
                            class="w-64"
                            :class="{ 'p-invalid': invalidSettingName }"
                            @input="
                                () => {
                                    change = 1;
                                    invalidSettingName = false;
                                }
                            "
                        />
                        <small
                            v-if="invalidSettingName"
                            class="text-red-500 block"
                            >請輸入需求單名稱</small
                        >
                    </div>
                </div>
                <div class="block md:flex justify-between my-8">
                    <span class="my-auto relative">
                        需求單編號 <br />
                        <span class="text-sm text-gray-400"
                            >限英文A-Z、數字0-9兩字元</span
                        >
                    </span>
                    <div>
                        <InputText
                            v-model="settingCode"
                            placeholder="輸入需求單編號"
                            maxlength="2"
                            class="w-64"
                            :class="{ 'p-invalid': invalidSettingCode }"
                            @input="change = 1"
                        />
                        <small
                            v-if="invalidSettingCode"
                            class="text-red-500 block"
                            >{{ invalidSettingText }}</small
                        >
                    </div>
                </div>
                <div class="block md:flex justify-between my-8">
                    <span class="my-auto relative">
                        需求單欄位排序
                        <span
                            class="absolute left-0 -bottom-5 text-sm text-gray-400"
                            >數字越小越前面</span
                        >
                    </span>
                    <div>
                        <InputNumber
                            :inputClass="'w-52'"
                            v-model="settingSort"
                            placeholder="輸入順序"
                            showButtons
                            :min="0"
                            :max="999"
                            @input="change = 1"
                        />
                    </div>
                </div>
                <div class="block md:flex justify-between my-8">
                    <span class="my-auto">
                        摘要顯示資訊 <br />
                        <span class="text-sm text-gray-400"
                            >於進度、審核、紀錄頁面摘要顯示該欄位資訊</span
                        >
                    </span>
                    <div>
                        <Dropdown
                            class="w-64"
                            v-model="summaryColId"
                            :options="
                                settingForm.filter((form) => {
                                    return (
                                        form.type !== 'list' &&
                                        form.type !== 'customList' &&
                                        form.type !== 'document'
                                    );
                                })
                            "
                            optionLabel="name"
                            optionValue="id"
                        />
                    </div>
                </div>
                <div class="block md:flex justify-between my-8">
                    <span class="my-auto">是否可多筆申請</span>
                    <div class="radio flex justify-between w-64 px-1">
                        <div class="p-field-radiobutton relative">
                            <RadioButton
                                id="yes"
                                class="mb-1"
                                name="settingRequest"
                                :value="true"
                                v-model="settingRequest"
                            />
                            <label for="settingRequest">是</label>
                        </div>
                        <div class="p-field-radiobutton">
                            <RadioButton
                                id="no"
                                class="mb-1"
                                name="settingRequest"
                                :value="false"
                                v-model="settingRequest"
                                @change="change = 1"
                            />
                            <label for="settingRequest">否</label>
                        </div>
                    </div>
                </div>
                <div class="block md:flex justify-between my-8">
                    <span class="my-auto">是否自動結案</span>
                    <div class="radio flex justify-between w-64 px-1">
                        <div class="p-field-radiobutton relative">
                            <RadioButton
                                id="yes"
                                class="mb-1"
                                name="settingOver"
                                :value="true"
                                v-model="settingOver"
                                @change="change = 1"
                            />
                            <label for="settingOver">是</label>
                        </div>
                        <div class="p-field-radiobutton">
                            <RadioButton
                                id="no"
                                class="mb-1"
                                name="settingOver"
                                :value="false"
                                v-model="settingOver"
                                @change="change = 1"
                            />
                            <label for="settingOver">否</label>
                        </div>
                    </div>
                </div>
                <div class="block md:flex justify-between my-8">
                    <span class="my-auto">是否可帶入其他需求單</span>
                    <div class="radio flex justify-between w-64 relative px-1">
                        <div class="p-field-radiobutton relative">
                            <RadioButton
                                id="yes"
                                class="mb-1"
                                name="settingImport"
                                :value="true"
                                v-model="settingImport"
                                @change="change = 1"
                            />
                            <label for="settingImport">是</label>
                        </div>
                        <div class="p-field-radiobutton">
                            <RadioButton
                                id="no"
                                class="mb-1"
                                name="settingImport"
                                :value="false"
                                v-model="settingImport"
                                @change="change = 1"
                            />
                            <label for="settingImport">否</label>
                        </div>
                        <Button
                            @click="importList()"
                            label="欄位帶入設定"
                            class="p-button-outlined p-button-secondary absolute -right-44 -bottom-2.5"
                        />
                    </div>
                </div>
                <!--  -->
                <div class="block md:flex justify-between my-8">
                    <span
                        >需求單匯出格式(Excel)
                        <p class="text-sm text-gray-400">
                            檔案大小10MB以內，xlx、xlsx檔
                        </p>
                    </span>

                    <div class="w-64">
                        <Button
                            @click="$refs.fileTemplete_excel.click()"
                            label="上傳檔案"
                            class="p-button-outlined p-button-secondary"
                        />
                        <input
                            id="excel"
                            type="file"
                            class="hidden"
                            ref="fileTemplete_excel"
                            accept=".xls,.xlsx"
                            @change="uploadFile($event, 'excel')"
                        />
                        <div class="overflow-auto block lg:inline-block">
                            <div
                                v-if="file ? file.excel != null : false"
                                class="inline-block"
                            >
                                <span class="flex gap-1 mx-2">
                                    <div class="w-20 truncate">
                                        <a
                                            :href="file.excel.URL"
                                            :title="file.excel.name"
                                            target="_blank"
                                            rel="noreferrer noopenner"
                                            >{{ file.excel.name }}</a
                                        >
                                    </div>
                                    <button
                                        @click="deleteTemp('excel')"
                                        title="刪除附件"
                                        class="group"
                                    >
                                        <svg
                                            width="16"
                                            height="16"
                                            viewBox="0 0 16 16"
                                            fill="none"
                                            xmlns="http://www.w3.org/2000/svg"
                                        >
                                            <path
                                                class="group-hover:fill-[#030405]"
                                                d="M8.9898 8L10.9701 6.0204C11.0351 5.95536 11.0867 5.87815 11.1219 5.79318C11.1571 5.7082 11.1752 5.61713 11.1752 5.52515C11.1752 5.43317 11.1571 5.3421 11.1219 5.25712C11.0867 5.17215 11.0351 5.09494 10.9701 5.0299C10.9051 4.96486 10.8279 4.91327 10.7429 4.87807C10.6579 4.84288 10.5668 4.82476 10.4748 4.82476C10.3829 4.82476 10.2918 4.84288 10.2068 4.87807C10.1218 4.91327 10.0446 4.96486 9.9796 5.0299L8 7.0102L6.0204 5.0299C5.88905 4.89855 5.71091 4.82476 5.52515 4.82476C5.3394 4.82476 5.16125 4.89855 5.0299 5.0299C4.89855 5.16125 4.82476 5.3394 4.82476 5.52515C4.82476 5.61713 4.84288 5.7082 4.87807 5.79318C4.91327 5.87815 4.96486 5.95536 5.0299 6.0204L7.0102 8L5.0299 9.9796C4.89855 10.1109 4.82476 10.2891 4.82476 10.4748C4.82476 10.6606 4.89855 10.8388 5.0299 10.9701C5.16125 11.1014 5.3394 11.1752 5.52515 11.1752C5.71091 11.1752 5.88905 11.1014 6.0204 10.9701L8 8.9898L9.9796 10.9701C10.1109 11.1014 10.2891 11.1752 10.4748 11.1752C10.6606 11.1752 10.8388 11.1014 10.9701 10.9701C11.1014 10.8388 11.1752 10.6606 11.1752 10.4748C11.1752 10.2891 11.1014 10.1109 10.9701 9.9796L8.9898 8ZM8 15C4.1339 15 1 11.8661 1 8C1 4.1339 4.1339 1 8 1C11.8661 1 15 4.1339 15 8C15 11.8661 11.8661 15 8 15Z"
                                                fill="#314560"
                                            />
                                        </svg>
                                    </button>
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="block md:flex justify-between my-8">
                    <span
                        >需求單匯出格式(PDF)
                        <p class="text-sm text-gray-400">
                            檔案大小10MB以內，docx檔
                        </p>
                    </span>

                    <div class="w-64">
                        <Button
                            @click="$refs.fileTemplete_word.click()"
                            label="上傳檔案"
                            class="p-button-outlined p-button-secondary"
                        />
                        <input
                            id="word"
                            type="file"
                            class="hidden"
                            ref="fileTemplete_word"
                            accept=".docx"
                            @change="uploadFile($event, 'word')"
                        />
                        <div class="overflow-auto block lg:inline-block">
                            <div
                                v-if="file ? file.word != null : false"
                                class="inline-block"
                            >
                                <span class="flex gap-1 mx-2">
                                    <div class="w-20 truncate">
                                        <a
                                            :href="file.word.URL"
                                            :title="file.word.name"
                                            target="_blank"
                                            >{{ file.word.name }}</a
                                        >
                                    </div>
                                    <button
                                        @click="deleteTemp('word')"
                                        title="刪除附件"
                                        class="group"
                                    >
                                        <svg
                                            width="16"
                                            height="16"
                                            viewBox="0 0 16 16"
                                            fill="none"
                                            xmlns="http://www.w3.org/2000/svg"
                                        >
                                            <path
                                                class="group-hover:fill-[#030405]"
                                                d="M8.9898 8L10.9701 6.0204C11.0351 5.95536 11.0867 5.87815 11.1219 5.79318C11.1571 5.7082 11.1752 5.61713 11.1752 5.52515C11.1752 5.43317 11.1571 5.3421 11.1219 5.25712C11.0867 5.17215 11.0351 5.09494 10.9701 5.0299C10.9051 4.96486 10.8279 4.91327 10.7429 4.87807C10.6579 4.84288 10.5668 4.82476 10.4748 4.82476C10.3829 4.82476 10.2918 4.84288 10.2068 4.87807C10.1218 4.91327 10.0446 4.96486 9.9796 5.0299L8 7.0102L6.0204 5.0299C5.88905 4.89855 5.71091 4.82476 5.52515 4.82476C5.3394 4.82476 5.16125 4.89855 5.0299 5.0299C4.89855 5.16125 4.82476 5.3394 4.82476 5.52515C4.82476 5.61713 4.84288 5.7082 4.87807 5.79318C4.91327 5.87815 4.96486 5.95536 5.0299 6.0204L7.0102 8L5.0299 9.9796C4.89855 10.1109 4.82476 10.2891 4.82476 10.4748C4.82476 10.6606 4.89855 10.8388 5.0299 10.9701C5.16125 11.1014 5.3394 11.1752 5.52515 11.1752C5.71091 11.1752 5.88905 11.1014 6.0204 10.9701L8 8.9898L9.9796 10.9701C10.1109 11.1014 10.2891 11.1752 10.4748 11.1752C10.6606 11.1752 10.8388 11.1014 10.9701 10.9701C11.1014 10.8388 11.1752 10.6606 11.1752 10.4748C11.1752 10.2891 11.1014 10.1109 10.9701 9.9796L8.9898 8ZM8 15C4.1339 15 1 11.8661 1 8C1 4.1339 4.1339 1 8 1C11.8661 1 15 4.1339 15 8C15 11.8661 11.8661 15 8 15Z"
                                                fill="#314560"
                                            />
                                        </svg>
                                    </button>
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
                <!--  -->
                <div class="block md:flex justify-between my-8">
                    <span>備註提醒</span>
                    <div class="w-64">
                        <Button
                            @click="
                                remarks.push({ recipient: '所有人', value: '' })
                            "
                            label="新增備註"
                            class="p-button-outlined p-button-secondary mb-2"
                        />
                        <div>
                            <div
                                v-for="(remark, remark_index) in remarks"
                                :key="remark_index"
                                class="relative"
                            >
                                <div class="flex gap-2 w-max">
                                    <InputText
                                        v-if="remark instanceof Object"
                                        :class="{ 'p-invalid': remark.invalid }"
                                        v-model="remark.value"
                                        @input="remark.invalid = false"
                                    />

                                    <div
                                        class="border border-gray-300 flex items-center justify-center rounded-md bg-white overflow-hidden"
                                    >
                                        <div
                                            class="w-36 py-3 pl-3 flex gap-1 items-center"
                                            :class="[
                                                remark.file?.name
                                                    ? 'text-gray-500'
                                                    : 'text-gray-400',
                                            ]"
                                        >
                                            <template v-if="remark.file">
                                                <a
                                                    :href="remark.file.URL"
                                                    :title="remark.file.name"
                                                    class="inline- truncate"
                                                    >{{ remark.file.name }}</a
                                                >
                                                <button
                                                    @click="
                                                        removeFile(remark_index)
                                                    "
                                                    class="group"
                                                    :class="{
                                                        hidden: !remark.file,
                                                    }"
                                                >
                                                    <svg
                                                        width="16"
                                                        height="16"
                                                        viewBox="0 0 16 16"
                                                        fill="none"
                                                        xmlns="http://www.w3.org/2000/svg"
                                                    >
                                                        <path
                                                            class="group-hover:fill-[#030405]"
                                                            d="M8.9898 8L10.9701 6.0204C11.0351 5.95536 11.0867 5.87815 11.1219 5.79318C11.1571 5.7082 11.1752 5.61713 11.1752 5.52515C11.1752 5.43317 11.1571 5.3421 11.1219 5.25712C11.0867 5.17215 11.0351 5.09494 10.9701 5.0299C10.9051 4.96486 10.8279 4.91327 10.7429 4.87807C10.6579 4.84288 10.5668 4.82476 10.4748 4.82476C10.3829 4.82476 10.2918 4.84288 10.2068 4.87807C10.1218 4.91327 10.0446 4.96486 9.9796 5.0299L8 7.0102L6.0204 5.0299C5.88905 4.89855 5.71091 4.82476 5.52515 4.82476C5.3394 4.82476 5.16125 4.89855 5.0299 5.0299C4.89855 5.16125 4.82476 5.3394 4.82476 5.52515C4.82476 5.61713 4.84288 5.7082 4.87807 5.79318C4.91327 5.87815 4.96486 5.95536 5.0299 6.0204L7.0102 8L5.0299 9.9796C4.89855 10.1109 4.82476 10.2891 4.82476 10.4748C4.82476 10.6606 4.89855 10.8388 5.0299 10.9701C5.16125 11.1014 5.3394 11.1752 5.52515 11.1752C5.71091 11.1752 5.88905 11.1014 6.0204 10.9701L8 8.9898L9.9796 10.9701C10.1109 11.1014 10.2891 11.1752 10.4748 11.1752C10.6606 11.1752 10.8388 11.1014 10.9701 10.9701C11.1014 10.8388 11.1752 10.6606 11.1752 10.4748C11.1752 10.2891 11.1014 10.1109 10.9701 9.9796L8.9898 8ZM8 15C4.1339 15 1 11.8661 1 8C1 4.1339 4.1339 1 8 1C11.8661 1 15 4.1339 15 8C15 11.8661 11.8661 15 8 15Z"
                                                            fill="#314560"
                                                        />
                                                    </svg>
                                                </button>
                                            </template>
                                            <span v-else class="truncate"
                                                >上傳附件</span
                                            >
                                        </div>
                                        <button
                                            class="p-3 hover:bg-[#f8f5ff]"
                                            @click.prevent="
                                                triggerUpload(remark_index)
                                            "
                                        >
                                            <img
                                                src="@images/icon/upload.png"
                                                alt="upload"
                                            />
                                        </button>
                                        <input
                                            ref="fileInputs"
                                            class="hidden"
                                            type="file"
                                            @change="
                                                remarkFileChange(
                                                    $event,
                                                    remark_index
                                                )
                                            "
                                        />
                                    </div>
                                    <button
                                        @click="remarks.splice(remark_index, 1)"
                                        class="w-6"
                                    >
                                        <img
                                            src="@images/icon/delete_enable.png"
                                            alt="delete"
                                        />
                                    </button>
                                </div>
                                <small
                                    v-if="remark.invalid"
                                    class="text-red-500 block"
                                    >請輸入備註內容</small
                                >
                                <div
                                    class="flex flex-row gap-2.5 whitespace-nowrap py-2"
                                >
                                    <div class="pr-2">提醒</div>
                                    <div
                                        v-for="(recipient, recipient_index) in [
                                            '所有人',
                                            '申請人',
                                            '審核人',
                                        ]"
                                        :key="recipient_index"
                                        @click="$forceUpdate()"
                                    >
                                        <RadioButton
                                            :id="
                                                recipient +
                                                remark_index.toString() +
                                                recipient_index.toString()
                                            "
                                            class="mb-1"
                                            :name="recipient"
                                            :value="recipient"
                                            v-model="remark.recipient"
                                        />
                                        <label
                                            class="cursor-pointer"
                                            :for="
                                                recipient +
                                                remark_index.toString() +
                                                recipient_index.toString()
                                            "
                                        >
                                            {{ recipient }}
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div
                class="buttons mt-12 w-full flex justify-between md:justify-end"
            >
                <Button
                    @click="action = 2"
                    label="預覽"
                    class="w-28 underline mr-5 p-button-text p-button-secondary"
                    plain
                    text
                />
                <Button
                    @click="previous"
                    label="取消"
                    class="p-button-outlined p-button-secondary w-28 mr-5"
                />
                <Button @click="saveLayoutReturn()" label="儲存" class="w-28" />
            </div>
        </div>

        <!-- 預覽需求單 -->
        <div v-if="action == 2">
            <span @click="action = 1" class="cursor-pointer">
                <i class="fas fa-arrow-left"></i>
            </span>
            <div class="text-center p-6">
                <span class="text-xl font-semibold"> 需求單預覽 </span>
            </div>
            <div class="demandList">
                <div class="flex flex-wrap">
                    <div
                        v-for="(item, item_index) in settingForm"
                        :key="item.id"
                    >
                        <DemandItem
                            :formIndex="0"
                            :item="item"
                            :flowType="''"
                            :flowColId="0"
                            v-on:openList="openList(item, item_index)"
                        />
                    </div>
                </div>
                <hr v-if="remarks.length > 0" class="w-full text-gray-200" />
                <div>
                    <div
                        v-for="(remark, remark_index) in remarks"
                        :key="remark_index"
                        class="pt-6 flex"
                    >
                        <div class="w-5">
                            <p class="w-3 text-primary text-xl">•</p>
                        </div>
                        <p>{{ remark.value }}</p>
                    </div>
                </div>
            </div>
        </div>
        <ConfirmDialog ref="confirmDialog" />
        <Dialog
            :visible.sync="displayModal4"
            :closable="false"
            :containerStyle="{
                width: '30vw',
            }"
            :modal="true"
        >
            <div class="pb-8">
                <img class="mx-auto" src="@images/popup_state/warning.png" />
                <br />
                <p class="text-center">確定要刪除嗎?</p>
            </div>
            <template #footer>
                <div class="flex justify-evenly">
                    <Button
                        @click="displayModal4 = false"
                        label="取消"
                        class="p-button-outlined p-button-secondary"
                    />
                    <Button
                        @click="del == 0 ? deleteGroups() : deleteLayout()"
                        label="確定"
                        class=""
                    />
                </div>
            </template>
        </Dialog>
        <Dialog
            :visible.sync="displayModal4"
            :closable="false"
            :containerStyle="{
                width: '30vw',
            }"
            :modal="true"
        >
            <div class="pb-8">
                <img class="mx-auto" src="@images/popup_state/warning.png" />
                <br />
                <p class="text-center">確定要刪除嗎?</p>
            </div>
            <template #footer>
                <div class="flex justify-evenly">
                    <Button
                        @click="displayModal4 = false"
                        label="取消"
                        class="p-button-outlined p-button-secondary"
                    />
                    <Button
                        @click="del == 0 ? deleteGroups() : deleteLayout()"
                        label="確定"
                        class=""
                    />
                </div>
            </template>
        </Dialog>
        <Toast ref="toast" position="top-center" />
        <Dialog
            :visible.sync="displayModal3"
            :dismissableMask="true"
            :closable="true"
            :containerStyle="{
                width: '30vw',
            }"
            :modal="true"
            header="選項說明"
            @hide="saveOpHint"
        >
            <div class="pb-8">
                <span>說明內容</span>
                <br />
                <InputText v-model="optionCommit" class="w-full" />
            </div>
            <template #footer>
                <div></div>
            </template>
        </Dialog>
        <Dialog
            :visible.sync="displayModal"
            :dismissableMask="true"
            :containerStyle="{
                width: '70%',
            }"
            :modal="true"
            @hide="saveList"
        >
            <template #header>
                <div class="w-full flex justify-between">
                    <div class="flex flex-wrap">
                        <h3
                            class="text-2xl font-semibold whitespace-nowrap my-auto mr-3"
                        >
                            項目清單
                        </h3>
                        <Button
                            @click="listModeChange"
                            :label="
                                !('mode' in settingForm[columnIndex]) ||
                                settingForm[columnIndex].mode == 0
                                    ? '預設表單'
                                    : '員工填寫'
                            "
                            class="w-28 p-button-outlined"
                        ></Button>
                    </div>
                    <div class="flex overflow-x-auto my-auto">
                        <p
                            v-if="settingForm[columnIndex].mode == 1"
                            class="underline mx-2 whitespace-nowrap cursor-pointer"
                        >
                            <span
                                @click="ListColSwitch(0, 'name')"
                                v-if="'name' in menuForm[0]"
                                >移除項目</span
                            >
                            <span @click="ListColSwitch(1, 'name')" v-else
                                >新增項目</span
                            >
                        </p>
                        <p
                            v-if="settingForm[columnIndex].mode == 1"
                            class="underline mx-2 whitespace-nowrap cursor-pointer"
                        >
                            <span
                                @click="ListColSwitch(0, 'count')"
                                v-if="'count' in menuForm[0]"
                                >移除數量</span
                            >
                            <span @click="ListColSwitch(1, 'count')" v-else
                                >新增數量</span
                            >
                        </p>
                        <p
                            class="underline mx-2 whitespace-nowrap cursor-pointer"
                        >
                            <span
                                @click="ListColSwitch(0, 'unit')"
                                v-if="'unit' in menuForm[0]"
                                >移除單位</span
                            >
                            <span @click="ListColSwitch(1, 'unit')" v-else
                                >新增單位</span
                            >
                        </p>
                        <p
                            class="underline mx-2 whitespace-nowrap cursor-pointer"
                        >
                            <span
                                @click="ListColSwitch(0, 'price')"
                                v-if="'price' in menuForm[0]"
                                >移除單價</span
                            >
                            <span @click="ListColSwitch(1, 'price')" v-else
                                >新增單價</span
                            >
                        </p>
                        <p
                            class="underline mx-2 whitespace-nowrap cursor-pointer"
                        >
                            <span
                                @click="ListColSwitch(0, 'total')"
                                v-if="'total' in menuForm[0]"
                                >移除總計</span
                            >
                            <span @click="ListColSwitch(1, 'total')" v-else
                                >新增總計</span
                            >
                        </p>
                        <p
                            class="underline mx-2 whitespace-nowrap cursor-pointer"
                        >
                            <span
                                @click="ListColSwitch(0, 'memo')"
                                v-if="'memo' in menuForm[0]"
                                >移除備註</span
                            >
                            <span @click="ListColSwitch(1, 'memo')" v-else
                                >新增備註</span
                            >
                        </p>
                    </div>
                </div>
            </template>
            <div class="m-0">
                <table class="py-5 table-auto" width="100%">
                    <thead class="border-b border-gray-200">
                        <tr>
                            <th class="text-left" v-if="'name' in menuForm[0]">
                                項目
                            </th>
                            <th class="text-left" v-if="'count' in menuForm[0]">
                                數量
                            </th>
                            <th class="text-left" v-if="'unit' in menuForm[0]">
                                單位
                            </th>
                            <th class="text-left" v-if="'price' in menuForm[0]">
                                單價
                            </th>
                            <th class="text-left" v-if="'total' in menuForm[0]">
                                <p class="w-32">總計</p>
                            </th>
                            <th class="text-left" v-if="'memo' in menuForm[0]">
                                備註
                            </th>
                            <th class="text-right w-40">
                                <div
                                    v-if="
                                        displayModal &&
                                        action == 1 &&
                                        (!(
                                            'mode' in settingForm[columnIndex]
                                        ) ||
                                            settingForm[columnIndex].mode == 0)
                                    "
                                >
                                    <button class="mr-5">
                                        <img
                                            @click="triggerInput"
                                            id="file-upload"
                                            style="max-width: 24px"
                                            src="@images/icon/import_enable.png"
                                            alt=""
                                        />
                                    </button>
                                    <input
                                        type="file"
                                        ref="fileInput"
                                        @change="inputExcel"
                                        class="visuallyhidden"
                                        accept=".xlsx,.xls"
                                    />
                                    <button class="">
                                        <img
                                            @click="outputExcel"
                                            style="max-width: 24px"
                                            src="@images/icon/export_enable.png"
                                            alt=""
                                        />
                                    </button>
                                </div>
                            </th>
                        </tr>
                    </thead>
                    <tbody
                        :class="
                            displayModal &&
                            action == 1 &&
                            (!('mode' in settingForm[columnIndex]) ||
                                settingForm[columnIndex].mode == 0)
                                ? 'border-b'
                                : ''
                        "
                        class="border-gray-200"
                    >
                        <tr
                            v-for="(item, item_index) in menuForm"
                            :key="item_index"
                        >
                            <td class="py-3" v-if="'name' in menuForm[0]">
                                <InputText
                                    v-if="
                                        displayModal &&
                                        action == 1 &&
                                        (!(
                                            'mode' in settingForm[columnIndex]
                                        ) ||
                                            settingForm[columnIndex].mode == 0)
                                    "
                                    v-model="item.name"
                                />
                                <InputText
                                    v-else-if="
                                        displayModal &&
                                        (!(
                                            'mode' in settingForm[columnIndex]
                                        ) ||
                                            settingForm[columnIndex].mode == 1)
                                    "
                                    disabled
                                    v-model="item.name"
                                />
                                <p v-else>{{ item.name }}</p>
                            </td>
                            <td class="py-3" v-if="'count' in menuForm[0]">
                                <InputNumber
                                    v-model="item.count"
                                    :disabled="
                                        displayModal &&
                                        action == 1 &&
                                        settingForm[columnIndex].mode == 1
                                    "
                                />
                            </td>
                            <td class="py-3" v-if="'unit' in menuForm[0]">
                                <InputText
                                    v-if="
                                        displayModal &&
                                        action == 1 &&
                                        (!(
                                            'mode' in settingForm[columnIndex]
                                        ) ||
                                            settingForm[columnIndex].mode == 0)
                                    "
                                    v-model="item.unit"
                                />
                                <InputText
                                    v-else-if="
                                        displayModal &&
                                        (!(
                                            'mode' in settingForm[columnIndex]
                                        ) ||
                                            settingForm[columnIndex].mode == 1)
                                    "
                                    v-model="item.unit"
                                    disabled
                                />
                                <p v-else>{{ item.unit }}</p>
                            </td>
                            <td class="py-3" v-if="'price' in menuForm[0]">
                                <InputNumber
                                    mode="currency"
                                    currency="USD"
                                    locale="en-US"
                                    v-if="
                                        displayModal &&
                                        action == 1 &&
                                        (!(
                                            'mode' in settingForm[columnIndex]
                                        ) ||
                                            settingForm[columnIndex].mode == 0)
                                    "
                                    v-model="item.price"
                                />
                                <InputNumber
                                    mode="currency"
                                    currency="USD"
                                    locale="en-US"
                                    v-else-if="
                                        displayModal &&
                                        (!(
                                            'mode' in settingForm[columnIndex]
                                        ) ||
                                            settingForm[columnIndex].mode == 1)
                                    "
                                    disabled
                                    v-model="item.name"
                                />
                                <p v-else>{{ item.price }}</p>
                            </td>
                            <td class="p-2" v-if="'total' in menuForm[0]">
                                <p>{{ menuTotal(item) }}</p>
                            </td>
                            <td class="py-3" v-if="'memo' in menuForm[0]">
                                <InputText
                                    v-if="
                                        displayModal &&
                                        action == 1 &&
                                        (!(
                                            'mode' in settingForm[columnIndex]
                                        ) ||
                                            settingForm[columnIndex].mode == 0)
                                    "
                                    v-model="item.memo"
                                />
                                <InputText
                                    v-else-if="
                                        displayModal &&
                                        (!(
                                            'mode' in settingForm[columnIndex]
                                        ) ||
                                            settingForm[columnIndex].mode == 1)
                                    "
                                    disabled
                                    v-model="item.name"
                                />
                                <p v-else>{{ item.memo }}</p>
                            </td>
                            <td v-if="action == 1" class="text-right my-3">
                                <button
                                    v-if="menuForm.length > 1"
                                    @click="menuForm.splice(item_index, 1)"
                                >
                                    <img
                                        style="max-width: 24px"
                                        src="@images/icon/delete_enable.png"
                                        alt=""
                                    />
                                </button>
                            </td>
                        </tr>
                        <tr>
                            <td class="py-3" v-if="'name' in menuForm[0]"></td>
                            <td class="py-3" v-if="'count' in menuForm[0]"></td>
                            <td class="py-3" v-if="'unit' in menuForm[0]"></td>
                            <td class="py-3" v-if="'price' in menuForm[0]"></td>
                            <td class="p-2" v-if="'total' in menuForm[0]">
                                <p
                                    v-if="
                                        displayModal &&
                                        (!(
                                            'mode' in settingForm[columnIndex]
                                        ) ||
                                            settingForm[columnIndex].mode == 0)
                                    "
                                >
                                    合計 ${{ finallyTotal() }}
                                </p>
                            </td>
                            <td class="py-3" v-if="'memo' in menuForm[0]"></td>
                            <td v-if="action == 1" class="my-3"></td>
                        </tr>
                    </tbody>
                </table>
                <div
                    v-if="
                        displayModal &&
                        action == 1 &&
                        (!('mode' in settingForm[columnIndex]) ||
                            settingForm[columnIndex].mode == 0)
                    "
                    @click="addItemList()"
                    class="w-full h-12 my-3 text-current cursor-pointer"
                >
                    <i class="fa-solid fa-plus"></i>
                    <span class="my-auto inline-block">新增項目</span>
                </div>
            </div>
            <template #footer>
                <div class="p-1"></div>
                <!-- <Button
                    v-if="action == 1"
                    @click="cancelList()"
                    label="取消"
                    class="p-button-outlined p-button-secondary w-28 mr-5"
                />
                <Button
                    v-if="action == 1"
                    @click="saveList()"
                    label="儲存"
                    class="w-28"
                /> -->
            </template>
        </Dialog>
        <Dialog
            :visible.sync="displayModal6"
            :dismissableMask="true"
            :containerStyle="{
                width: '70%',
            }"
            :modal="true"
            header="自訂表單"
            class="customDialog"
            @hide="
                $set(
                    settingForm[columnIndex],
                    'custom_list',
                    $refs.customForm.custom_rows
                );
                $set(
                    settingForm[columnIndex],
                    'form_setting',
                    $refs.customForm.form_setting
                );
                $set(
                    settingForm[columnIndex],
                    'can_insert',
                    $refs.customForm.can_insert
                );
            "
        >
            <CustomForm
                :action="action"
                :columnId="columnIndex + 1"
                :dem_custom_list="
                    displayModal6 && 'custom_list' in settingForm[columnIndex]
                        ? settingForm[columnIndex].custom_list
                        : [['']]
                "
                :dem_form_setting="
                    displayModal6 && 'form_setting' in settingForm[columnIndex]
                        ? settingForm[columnIndex].form_setting
                        : [
                              {
                                  id: columnIndex + 1 + '-' + 1,
                                  name: '',
                                  type: 'input',
                                  mode: 0,
                                  is_must: false,
                                  frozen: false,
                                  filterCriteria: false,
                              },
                          ]
                "
                :dem_can_insert="
                    displayModal6 && 'can_insert' in settingForm[columnIndex]
                        ? settingForm[columnIndex].can_insert
                        : false
                "
                ref="customForm"
            />
            <template #footer>
                <div
                    v-if="
                        action == 1 ||
                        ($refs.customForm !== undefined &&
                            settingForm[columnIndex].can_insert)
                    "
                    @click="
                        $refs.customForm.custom_rows.push([]);
                        $refs.customForm.form_setting.forEach((col, index) => {
                            col.type == 'input'
                                ? $refs.customForm.custom_rows[
                                      $refs.customForm.custom_rows.length - 1
                                  ].push('')
                                : $refs.customForm.custom_rows[
                                      $refs.customForm.custom_rows.length - 1
                                  ].push(0);
                        });
                        $refs.customForm.custom_values = [];
                    "
                    class="p-1 text-center"
                >
                    <label>新增內容</label>
                </div>
            </template>
        </Dialog>
        <ImportDialog
            :visible.sync="displayModal1"
            :dismissableMask="true"
            :containerStyle="{
                width: '70%',
            }"
            :modal="true"
            class="importSelectDialog"
        >
            <template #header>
                <div class="w-full flex justify-center">
                    <h3 class="text-2xl font-semibold">欄位帶入設定</h3>
                </div>
            </template>
            <div class="m-0">
                <span
                    class="underline cursor-pointer"
                    @click="displayModal2 = true"
                    >新增要帶入之需求單</span
                >
                <TabView
                    class="min-w-full w-max"
                    v-if="imports.demands.length > 0"
                    :key="forceUpdate_TabView"
                >
                    <TabPanel
                        v-for="(tab, index) in imports.demands"
                        :key="index"
                        :header="tab.name"
                    >
                        <table class="table-auto" width="100%">
                            <thead class="border-b border-gray-200">
                                <tr>
                                    <th class="text-left p-5">此需求單</th>
                                    <th class="text-left w-40 p-5"></th>
                                    <th class="text-left p-5">
                                        {{ tab.name }}
                                    </th>
                                    <th
                                        class="text-right w-24 p-5 whitespace-nowrap"
                                    >
                                        <p
                                            @click="deleteImports(index)"
                                            class="underline cursor-pointer"
                                        >
                                            移除需求單
                                        </p>
                                    </th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr
                                    v-for="(tr, tr_index) in tab.models"
                                    :key="tr_index"
                                    class="border-b border-gray-200"
                                >
                                    <td class="text-left p-5">
                                        <Dropdown
                                            :options="importableForm"
                                            :optionDisabled="
                                                (option) =>
                                                    isOptionDisabled(
                                                        option,
                                                        index
                                                    )
                                            "
                                            optionLabel="name"
                                            data-key="id"
                                            v-model="tr.demand_col"
                                            @change="
                                                () => {
                                                    tr.isSelected = true;
                                                    getImportSetting(
                                                        tab.id,
                                                        index,
                                                        tr.demand_col,
                                                        tr_index
                                                    );
                                                }
                                            "
                                            placeholder="選擇欄位"
                                        >
                                            <template #option="slotProps">
                                                <div>
                                                    <span
                                                        >欄位{{
                                                            settingForm.findIndex(
                                                                (el) =>
                                                                    el.id ===
                                                                    slotProps
                                                                        .option
                                                                        .id
                                                            ) + 1
                                                        }}</span
                                                    >
                                                    <span>{{
                                                        slotProps.option.name
                                                    }}</span>
                                                </div>
                                            </template>
                                        </Dropdown>
                                    </td>
                                    <td class="text-left w-40 p-5">
                                        <i class="fa-solid fa-arrow-left"></i>
                                    </td>
                                    <td class="text-left p-5">
                                        <Dropdown
                                            :disabled="tr.cols.length == 0"
                                            :options="tr.cols"
                                            optionLabel="name"
                                            data-key="id"
                                            v-model="tr.col"
                                            @change="
                                                changeImport(
                                                    tr.col,
                                                    index,
                                                    tr.demand_col,
                                                    tr_index
                                                )
                                            "
                                            placeholder="選擇欄位"
                                        >
                                            <template #option="slotProps">
                                                <div>
                                                    <span
                                                        >欄位{{
                                                            slotProps.option
                                                                .col_no
                                                        }}</span
                                                    >
                                                    <span>{{
                                                        slotProps.option.name
                                                    }}</span>
                                                </div>
                                            </template>
                                        </Dropdown>
                                    </td>
                                    <td class="text-right p-5 w-24">
                                        <button
                                            @click="
                                                tab.models.splice(tr_index, 1)
                                            "
                                        >
                                            <img
                                                style="max-width: 24px"
                                                src="@images/icon/delete_enable.png"
                                                alt=""
                                            />
                                        </button>
                                    </td>
                                </tr>
                            </tbody>
                            <button
                                @click="
                                    tab.models.push({
                                        cols: [],
                                        col: {},
                                        col_id: null,
                                        demand_col: {},
                                        demand_col_id: null,
                                    })
                                "
                                :disabled="
                                    tab.models.length == importableForm.length
                                "
                                class="newSettingBtn m-5"
                                :class="{
                                    'pointer-events-none text-gray-300':
                                        tab.models.length ==
                                        importableForm.length,
                                }"
                            >
                                <i class="fa-solid fa-plus"></i>
                                <span class="my-auto inline-block"
                                    >新增欄位</span
                                >
                            </button>
                        </table>
                    </TabPanel>
                </TabView>
            </div>
            <template #footer>
                <div class="p-1"></div>
                <!-- <Button
                    @click="displayModal1 = !displayModal1"
                    label="取消"
                    class="p-button-outlined p-button-secondary w-28 mr-5"
                />
                <Button
                    @click="displayModal1 = !displayModal1"
                    label="儲存"
                    class="w-28"
                /> -->
            </template>
            <SelectDialog
                header="選擇需求單"
                :visible.sync="displayModal2"
                :dismissableMask="true"
                :closable="false"
                :containerStyle="{ width: '30vw' }"
                :modal="true"
            >
                <div
                    v-for="(demand, index) in imports.demandLists"
                    :key="index"
                    class="mb-4"
                >
                    <RadioButton
                        class="mb-1 mr-2"
                        :class="{
                            'pointer-events-none': imports.demands
                                .map((el) => el.id)
                                .includes(demand.id),
                        }"
                        :value="JSON.parse(JSON.stringify(demand))"
                        :id="index"
                        v-model="imports.demand"
                        :disabled="
                            imports.demands
                                .map((el) => el.id)
                                .includes(demand.id)
                        "
                    />
                    <label
                        :for="index"
                        :class="{
                            'text-gray-300': imports.demands
                                .map((el) => el.id)
                                .includes(demand.id),
                        }"
                        >{{ demand.name }}</label
                    >
                </div>
                <template #footer>
                    <div class="flex justify-center">
                        <Button
                            @click="displayModal2 = false"
                            label="取消"
                            class="p-button-outlined p-button-secondary w-28 mr-5"
                        />
                        <Button
                            @click="selectImport()"
                            label="確定"
                            class="w-28"
                        />
                    </div>
                </template>
            </SelectDialog>
        </ImportDialog>
        <Dialog
            :visible.sync="displayModal5"
            :dismissableMask="true"
            :closable="true"
            :containerStyle="{
                width: '30vw',
            }"
            :modal="true"
            header="子層選項"
        >
            <div v-if="displayModal5 && settingForm.length !== 0">
                <div
                    v-for="(state, stateIndex) in settingForm[columnIndex]
                        .options[optionIndex].states"
                    :key="stateIndex"
                >
                    <span>選項{{ stateIndex + 1 }}</span>
                    <br />
                    <div class="flex w-full">
                        <InputText class="w-full" v-model="state.iname" />
                        <img
                            class="h-6 my-auto cursor-pointer"
                            @click="
                                settingForm[columnIndex].options[
                                    optionIndex
                                ].states.splice(stateIndex, 1);
                                if (
                                    settingForm[columnIndex].options[
                                        optionIndex
                                    ].states.length == 0
                                ) {
                                    $delete(
                                        settingForm[columnIndex].options[
                                            optionIndex
                                        ],
                                        'states'
                                    );
                                    $delete(
                                        settingForm[columnIndex].options[
                                            optionIndex
                                        ],
                                        'gname'
                                    );
                                    $set(
                                        settingForm[columnIndex].options[
                                            optionIndex
                                        ],
                                        'iname',
                                        settingForm[columnIndex].options[
                                            optionIndex
                                        ].name
                                    );
                                }
                            "
                            src="@images/icon/delete_enable.png"
                            alt=""
                        />
                    </div>
                </div>
            </div>
            <template #footer>
                <div
                    @click="
                        !(
                            'states' in
                            settingForm[columnIndex].options[optionIndex]
                        )
                            ? $set(
                                  settingForm[columnIndex].options[optionIndex],
                                  'states',
                                  []
                              )
                            : '';
                        settingForm[columnIndex].options[
                            optionIndex
                        ].states.push({
                            iname: '',
                        });
                        $delete(
                            settingForm[columnIndex].options[optionIndex],
                            'iname'
                        );
                        $set(
                            settingForm[columnIndex].options[optionIndex],
                            'gname',
                            settingForm[columnIndex].options[optionIndex].name
                        );
                    "
                    class="cursor-pointer text-left"
                >
                    <i class="fa-solid fa-plus"></i>
                    <span>新增選項</span>
                </div>
            </template>
        </Dialog>
    </div>
</template>
<script>
import axios from "axios";
import Button from "primevue/button";
import Cascadeselect from "primevue/cascadeselect";
import Checkbox from "primevue/checkbox";
import ConfirmDialog from "primevue/confirmdialog";
import CustomForm from "@/demand/common/custom-form";
import DemandItem from "@/demand/common/demand-item";
import Dialog from "primevue/dialog";
import Dropdown from "primevue/dropdown";
import ImportDialog from "primevue/dialog";
import SelectDialog from "primevue/dialog";
import InputNumber from "primevue/inputnumber";
import InputSwitch from "primevue/inputswitch";
import InputText from "primevue/inputtext";
import RadioButton from "primevue/radiobutton";
import Toast from "primevue/toast";
import TabView from "primevue/tabview";
import TabPanel from "primevue/tabpanel";
import { nanoid } from "nanoid";
export default {
    components: {
        Button,
        Cascadeselect,
        ConfirmDialog,
        DemandItem,
        Dialog,
        Dropdown,
        Checkbox,
        ImportDialog,
        SelectDialog,
        CustomForm,
        InputNumber,
        InputSwitch,
        InputText,
        RadioButton,
        Toast,
        TabView,
        TabPanel,
    },
    data() {
        return {
            // 0:通用流程 , 1:依選項流程
            flowSettingType: null,
            action: 0,
            apiURL: "/api/demand/setting/groups",
            apiSettingURL: "/api/demand/setting/layouts",
            cascadeForm: [],
            change: 0,
            dataList: [],
            dataListIndex: null,
            layoutIndex: null,
            dateSelect: [
                {
                    dname: "可選所有日期",
                    value: "free",
                },
                {
                    dname: "限制選擇當日以前日期",
                    value: "pass",
                },
                {
                    dname: "限制選擇當日以後日期",
                    value: "future",
                },
                {
                    dname: "自動帶入日期",
                    value: "auto",
                },
            ],
            dragIndex: "",
            dragColIndex: "",
            dragOptionIndex: "",
            displayModal: false,
            displayModal1: false,
            displayModal2: false,
            displayModal3: false,
            displayModal4: false,
            displayModal5: false,
            displayModal6: false,
            del: null,
            delIndexGroup: null,
            delIndexLayout: null,
            optionIndex: null,
            optionCommit: "",
            imports: {
                // createList: false,
                demandLists: [],
                demand: {},
                demands: [],
            },
            menuForm: [
                {
                    name: "",
                    count: 0,
                    memo: "",
                    unit: "",
                    price: 0,
                    total: 0,
                },
            ],
            listId: 0,
            remarks: [],
            file: {},
            settingTitle: "新增需求單",
            settingForm: [],
            settingName: "",
            settingCode: "",
            originalCode: "",
            settingId: 0, //layout_id
            settingGroupId: 0,
            settingRequest: false,
            settingSort: 0,
            settingOver: false,
            settingOpen: false,
            settingImport: false,
            //該單目前的簽核關卡
            settingSignRoles: [],
            invalidSettingName: false,
            invalidSettingCode: false,
            invalidSettingText: "請輸入需求單編號",
            summaryColId: null,
            //====項目清單
            columnIndex: 0,
            listRows: [],
            formData: new FormData(),
            computeOptions: [
                {
                    name: "相加",
                    value: "+",
                },
                {
                    name: "相減",
                    value: "-",
                },
                {
                    name: "相乘",
                    value: "*",
                },
                {
                    name: "相除",
                    value: "/",
                },
            ],
            forceUpdate_TabView: 1,
        };
    },
    watch: {
        "$root.currentTab": function (newValue) {
            if (this.action == 1 && newValue == 1) {
                this.$root.currentTab = 0;
                this.$refs.confirmDialog.visible = true;
                this.$refs.confirmDialog.confirmation = {
                    message:
                        "未儲存" +
                        "「" +
                        this.settingName +
                        "」" +
                        "是否確定離開?",
                    acceptLabel: "是",
                    rejectLabel: "否",
                    accept: () => {
                        this.$root.currentTab = 1;
                        this.action = 0;
                    },
                    reject: () => {
                        this.$root.currentTab = 0;
                    },
                };
            }
            if (newValue == 0) {
                this.getGroups();
            }
        },
        settingCode: function (newValue, oldValue) {
            if (newValue != this.originalCode) {
                axios
                    .get(this.apiSettingURL + "/code?code=" + newValue)
                    .then((response) => {
                        if (response.data[0]) {
                            this.invalidSettingCode = false;
                        } else {
                            this.invalidSettingCode = true;
                            this.invalidSettingText = response.data[1];
                        }
                    })
                    .catch((error) => {
                        console.error(error);
                    });
            }
        },
        settingForm: {
            handler: function () {
                this.totalCompute();
            },
            deep: true,
        },
    },
    mounted() {
        this.getGroups();
        this.getCascadeForm();
        this.initMenuForm();
    },
    methods: {
        // --------------------------------------需求單列表--------------------------------------
        getGroups() {
            axios
                .get(this.apiURL)
                .then((response) => {
                    let dataRows = response.data;
                    if (dataRows) {
                        dataRows.forEach((item, index) => {
                            dataRows[index].edit = false;
                        });
                        this.dataList = dataRows;
                        this.dataList.forEach((list) => {
                            list.layouts.sort((a, b) => {
                                return (
                                    (b.sort != "" && b.sort != null) -
                                        (a.sort != "" && a.sort != null) ||
                                    a.sort - b.sort
                                );
                            });
                        });
                    }
                })
                .catch((error) => {
                    console.error(error);
                });
        },
        changeIcon(key) {
            let icon = this.dataList[key].edit;
            if (!icon) {
                this.dataList[key].edit = true;
            } else {
                this.saveGroup(key);
            }
        },
        saveGroup(key) {
            let param = {
                id: this.dataList[key].id,
                name: this.dataList[key].name,
            };
            axios
                .put(this.apiURL, param)
                .then((response) => {
                    if (response.data) {
                        this.dataList[key].edit = false;
                        this.$refs.toast.add({
                            severity: "success",
                            summary: "修改成功",
                            life: 3000,
                        });
                    } else {
                        this.$refs.toast.add({
                            severity: "error",
                            summary: "修改失敗",
                            life: 3000,
                        });
                    }
                })
                .catch((error) => {
                    console.error(error);
                    if (error.response) {
                        this.$refs.toast.add({
                            severity: "error",
                            summary: "修改失敗!",
                            life: 3000,
                        });
                    }
                });
        },
        deleteLayout() {
            axios
                .delete(
                    this.apiURL +
                        "/layout/" +
                        this.dataList[this.delIndexGroup].layouts[
                            this.delIndexLayout
                        ].id
                )
                .then((response) => {
                    if (response.data[0]) {
                        this.$refs.toast.add({
                            severity: "success",
                            summary: "刪除成功",
                            life: 3000,
                        });
                        this.dataList[this.delIndexGroup].layouts.splice(
                            this.delIndexLayout,
                            1
                        );
                    } else {
                        this.$refs.toast.add({
                            severity: "error",
                            summary: response.data[1],
                            life: 3000,
                        });
                    }
                })
                .catch((error) => {
                    console.error(error);
                    this.$refs.toast.add({
                        severity: "error",
                        summary: "刪除失敗!",
                        life: 3000,
                    });
                })
                .finally(() => {
                    this.displayModal4 = false;
                });
        },
        deleteGroups() {
            axios
                .delete(
                    this.apiURL + "/" + this.dataList[this.delIndexGroup].id
                )
                .then((response) => {
                    if (response.data[0]) {
                        this.$refs.toast.add({
                            severity: "success",
                            summary: "刪除成功",
                            life: 3000,
                        });
                        this.dataList.splice(this.delIndexGroup, 1);
                    } else {
                        this.$refs.toast.add({
                            severity: "error",
                            summary: response.data[1],
                            life: 3000,
                        });
                    }
                })

                .catch((error) => {
                    console.error(error);
                    this.$refs.toast.add({
                        severity: "error",
                        summary: "刪除失敗!",
                        life: 3000,
                    });
                })
                .finally(() => {
                    this.displayModal4 = false;
                });
        },
        addGroup() {
            let len = this.dataList.length;
            let defaultName = {
                name: "類別" + this.dataList.length++,
                edit: false,
                id: 0,
            };
            axios
                .post(this.apiURL, defaultName)
                .then((response) => {
                    if (response.data) {
                        defaultName.id = response.data;
                        this.$set(this.dataList, len, defaultName);
                        this.$refs.toast.add({
                            severity: "success",
                            summary: "新增成功",
                            life: 3000,
                        });
                    } else {
                        this.$refs.toast.add({
                            severity: "error",
                            summary: "新增失敗",
                            life: 3000,
                        });
                    }
                })
                .catch((error) => {
                    console.error(error);
                    this.$refs.toast.add({
                        severity: "error",
                        summary: "新增失敗!",
                        life: 3000,
                    });
                });
        },
        changeLayoutSwitch(key, index) {
            let content = this.dataList[key].layouts[index];
            let str = content.open
                ? "確定啟用" + "「" + content.name + "」" + "?"
                : "確定關閉" + "「" + content.name + "」" + "?";
            let switchStr = content.open
                ? "「" + content.name + "」" + "已開啟"
                : "「" + content.name + "」" + "已關閉";
            if (
                content.open == true &&
                (!content.sign_role || content.sign_role.length == 0)
            ) {
                this.$refs.confirmDialog.visible = true;
                this.$refs.confirmDialog.confirmation = {
                    message:
                        "未設定" + "「" + content.name + "」" + "是否前往設定?",
                    acceptLabel: "是",
                    rejectLabel: "否",
                    accept: () => {
                        this.dataList[key].layouts[index].open = !content.open;
                        this.$root.layoutKey = key;
                        this.$root.layoutIndex = index;
                        this.$root.currentTab = 1;
                    },
                    reject: () => {
                        this.dataList[key].layouts[index].open = !content.open;
                    },
                };
            } else {
                this.$refs.confirmDialog.visible = true;
                this.$refs.confirmDialog.confirmation = {
                    message: str,
                    acceptLabel: "確定",
                    rejectLabel: "取消",
                    accept: () => {
                        axios
                            .put(this.apiURL + "/change/layout/switch", {
                                id: content.id,
                                open: content.open,
                            })
                            .then((response) => {
                                this.$refs.toast.add({
                                    severity: "success",
                                    summary: switchStr,
                                    life: 3000,
                                });
                            })
                            .catch((error) => {
                                console.error(error);
                                this.$refs.toast.add({
                                    severity: "error",
                                    summary: "開關失敗!",
                                    life: 3000,
                                });
                                this.dataList[key].layouts[index].open =
                                    !content.open;
                            });
                    },
                    reject: () => {
                        this.dataList[key].layouts[index].open = !content.open;
                    },
                };
            }
        },

        // --------------------------------------需求單設定--------------------------------------
        getCascadeForm() {
            axios
                .get("/api/demand/setting/layouts/type/cas-select")
                .then((response) => {
                    this.cascadeForm = response.data ?? [];
                })
                .catch((error) => {
                    console.error(error);
                });
        },
        getLayoutSetting(id, index, layoutIndex) {
            axios
                .get("/api/demand/setting/layouts", { params: { id: id } })
                .then((response) => {
                    let dataRow = response.data;
                    if (dataRow) {
                        this.settingForm = dataRow.columns;
                        this.settingName = dataRow.name;
                        this.settingCode = dataRow.setting_code;
                        this.originalCode = dataRow.setting_code;
                        this.settingId = dataRow.id;
                        this.settingRequest = dataRow.can_multi;
                        this.settingOver = dataRow.auto_finished;
                        this.settingImport = dataRow.import;
                        this.remarks = [];
                        this.flowSettingType = dataRow.flow_setting_type;
                        this.settingSignRoles = dataRow.sign_role;
                        for (let i = 0; i < dataRow.remarks.length; i++) {
                            if (dataRow.remarks[i] instanceof Object) {
                                this.remarks.push(dataRow.remarks[i]);
                            } else {
                                this.remarks.push({
                                    value: dataRow.remarks[i],
                                    recipient: "所有人",
                                });
                            }
                        }
                        // 載入匯出資料
                        if (dataRow.file && !(dataRow.file instanceof Array)) {
                            // 新資料
                            if (
                                "word" in dataRow.file ||
                                "excel" in dataRow.file
                            ) {
                                this.file = dataRow.file;
                            }
                            // 舊資料
                            else {
                                const [ext, ...fileName] = dataRow.file.name
                                    .split(".")
                                    .reverse();
                                if (ext === "docx") {
                                    this.file = { word: dataRow.file };
                                } else if (ext === "xlx" || ext === "xlsx") {
                                    this.file = { excel: dataRow.file };
                                } else {
                                    console.log("未捕捉到的舊資料類型:", ext);
                                }
                            }
                        } else {
                            this.file = dataRow.file;
                        }
                        this.summaryColId =
                            parseInt(dataRow.summaryColId) ||
                            dataRow.summaryColId;
                        this.settingTitle = "編輯需求單";
                        this.getImport(this.settingId);
                        this.action = 1;
                        this.settingOpen = dataRow.open;
                        this.settingSort = dataRow.sort;
                        this.dataListIndex = index;
                        this.layoutIndex = layoutIndex;
                    }
                })
                .catch((error) => {
                    this.action = 0;
                    console.error(error);
                    this.$refs.toast.add({
                        severity: "error",
                        summary: "開啟失敗!",
                        life: 3000,
                    });
                });
        },
        dragstart(index) {
            if (typeof this.dragOptionIndex === "number") {
                return;
            }
            this.dragIndex = index;
        },
        dragenter(e, index) {
            if (typeof this.dragOptionIndex === "number") {
                return;
            }
            //FOR 火狐無法拖曳
            e.stopPropagation();
            e.preventDefault();
            // 避免源对象触发自身的dragenter事件
            if (this.dragIndex !== index) {
                const source = this.settingForm[this.dragIndex];
                this.settingForm.splice(this.dragIndex, 1);
                this.settingForm.splice(index, 0, source);
                // 排序变化后目标对象的索引变成源对象的索引
                this.dragIndex = index;
            }
        },
        dragover(e, index) {
            //FOR 火狐無法拖曳
            e.stopPropagation();
        },
        // @drag="dragOption($event, option_index)"
        // dragOption(e, optionIndex) {
        //   console.log(123, optionIndex)
        //   this.dragOptionIndex = optionIndex
        //   this.$forceUpdate();
        // },
        dragOptionStart(e, colIndex, optionIndex) {
            e.stopPropagation();
            this.dragColIndex = colIndex;
            this.dragOptionIndex = optionIndex;
            this.$forceUpdate();
        },
        dragOptionEnter(e, colIndex, optionIndex) {
            e.stopPropagation();
            if (
                this.dragOptionIndex !== optionIndex &&
                this.dragColIndex === colIndex
            ) {
                // 不同選項且同一欄位才執行
                const source =
                    this.settingForm[colIndex].options[this.dragOptionIndex];
                this.settingForm[colIndex].options.splice(
                    this.dragOptionIndex,
                    1
                );
                this.settingForm[colIndex].options.splice(
                    optionIndex,
                    0,
                    source
                );
                // 排序变化后目标对象的索引变成源对象的索引
                this.dragOptionIndex = optionIndex;
            }
        },
        // @dragover="dragOptionOver($event)"
        // dragOptionOver(e) {
        //   e.stopPropagation();
        // },
        dragOptionEnd(e) {
            e.stopPropagation();
            this.dragColIndex = "";
            this.dragOptionIndex = "";
            this.$forceUpdate();
        },
        previous() {
            if (this.change == 0) {
                this.action = 0;
                this.listRows = [];
                this.settingForm = [];
                this.imports.demands = [];
                this.file = null;
                this.remarks = [];
            } else {
                this.action = 1;
                this.$refs.confirmDialog.visible = true;
                this.$refs.confirmDialog.confirmation = {
                    message:
                        "未儲存" +
                        "「" +
                        this.settingName +
                        "」" +
                        "是否確定離開?",
                    acceptLabel: "是",
                    rejectLabel: "否",
                    accept: () => {
                        this.change = 0;
                        this.action = 0;
                        this.listRows = [];
                        this.settingForm = [];
                        this.file = null;
                        this.remarks = [];
                    },
                    reject: () => {
                        this.action = 1;
                    },
                };
            }
        },
        validate() {
            let error = false;

            if (!this.settingName) {
                error = true;
                this.invalidSettingName = true;
            } else {
                this.invalidSettingName = false;
            }

            var result = (this.settingCode ? this.settingCode : "").match(
                /[0-9A-Z]{2}/
            );
            if (result == null) {
                error = true;
                this.invalidSettingText = "請修正編號格式";
                this.invalidSettingCode = true;
            } else {
                this.invalidSettingCode = false;
            }

            this.settingForm.forEach((item) => {
                if (!item.type && "type" in item) {
                    error = true;
                    this.$set(item, "invalidType", true);
                } else {
                    this.$delete(item, "invalidType", true);
                }
                if (!item.name && "name" in item) {
                    error = true;
                    this.$set(item, "invalidName", true);
                } else {
                    this.$delete(item, "invalidName", true);
                }
                if ("options" in item) {
                    item.options.forEach((op) => {
                        if (!op.name) {
                            error = true;
                            this.$set(op, "invalid", true);
                        } else {
                            this.$delete(op, "invalid", true);
                        }
                    });
                }
            });

            // 檢查 remark
            this.remarks.forEach((remark) => {
                if (!remark.value) {
                    error = true;
                    this.$set(remark, "invalid", true);
                }
            });

            return error;
        },
        canMulti() {
            if (
                this.settingId !== 0 &&
                "roles" in
                    this.dataList[this.dataListIndex].layouts[this.layoutIndex]
                        .sign_role[0]
            ) {
                this.settingRequest = false;
                this.$refs.toast.add({
                    severity: "error",
                    summary: "已設定多筆申請，無法依選項設定流程",
                    life: 3000,
                });
            }
        },
        backToPreviousPage() {
            this.settingForm = [];
            this.action = 0;
            this.remarks = [];
            this.file = null;
        },
        saveLayoutReturn() {
            if (this.saveLayout()) {
                this.backToPreviousPage();
            }
        },
        saveLayout() {
            if (this.invalidSettingCode) {
                this.$refs.toast.add({
                    severity: "error",
                    summary: "需求單編號已重複!",
                    life: 3000,
                });
                return;
            }
            if (this.validate() == true) {
                this.$refs.toast.add({
                    severity: "error",
                    summary: "有欄位缺填!",
                    life: 3000,
                });
                return;
            }
            let param = {
                id: this.settingId,
                name: this.settingName,
                can_multi: this.settingRequest,
                auto_finished: this.settingOver,
                columns: this.settingForm.map((col) => {
                    if (col.type == "list") {
                        return { ...col, default: col.list };
                    }
                    if (col.type == "customList") {
                        return { ...col, default: col.custom_list };
                    }
                    return col;
                }),
                group_id: this.settingGroupId,
                file: this.file,
                open: this.settingOpen,
                import_from_others: this.settingImport,
                remarks: this.remarks,
                sort: this.settingSort,
                settingCode: this.settingCode,
                summaryColId: this.summaryColId,
                list_rows: this.listRows,
                list_import: this.imports.demands,
                old_layout_id: this.settingId,
            };
            this.settingForm.forEach((f) => {
                if ("files" in f) {
                    f.files = [];
                } else if (f.type == "database") {
                    f.selected.data = [];
                    f.filteredDb = [];
                }
            });

            // 都是處理同一張 layout 的邏輯已一同整併入 updateLayout API 中以同步更新 db table
            // await this.saveListToDB(response.data.id)
            // await this.saveImport(response.data.id);

            axios
                .put(this.apiSettingURL, param)
                .then((response) => {
                    if (response.data) {
                        this.getGroups();
                        this.$refs.toast.add({
                            severity: "success",
                            summary: "需求單設定儲存成功",
                            life: 3000,
                        });
                    } else {
                        this.$refs.toast.add({
                            severity: "error",
                            summary: "儲存失敗",
                            life: 3000,
                        });
                    }
                })
                .catch((error) => {
                    console.error(error);
                    if (error.response) {
                        this.$refs.toast.add({
                            severity: "error",
                            summary: "儲存失敗!",
                            life: 3000,
                        });
                    }
                });
            return 1;
        },
        settingCol(item) {
            this.change = 1;
            item.type = item.selected.type;
            this.$set(item, "name", "");
            delete item.must;
            delete item.audit_must;
            switch (item.type) {
                case "money":
                case "input":
                case "time":
                case "employee":
                case "number":
                    delete item.list;
                    delete item.mode;
                    delete item.date;
                    delete item.options;
                    delete item.other;
                    delete item.mul;
                    delete item.files;
                    delete item.Dbs;
                    delete item.filteredDb;
                    delete item.value;
                    delete item.preset;
                    delete item.compute;
                    delete item.parseInt;
                    delete item.cols;
                    delete item.displayTotalRow;
                    break;
                case "dropdown":
                case "single":
                case "multi":
                case "cascade":
                    delete item.list;
                    delete item.mode;
                    delete item.date;
                    delete item.files;
                    delete item.Dbs;
                    delete item.filteredDb;
                    delete item.value;
                    delete item.preset;
                    delete item.compute;
                    delete item.parseInt;
                    delete item.cols;
                    delete item.displayTotalRow;
                    this.$set(item, "options", []);
                    this.$set(item, "other", 0);
                    this.addSelect(1, item);
                    break;
                case "list":
                case "customList":
                    delete item.date;
                    delete item.options;
                    delete item.mul;
                    delete item.other;
                    delete item.files;
                    delete item.Dbs;
                    delete item.filteredDb;
                    delete item.value;
                    delete item.preset;
                    delete item.compute;
                    delete item.parseInt;
                    delete item.cols;
                    delete item.displayTotalRow;
                    if (item.type == "list") {
                        this.$set(item, "list", [
                            {
                                count: 0,
                                name: "",
                            },
                        ]);
                        this.$set(item, "mode", 0);
                    } else if (item.type == "customList") {
                        //
                    }
                    break;
                case "date":
                    delete item.list;
                    delete item.mode;
                    delete item.options;
                    delete item.other;
                    delete item.mul;
                    delete item.files;
                    delete item.Dbs;
                    delete item.filteredDb;
                    delete item.value;
                    delete item.preset;
                    delete item.compute;
                    delete item.parseInt;
                    delete item.cols;
                    delete item.displayTotalRow;
                    this.$set(item, "date", {
                        selected: "free",
                        end: null,
                        start: null,
                        default: null,
                        day: null,
                    });
                    break;
                case "document":
                    delete item.list;
                    delete item.mode;
                    delete item.date;
                    delete item.options;
                    delete item.other;
                    delete item.mul;
                    delete item.Dbs;
                    delete item.filteredDb;
                    delete item.value;
                    delete item.preset;
                    delete item.compute;
                    delete item.cols;
                    delete item.parseInt;
                    delete item.displayTotalRow;
                    item.name = item.selected.fname;
                    this.$set(item, "files", []);
                    break;
                case "database":
                    delete item.list;
                    delete item.mode;
                    delete item.date;
                    delete item.options;
                    delete item.other;
                    delete item.mul;
                    delete item.files;
                    delete item.value;
                    delete item.preset;
                    delete item.compute;
                    delete item.parseInt;
                    delete item.cols;
                    delete item.displayTotalRow;
                    item.name = item.selected.fname;
                    this.$set(item, "filteredDb", []);
                case "total":
                    delete item.list;
                    delete item.mode;
                    delete item.date;
                    delete item.options;
                    delete item.other;
                    delete item.mul;
                    delete item.files;
                    delete item.value;
                    this.$set(item, "compute", "+");
                    this.$set(item, "parseInt", 1);
                    this.$set(item, "value", null);
                    this.$set(item, "cols", {
                        col1: null,
                        col2: null,
                    });
                    this.$set(item, "displayTotalRow", false);
            }
        },
        addCol() {
            this.change = 1;
            let temp = {
                type: "",
                id:
                    this.settingForm.length == 0
                        ? 1
                        : Math.max(...this.settingForm.map((el) => el.id)) + 1,
            };
            this.settingForm.push(temp);
        },
        addSelect(state, item) {
            this.change = 1;
            let temp = {
                id: nanoid(),
                name: state == 0 ? "其他" : "",
            };
            let cascade_temp = {
                id: nanoid(),
                name: "",
                iname: "",
            };

            if (state == 0) {
                item.options.splice(item.options.length, 0, temp);
                this.$set(temp, "option", "");
                item.other = 1;
            } else {
                if (item.other == 1) {
                    item.options.splice(item.options.length - 1, 0, temp);
                } else {
                    if (item.type == "cascade") {
                        item.options.push(cascade_temp);
                    } else {
                        item.options.push(temp);
                    }
                }
            }
        },
        saveOpHint() {
            this.$set(
                this.settingForm[this.columnIndex].options[this.optionIndex],
                "prompt",
                this.optionCommit
            );
            this.optionCommit = "";
        },
        showWarningToast({
            severity = "error",
            summary = "Default summary",
            life = 3000,
        } = {}) {
            this.$refs.toast.add({
                severity,
                summary,
                life,
            });
        },
        checkIfTheColumnHasFlowSetting(item_index) {
            let hasFlowSetting = false;
            const signRoleIdSet = new Set(
                this.settingSignRoles.map((item) => item.id)
            );

            if (Array.isArray(this.settingForm[item_index].options)) {
                hasFlowSetting = this.settingForm[item_index].options.some(
                    (item) => signRoleIdSet.has(item.id)
                );
            }

            return hasFlowSetting;
        },
        checkIfTheColumnHasAmountJudgementOnFlowSetting(item_index) {
            let hasAmountJudgement = false;

            hasAmountJudgement = this.settingSignRoles
                .flatMap((item) => item.roles)
                .some((role) => {
                    return (
                        role.limit_dollar &&
                        this.settingForm[item_index].id === role.dollar_col_id
                    );
                });

            return hasAmountJudgement;
        },
        checkIfTheColumnHasAmountJudgementOnCommonSetting(item_index) {
            let hasAmountJudgement = false;

            hasAmountJudgement = this.settingSignRoles.some((role) => {
                return (
                    role.limit_dollar &&
                    this.settingForm[item_index].id === role.dollar_col_id
                );
            });

            return hasAmountJudgement;
        },
        checkIfCanDeleteColumn(item_index) {
            let canDelete = true;

            // 若欄位選項已被設定為流程選項，則不可刪除
            if (this.checkIfTheColumnHasFlowSetting(item_index)) {
                this.showWarningToast({
                    severity: "error",
                    summary: "此欄位已設定選項流程，無法刪除",
                });
                canDelete = false;
            }

            // 當前簽核設定為依選項流程
            if (this.flowSettingType === 1) {
                // 如有設定金額判斷的欄位，則不可刪除
                if (
                    this.checkIfTheColumnHasAmountJudgementOnFlowSetting(
                        item_index
                    )
                ) {
                    this.showWarningToast({
                        severity: "error",
                        summary: "此欄位已設定金額判斷，無法刪除",
                    });
                    canDelete = false;
                }
            } else {
                // 如有設定金額判斷的欄位，則不可刪除
                if (
                    this.checkIfTheColumnHasAmountJudgementOnCommonSetting(
                        item_index
                    )
                ) {
                    this.showWarningToast({
                        severity: "error",
                        summary: "此欄位已設定金額判斷，無法刪除",
                    });
                    canDelete = false;
                }
            }

            return canDelete;
        },
        deleteCol(item_index) {
            if (!this.checkIfCanDeleteColumn(item_index)) return;

            this.change = 1;
            this.settingForm.splice(item_index, 1);
        },
        deleteSelect(item, options_index) {
            this.change = 1;
            if (item.other == 1 && item.options[options_index].name == "其他") {
                item.other = 0;
            }
            item.options.splice(options_index, 1);
        },
        openList(item, item_index) {
            this.columnIndex = item_index;
            if (item.type == "list") {
                if (item.id == this.settingForm[item_index].id) {
                    this.menuForm = item.list;
                    this.listId = this.settingForm[item_index].list_id;
                }
                this.displayModal = true;
            } else if (item.type == "customList") {
                this.displayModal6 = true;
            }
        },
        cancelList() {
            this.initMenuForm();
            this.displayModal = false;
        },
        menuTotal(item) {
            item.total = (item.count * 10000 * item.price) / 10000 || 0;
            return item.total;
        },
        finallyTotal() {
            let menu_totals = this.menuForm.map((form) => form.total);
            return menu_totals.reduce((a, m) => {
                return a + m || 0;
            });
        },
        initMenuForm() {
            this.menuForm = [
                {
                    name: "",
                    count: 0,
                },
            ];
        },
        selectDate(item) {
            this.change = 1;
            if (item.date.selected !== "auto") {
                item.date.day = null;
                delete item.value;
            } else {
                item.date.day = 0;
                return (item.value = new Date());
            }
        },
        saveList() {
            let len = this.listRows.length;
            let id = this.settingForm[this.columnIndex].id;
            let menu_form = JSON.stringify(this.menuForm);

            let row = {
                column_id: id,
                menu_form: menu_form,
            };

            if (len == 0) {
                this.$set(this.listRows, len, row);
                //this.initMenuForm();
            }
            let hasColumnId = Object.values(this.listRows).some(
                (item, index) => {
                    return item.column_id == this.columnIndex + 1;
                }
            );

            if (hasColumnId) {
                Object.values(this.listRows).forEach((item, index) => {
                    if (item.column_id == id) {
                        item.menu_form = JSON.stringify(this.menuForm);
                    }
                });
            } else {
                this.$set(this.listRows, len, row);
            }
        },
        listModeChange() {
            if (
                !("mode" in this.settingForm[this.columnIndex]) ||
                this.settingForm[this.columnIndex].mode == 0
            ) {
                this.$set(this.settingForm[this.columnIndex], "mode", 1);
                this.$set(
                    this.settingForm[this.columnIndex],
                    "list_form",
                    this.settingForm[this.columnIndex].list
                );
                this.menuForm = [{}];
                this.settingForm[this.columnIndex].list = [{}];
            } else {
                this.menuForm = this.settingForm[this.columnIndex].list_form;
                this.settingForm[this.columnIndex].list =
                    this.settingForm[this.columnIndex].list_form;
                this.settingForm[this.columnIndex].mode = 0;
            }
        },
        ListColSwitch(tag, title) {
            this.menuForm.forEach((m, index) => {
                if (tag == 1) {
                    if (title == "total") {
                        this.$set(m, title, m.count * m.price);
                    } else {
                        this.$set(
                            m,
                            title,
                            title == "price" || title == "count" ? 0 : ""
                        );
                    }
                } else {
                    this.$delete(m, title, index);
                }
            });
            this.settingForm[this.columnIndex].list.forEach((l, index) => {
                if (tag == 1) {
                    if (title == "total") {
                        this.$set(l, title, l.count * l.price);
                    } else {
                        this.$set(
                            l,
                            title,
                            title == "price" || title == "count" ? 0 : ""
                        );
                    }
                } else {
                    this.$delete(l, title, index);
                }
            });
        },
        saveImport(id) {
            let param = {
                list_import: this.imports.demands,
                layout_id: id,
                old_layout_id: this.settingId,
            };
            axios
                .put(this.apiSettingURL + "/import", param)
                .then((response) => {
                    let dataRow = response.data;
                    if (!dataRow) {
                        this.$refs.toast.add({
                            severity: "error",
                            summary: "儲存失敗",
                            life: 3000,
                        });
                    } else {
                        this.imports.demands = [];
                    }
                })
                .catch((error) => {
                    console.error(error);
                    if (error.response) {
                        this.$refs.toast.add({
                            severity: "error",
                            summary: "欄位帶入儲存失敗!",
                            life: 3000,
                        });
                    }
                });
            return 1;
        },
        saveListToDB(id) {
            let param = {
                list_rows: this.listRows,
                layout_id: id, //
            };
            axios
                .put(this.apiSettingURL + "/list", param)
                .then((response) => {
                    let dataRow = response.data;
                    if (dataRow) {
                        this.listId = response.data.id;
                        // this.getLayoutSetting(this.settingId);
                        this.listRows = [];
                    }
                })
                .catch((error) => {
                    console.error(error);
                    if (error.response) {
                        this.$refs.toast.add({
                            severity: "error",
                            summary: "項目清單儲存失敗!",
                            life: 3000,
                        });
                    }
                });
            return 1;
        },
        //新增項目
        addItemList() {
            this.menuForm.push({ name: "", count: 0 });
        },
        totalCompute() {
            this.settingForm.forEach((form) => {
                if (form.type == "total") {
                    if (form.compute == "+" || form.compute == "-") {
                        var r1, r2, m, n;
                        try {
                            r1 = form.cols.col1.value
                                .toString()
                                .split(".")[1].length;
                        } catch (e) {
                            r1 = 0;
                        }
                        try {
                            r2 = form.cols.col2.value
                                .toString()
                                .split(".")[1].length;
                        } catch (e) {
                            r2 = 0;
                        }
                        m = Math.pow(10, Math.max(r1, r2));
                        n = r1 >= r2 ? r1 : r2;
                    }
                    if (form.compute == "+") {
                        form.value = Number(
                            (
                                ((form.cols.col1 !== null &&
                                form.cols.col1.value !== null
                                    ? form.cols.col1.value
                                    : 0) *
                                    m +
                                    (form.cols.col2 !== null &&
                                    form.cols.col2.value !== null
                                        ? form.cols.col2.value
                                        : 0) *
                                        m) /
                                m
                            ).toFixed(n)
                        );
                    } else if (form.compute == "-") {
                        form.value = Number(
                            (
                                ((form.cols.col1 !== null &&
                                form.cols.col1.value !== null
                                    ? form.cols.col1.value
                                    : 0) *
                                    m -
                                    (form.cols.col2 !== null &&
                                    form.cols.col2.value !== null
                                        ? form.cols.col2.value
                                        : 0) *
                                        m) /
                                m
                            ).toFixed(n)
                        );
                    } else if (form.compute == "*") {
                        var m = 0,
                            s1 =
                                form.cols.col1 !== null &&
                                form.cols.col1.value !== null &&
                                "value" in form.cols.col1
                                    ? form.cols.col1.value.toString()
                                    : "0",
                            s2 =
                                form.cols.col2 !== null &&
                                form.cols.col2.value !== null &&
                                "value" in form.cols.col2
                                    ? form.cols.col2.value.toString()
                                    : "0";
                        try {
                            m += s1.split(".")[1].length;
                        } catch (e) {}
                        try {
                            m += s2.split(".")[1].length;
                        } catch (e) {}
                        form.value =
                            (Number(s1.replace(".", "")) *
                                Number(s2.replace(".", ""))) /
                            Math.pow(10, m);
                    } else if (form.compute == "/") {
                        var t1 = 0,
                            t2 = 0,
                            r1,
                            r2;
                        try {
                            t1 = form.cols.col1.value
                                .toString()
                                .split(".")[1].length;
                        } catch (e) {
                            t1 = "0";
                        }
                        try {
                            t2 = form.cols.col2.value
                                .toString()
                                .split(".")[1].length;
                        } catch (e) {
                            t2 = "0";
                        }
                        r1 =
                            form.cols.col1 !== null &&
                            form.cols.col1.value !== null &&
                            "value" in form.cols.col1
                                ? Number(
                                      form.cols.col1.value
                                          .toString()
                                          .replace(".", "")
                                  )
                                : 0;
                        r2 =
                            form.cols.col2 !== null &&
                            form.cols.col2.value !== null &&
                            "value" in form.cols.col1
                                ? Number(
                                      form.cols.col2.value
                                          ?.toString()
                                          .replace(".", "") || 0
                                  )
                                : 0;
                        form.value = (r1 / r2) * Math.pow(10, t2 - t1);
                    }
                }
            });
        },
        // ------以下匯入匯出
        triggerInput() {
            this.$refs.fileInput.click();
        },
        outputExcel() {
            if (this.listId == undefined) {
                this.$refs.toast.add({
                    severity: "error",
                    summary: "需求單未儲存無法匯出!",
                    life: 3000,
                });
            } else {
                window.open(
                    this.apiSettingURL + "/list/export?id=" + this.listId
                );
            }
        },
        inputExcel(e) {
            this.formData.append("file", e.target.files[0]);
            this.formData.append("id", this.listId);
            axios
                .post(this.apiSettingURL + "/list/import", this.formData)
                .then((response) => {
                    if (response.data.success) {
                        this.$refs.toast.add({
                            severity: "success",
                            summary: "匯入成功",
                            life: 3000,
                        });
                        this.menuForm = response.data.menu_form;
                        this.settingForm[this.columnIndex].list =
                            response.data.menu_form;
                    } else {
                        this.$refs.toast.add({
                            severity: "error",
                            summary:
                                this.listId == undefined
                                    ? "需求單未儲存無法匯入"
                                    : response.data.errors[0], //後端做錯誤訊息
                            life: 3000,
                        });
                    }
                });
        },
        importList() {
            this.displayModal1 = !this.displayModal1;
            const newLists = [];
            this.dataList.forEach((list) => {
                list.layouts.map((layout) => {
                    delete layout.audit_musts;
                    delete layout.open;
                    delete layout.sign_role;
                    this.$set(layout, "models", [
                        {
                            cols: [],
                            col: {},
                            col_id: null,
                            demand_col: {},
                            demand_col_id: null,
                        },
                    ]);
                    newLists.push(layout);
                });
            });
            this.$set(this.imports, "demandLists", newLists);
        },
        getImport(id) {
            axios
                .get(this.apiSettingURL + "/import", {
                    params: { layout_id: id },
                })
                .then((response) => {
                    let dataRow = response.data;
                    if (dataRow) {
                        this.imports.demands = dataRow;
                    }
                })
                .catch((error) => {
                    console.error(error);
                    this.$refs.toast.add({
                        severity: "error",
                        summary: "帶入需求單資訊獲取失敗!",
                        life: 3000,
                    });
                    this.backToPreviousPage();
                });
        },
        selectImport() {
            const turnBack = () => {
                this.displayModal2 = false;
            };

            // 沒選就按確定
            if (!this.imports.demand) {
                turnBack();
                return;
            }

            this.imports.demands.push(
                JSON.parse(JSON.stringify(this.imports.demand))
            );
            turnBack();
        },
        getImportSetting(id, index, col, tr_index) {
            axios
                .get("/api/demand/submit/original-layout", {
                    params: { layout_id: id },
                })
                .then((response) => {
                    let dataRow = response.data;
                    if (dataRow) {
                        this.imports.demands[index].models[tr_index].cols =
                            dataRow.columns
                                .map((el, index) => {
                                    el.col_no = index + 1;
                                    return el;
                                })
                                .filter((c) => {
                                    return c.type == col.type;
                                });
                        this.imports.demands[index].models[
                            tr_index
                        ].demand_col_id = col.id;
                    }
                })
                .catch((error) => {
                    console.error(error);
                    this.$refs.toast.add({
                        severity: "error",
                        summary: "獲取失敗!",
                        life: 3000,
                    });
                });
        },
        changeImport(col, index, d_col, tr_index) {
            this.imports.demands[index].models[tr_index].col_id = col.id;
            if ("options" in col) {
                if (
                    JSON.stringify(d_col.options) !==
                    JSON.stringify(col.options)
                ) {
                    this.$refs.toast.add({
                        severity: "error",
                        summary: "選項不同無法帶入!",
                        life: 3000,
                    });
                    this.imports.demands[index].models[tr_index].col = {};
                    this.imports.demands[index].models[tr_index].col_id = null;
                }
            }
        },
        uploadFile(e, type) {
            const file = e.target.files;
            const validExcelFileName = ["xls", "xlsx", "XLS", "XLSX"];
            const validWordFileName = ["docx", "DOCX"];

            // 沒選到檔案時取消
            if (!file.length) {
                return;
            }

            // 檔案大於10MB時取消
            if (file[0].size > 10485760) {
                this.$refs.toast.add({
                    severity: "warn",
                    summary: "檔案大於10MB",
                    life: 3000,
                });
                e.target.value = "";
                return;
            }

            // 不適用的檔案類型
            if (type == "excel") {
                if (
                    !validExcelFileName.includes(
                        file[0].name.split(".").slice(-1)[0]
                    )
                ) {
                    this.$refs.toast.add({
                        severity: "warn",
                        summary: "檔案類型錯誤",
                        life: 3000,
                    });
                    e.target.value = "";
                    return;
                }
            } else {
                if (
                    !validWordFileName.includes(
                        file[0].name.split(".").slice(-1)[0]
                    )
                ) {
                    this.$refs.toast.add({
                        severity: "warn",
                        summary: "檔案類型錯誤",
                        life: 3000,
                    });
                    e.target.value = "";
                    return;
                }
            }

            const form = new FormData();
            form.append("files0", file[0]);
            form.append("fileName0", file[0].name);

            const options = {
                method: "POST",
                headers: { "content-type": "multipart/form-data" },
                data: form,
                url: "/api/demand/upload/templete",
            };
            axios(options)
                .then((response) => {
                    if (response !== "") {
                        this.file =
                            type === "excel"
                                ? { ...this.file, excel: response.data[0] }
                                : { ...this.file, word: response.data[0] };
                        this.$refs.toast.add({
                            severity: "success",
                            summary: "上傳成功",
                            life: 3000,
                        });
                    } else {
                        this.$refs.toast.add({
                            severity: "error",
                            summary: "上傳失敗",
                            life: 3000,
                        });
                    }
                })
                .catch((error) => {
                    console.error(error);
                    this.$refs.toast.add({
                        severity: "error",
                        summary: "上傳失敗!",
                        life: 3000,
                    });
                });
        },
        deleteTemp(type) {
            axios
                .post("/api/demand/delete/temp", {
                    base_name: this.file[type].base_name,
                })
                .then((response) => {
                    if (type === "excel") {
                        if (Object.keys(this.file).length === 2) {
                            this.file = { word: this.file.word };
                        } else {
                            this.file = null;
                        }
                        this.$refs.fileTemplete_excel.value = "";
                    } else {
                        if (Object.keys(this.file).length === 2) {
                            this.file = { excel: this.file.excel };
                        } else {
                            this.file = null;
                        }
                        this.$refs.fileTemplete_word.value = "";
                    }
                    this.$refs.toast.add({
                        severity: "success",
                        summary: "刪除成功",
                        life: 3000,
                    });
                })
                .catch((error) => {
                    console.error(error);
                    this.$refs.toast.add({
                        severity: "error",
                        summary: "刪除失敗",
                        life: 3000,
                    });
                });
        },
        deleteImports(index) {
            this.imports.demands.splice(index, 1);
            this.forceUpdate_TabView += 1;
        },
        updateDefault(value, item) {
            if (value) {
                item.preset = 1;
                item.default = value;
            } else {
                item.preset = 0;
                item.default = undefined;
            }
        },
        isOptionDisabled(option, index) {
            // 取得所有已被選的 id，排除目前這一列的
            const selectedIds = this.imports.demands[index].models.map(
                (model) => model.demand_col.id
            );

            return selectedIds.includes(option.id);
        },
        triggerUpload(index) {
            this.$refs.fileInputs[index].click();
        },
        remarkFileChange(event, index) {
            const file = event.target.files[0];

            // 檔案大於10MB時取消
            if (file.size > 10 * 1024 * 1024) {
                this.$refs.toast.add({
                    severity: "warn",
                    summary: "檔案大於10MB",
                    life: 3000,
                });
                this.$set(this.remarks[index], "file", null); // init state
                event.target.value = ""; // clear input tag
                return;
            }

            const form = new FormData();
            form.append("file0", file);
            form.append("fileName0", file.name);

            const options = {
                method: "POST",
                headers: { "content-type": "multipart/form-data" },
                data: form,
                url: "/api/demand/upload/temp",
            };
            axios(options)
                .then((response) => {
                    this.remarks[index] = {
                        ...this.remarks[index],
                        file: response.data[0],
                    };
                    this.$forceUpdate();
                    this.$refs.toast.add({
                        severity: "success",
                        summary: "上傳成功",
                        life: 3000,
                    });
                })
                .catch((error) => {
                    if (error.response.data.message == "Invalid file type") {
                        this.$refs.toast.add({
                            severity: "error",
                            summary: "檔案類型不適用",
                            life: 3000,
                        });
                    } else {
                        this.$refs.toast.add({
                            severity: "error",
                            summary: "上傳失敗!",
                            life: 3000,
                        });
                    }
                    // 清空 input
                    if (this.$refs.fileInputs) {
                        this.$refs.fileInputs.value = "";
                    }
                });
        },
        removeFile(index) {
            if (!this.remarks[index]?.file) {
                return;
            }

            axios
                .post("/api/demand/delete/temp", {
                    base_name: this.remarks[index].file.base_name,
                })
                .then(() => {
                    this.$set(this.remarks[index], "file", null);
                    this.$forceUpdate();
                    this.$refs.toast.add({
                        severity: "success",
                        summary: "刪除成功",
                        life: 3000,
                    });
                })
                .catch((error) => {
                    console.error(error);
                    this.$refs.toast.add({
                        severity: "error",
                        summary: "刪除失敗!",
                        life: 3000,
                    });
                });
        },
    },
    computed: {
        importableForm() {
            return this.settingForm
                .filter(
                    (form) => form.type !== "list" && form.type !== "document"
                )
                .map((e) => {
                    e.disabled = true;
                    return e;
                });
        },
    },
};
</script>
<style>
.p-dialog {
    width: 30rem;
}
.p-dialog .p-dialog-footer button {
    width: 7rem !important;
}
.importSelectDialog .p-dialog-content {
    height: 28rem;
    padding-bottom: 0px !important;
}
.p-tabview-nav {
    height: 60px;
    overflow-x: auto;
}
.p-tabview .p-tabview-panels {
    padding: 0;
}
.visuallyhidden {
    border: 0;
    clip: rect(0 0 0 0);
    height: 1px;
    margin: -1px;
    overflow: hidden;
    padding: 0;
    position: absolute;
    width: 1px;
}
.customDialog .p-dialog .p-dialog-content {
    padding-right: 0px;
    padding-left: 0px;
}
@media (min-width: 768px) {
}
</style>
