<template>
  <div>
    <li
      v-for="item in data"
      :key="item.key"
      style="color: #1d2939"
      class="mb-1 last:mb-0 whitespace-nowrap"
    >
      <div
        class="py-1 w-full flex items-center hover:bg-blue-100 transition"
        style="line-height: 160%"
        :style="{ paddingLeft: padding + 'px' }"
        :class="item.isActive && 'bg-blue-100'"
      >
        <button
          v-if="item.children"
          @click="item.isExpand = !item.isExpand"
          class="w-4"
        >
          <img
            v-if="item.isExpand"
            class="w-full"
            src="@images/icon/arrow-down.svg"
          />
          <img v-else class="w-full" src="@images/icon/arrow-right.svg" />
        </button>
        <div
          class="flex flex-grow items-center justify-start mr-4 cursor-pointer"
          @click="enterFolder(item.id)"
        >
          <img
            class="w-3.5"
            :class="item.children ? ' ml-2' : 'ml-6'"
            src="@images/icon/folder.png"
          />
          <span class="ml-2">{{ item.name }}</span>
        </div>
      </div>
      <ul v-if="item.children" v-show="item.isExpand" class="bg-white">
        <DirectoryTree
          :data="item.children"
          :nextPathIds="nextPathIds"
          :padding="computedPadding"
        />
      </ul>
    </li>
  </div>
</template>
<script>
export default {
  name: "DirectoryTree",
  props: {
    data: { type: Array, required: true },
    padding: { type: Number, default: 16 },
    nextPathIds: { type: Array, required: true },
  },
  inject: [
    "fetchDataByFolderId",
    "changeDirActive",
    "checkFolderPermission",
    "updatePrevPathIds",
  ],
  methods: {
    enterFolder(id) {
      if (!this.checkFolderPermission(id)) return;
      this.updatePrevPathIds(id);
      this.fetchDataByFolderId(id);
      this.nextPathIds.length = 0;
    },
  },
  computed: {
    computedPadding() {
      return this.padding + 24;
    },
  },
};
</script>
