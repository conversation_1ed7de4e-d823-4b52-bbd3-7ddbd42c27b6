<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use App\Modules\explorer\Models\OcrFileRule;
use GuzzleHttp\Exception\BadResponseException;
use GuzzleHttp\Client;
use App\Modules\Demand\Models\Notification;
use Exception;
use Storage;

class UploadToServiceJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $fileOriginalName, $fileName, $company_id, $user_id, $isOcr, $file_url, $patchNo;

    /**
     * Create a new job instance.
     *
     * @param boolean $type
     * @return void
     */
    public function __construct($fileOriginalName, $fileName, $company_id, $user_id, $patchNo, $isOcr = true)
    {
        $this->fileOriginalName = $fileOriginalName;
        $this->fileName = $fileName;
        $this->company_id = $company_id;
        $this->user_id = $user_id;
        $this->isOcr = $isOcr;
        $this->patchNo = $patchNo;
        $this->file_url = config("app.services_file_api_url");
    }
    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        \Log::info('upload_to_sf_start');
        try {
            //抓取檔案
            $file = Storage::disk('public')->get("explorer/temp/$this->fileName");

            // 取得ocr_File_Rule
            $filRule = OcrFileRule::query()
                ->user()
                ->first();

            $enable_id = $filRule->enable_id;
            $enableRule = $filRule->rules->first(function ($value) use ($enable_id) {
                return $value['id'] == $enable_id;
            });

            //打api到service-file
            $client = new Client();

            $url = $this->urlScope($this->file_url . '/ocr-upload');
            $options = [
                'multipart' => [
                    [
                        'Content-type' => 'multipart/form-data',
                        'name' => 'file',
                        'contents' => $file,
                        'filename' => $this->fileName,
                    ],
                    ['name' => 'fileOriginalName', 'contents' => $this->fileOriginalName],
                    ['name' => 'fileRule', 'contents' => json_encode($enableRule)],
                    ['name' => 'isOcr', 'contents' => $this->isOcr],
                    ['name' => 'patchNo', 'contents' => $this->patchNo],
                ],
            ];

            $response = $client->post($url, $options);
            // $result = json_decode((string) $response->getBody());
        } catch (Exception $e) {
            Notification::query()
                ->where('payload->patc_no', $this->patchNo)
                ->update([
                    'metadata' => [
                        'response' => $e->getMessage()
                    ]
                ]);
            \Log::error(__CLASS__);

            if ($e instanceof BadResponseException) {
                $msg = json_decode($e->getResponse()->getBody()->getContents())->msg;
                \Log::error($msg);
            }
            \Log::error($e);
        }
        \Log::info('upload_to_sf_end');
    }

    public function urlScope($url)
    {
        return "$url?ap=ASAP&company_id=$this->company_id&employee_id=$this->user_id";
    }


}
