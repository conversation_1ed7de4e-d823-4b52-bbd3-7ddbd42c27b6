<template>
    <div class="mr-20 2xl:mr-24 pb-10">
        <Toast ref="toast" position="top-center" />
        <label v-if="item.name !== '' && item.name !== null">
            {{ item.name }}
        </label>
        <label v-else>
            <span v-if="item.type == 'money'">輸入題(金額)</span>
            <span v-else-if="item.type == 'input'">輸入題(文數字)</span>
            <span v-else-if="item.type == 'dropdown'">下拉選項題</span>
            <span v-else-if="item.type == 'single'">單選題</span>
            <span v-else-if="item.type == 'multi'">複選題</span>
            <span v-else-if="item.type == 'employee'">員工下拉選單</span>
            <span v-else-if="item.type == 'list'">項目清單</span>
            <span v-else-if="item.type == 'time'">時間選擇</span>
            <span v-else-if="item.type == 'date'">日期選擇</span>
        </label>
        <span v-if="item.must && !item.audit_must">*</span>
        <br />
        <div v-if="item.type == 'money'" class="demandItemWidth">
            <InputNumber
                v-model="item.value"
                placeholder="請輸入金額"
                class="w-full"
                :class="{ 'p-invalid': item.invalid && $parent.validated }"
                :disabled="item.audit_must || (item.must && item.audit_must)"
                :minFractionDigits="item.value == 0 ? 0 : 2"
                @input="$delete(item, 'invalid')"
            />
        </div>
        <div v-else-if="item.type == 'input'" class="demandItemWidth">
            <InputText
                v-model="item.value"
                placeholder="請輸入"
                class="w-full"
                :class="{ 'p-invalid': item.invalid && $parent.validated }"
                :disabled="item.audit_must || (item.must && item.audit_must)"
                @input="$delete(item, 'invalid')"
            />
        </div>
        <div v-else-if="item.type == 'total'" class="demandItemWidth">
            <InputText
                v-model="item.value"
                placeholder=""
                :disabled="true"
                class="w-full"
            />
        </div>
        <div v-else-if="item.type == 'number'" class="demandItemWidth">
            <InputNumber
                v-model="item.value"
                placeholder="請輸入"
                class="w-full"
                :class="{ 'p-invalid': item.invalid && $parent.validated }"
                :disabled="item.audit_must"
                :maxFractionDigits="4"
                @input="$delete(item, 'invalid')"
            />
        </div>
        <div v-else-if="item.type == 'dropdown'" class="demandItemWidth">
            <Dropdown
                v-if="!('mul' in item) || item.mul !== 1"
                v-model="item.value"
                :options="item.options"
                optionLabel="name"
                placeholder="選項"
                class="w-full"
                :class="{ 'p-invalid': item.invalid && $parent.validated }"
                :disabled="item.audit_must || (item.must && item.audit_must)"
                @input="$delete(item, 'invalid')"
                @change="openList(item)"
            />
            <MultiSelect
                v-else
                v-model="item.value"
                :options="item.options"
                optionLabel="name"
                optionValue="name"
                placeholder="選項"
                class="w-full"
                :class="{
                    'p-invalid': item.invalid && $parent.validated,
                    MultiSelect: true,
                }"
                :disabled="item.audit_must || (item.must && item.audit_must)"
                @input="$delete(item, 'invalid')"
            />
        </div>
        <div v-else-if="item.type == 'cascade'" class="demandItemWidth">
            <Cascadeselect
                v-model="item.value"
                :options="item.options"
                :optionGroupChildren="['states']"
                optionLabel="iname"
                optionGroupLabel="gname"
                :placeholder="'value' in item ? item.value : '選項'"
                class="w-full"
                :class="{ 'p-invalid': item.invalid && $parent.validated }"
                @input="$delete(item, 'invalid')"
                @group-change="item.group = $event.value.name"
                @change="
                    'name' in $event.value
                        ? (item.value = $event.value.iname)
                        : (item.value =
                              ('group' in item ? item.group + ',' : '') +
                              $event.value.iname);
                    $delete(item, 'group');
                    openList(item);
                "
            />
        </div>
        <div
            v-else-if="item.type == 'single'"
            class="flex flex-wrap demandItemWidth"
        >
            <div
                v-for="(option, op_index) of item.options"
                :key="option.id"
                class="mr-2"
                @dblclick="$delete(item, 'value')"
            >
                <label class="cursor-pointer">
                    <RadioButton
                        name="option"
                        :value="option"
                        v-model="item.value"
                        class="mb-1"
                        :class="{
                            'p-invalid': item.invalid && $parent.validated,
                        }"
                        @input="$delete(item, 'invalid')"
                        @change="choose(item)"
                    />
                    {{ option.name }}</label
                >
                <input
                    v-model="option.option"
                    v-if="
                        item.other == 1 && op_index === item.options.length - 1
                    "
                    type="text"
                    class="ml-1 border-b px-1"
                />
            </div>
        </div>
        <div
            v-else-if="item.type == 'multi'"
            class="flex flex-wrap demandItemWidth"
        >
            <div
                v-for="(option, op_index) of item.options"
                :key="option.id"
                class="mr-2"
            >
                <Checkbox
                    :id="option.id"
                    name="option"
                    :value="option"
                    v-model="item.value"
                    class="mb-1 mr-1"
                    :class="{ 'p-invalid': item.invalid && $parent.validated }"
                    @input="$delete(item, 'invalid')"
                />
                <label :for="option.id" class="cursor-pointer">{{
                    option.name
                }}</label>
                <input
                    v-model="option.option"
                    v-if="
                        item.other == 1 && op_index === item.options.length - 1
                    "
                    type="text"
                    class="ml-1 border-b px-1"
                />
            </div>
        </div>
        <div v-else-if="item.type == 'employee'" class="demandItemWidth">
            <Dropdown
                v-model="item.value"
                :options="
                    'getEmployees' in $parent
                        ? $parent.getEmployees
                        : item.options
                "
                :filter="true"
                optionLabel="name"
                data-key="id"
                placeholder="選擇人員"
                class="w-full"
                :class="{ 'p-invalid': item.invalid && $parent.validated }"
                @input="$delete(item, 'invalid')"
            >
                <template #value="slotProps">
                    <div v-if="slotProps.value">
                        <span>{{ slotProps.value.org_name }}</span>
                        <span>{{ slotProps.value.job_title }}</span>
                        <span>{{ slotProps.value.name }}</span>
                    </div>
                    <span v-else>
                        {{ slotProps.placeholder }}
                    </span>
                </template>
                <template #option="slotProps">
                    <div>
                        <span>{{ slotProps.option.org_name }}</span>
                        <span>{{ slotProps.option.job_title }}</span>
                        <span>{{ slotProps.option.name }}</span>
                    </div>
                </template>
            </Dropdown>
        </div>
        <div
            v-else-if="item.type == 'list' || item.type == 'customList'"
            class="demandItemWidth"
        >
            <Button
                @click="openList(item)"
                :label="item.type == 'list' ? '項目清單' : '自訂表單'"
                class="p-button-outlined p-button-secondary w-28"
            />
        </div>
        <div v-else-if="item.type == 'time'" class="demandItemWidth">
            <Calendar
                v-model="item.value"
                :timeOnly="true"
                hourFormat="12"
                placeholder="請輸入時間"
                :manualInput="false"
                :class="{ 'p-invalid': item.invalid && $parent.validated }"
                :disabled="item.audit_must || (item.must && item.audit_must)"
                @input="$delete(item, 'invalid')"
            />
        </div>
        <div v-else-if="item.type == 'date'" class="demandItemWidth">
            <Calendar
                v-model="item.value"
                :minDate="item.date.selected == 'future' ? new Date() : null"
                :maxDate="item.date.selected == 'pass' ? new Date() : null"
                :class="{ 'p-invalid': item.invalid && $parent.validated }"
                :disabled="item.audit_must || (item.must && item.audit_must)"
                placeholder="請輸入日期"
                dateFormat="yy-mm-dd"
                :manualInput="false"
                @input="$delete(item, 'invalid')"
            />
        </div>
        <div v-else-if="item.type == 'document'" class="demandItemWidth">
            <Button
                @click="$refs.fileInput.click()"
                label="上傳附件"
                class="p-button-outlined p-button-secondary w-28"
            />
            <input
                type="file"
                class="hidden"
                ref="fileInput"
                accept=".jpg,.pdf,.jpeg,.heif,.hevc,.png,.HEIF,.HEVC,.xls,.xlsx"
                multiple="multiple"
                @change="uploadFile($event, item.files)"
            />
            <div class="overflow-auto block lg:inline-block">
                <div
                    v-for="(file, index) in item.files"
                    :key="index"
                    class="inline-block"
                >
                    <span class="flex mx-2">
                        <div class="w-20 truncate">
                            <a
                                :href="file.URL"
                                :title="file.name"
                                target="_blank"
                                >{{ file.name }}</a
                            >
                        </div>
                        <span
                            @click="deleteTemp(item.files, index)"
                            title="刪除附件"
                        >
                            <i class="fas fa-times-circle cursor-pointer"></i>
                        </span>
                    </span>
                </div>
            </div>
            <p class="text-xs text-gray-400 mt-3">
                檔案大小10MB以內，JPG、PDF檔、EXCEL檔
            </p>
            <!-- <FileUpload
                :multiple="true"
                :customUpload="true"
                :auto="true"
                :showUploadButton="false"
                :cancelLabel="'清除'"
                :chooseLabel="'選擇附件'"
                @uploader="myUploader($event, item.files)"
                accept=".jpg,.pdf,.jpeg,.heif,.hevc,.png,.HEIF,.HEVC"
                :maxFileSize="10000000"
                invalidFileSizeMessage="檔案上傳限制10MB以內"
                invalidFileTypeMessage="允許附件為JPG或PDF檔"
            >
                <template #empty>
                    <p>可將文件拖放到此處進行上傳</p>
                </template>
            </FileUpload> -->
        </div>
        <div v-else-if="item.type == 'database'" class="demandItemWidth">
            <AutoComplete
                ref="autoCompleteRef"
                :value="item.value"
                :suggestions="item.filteredDb"
                :class="{ 'p-invalid': item.invalid && $parent.validated }"
                @complete="setFilteredDb(item, $event)"
                @item-select="autoCompleteSelect(item, $event)"
                @input="autoCompleteInput(item, $event)"
            >
                <template #item="slotProps">
                    <div>
                        {{ slotProps.item.value }}
                        {{
                            item.name == "專案名稱"
                                ? getACCCodeNumber(slotProps.item.originIndex)
                                : ""
                        }}
                    </div>
                </template>
            </AutoComplete>
        </div>
        <div v-else class="demandItemWidth">
            <AutoComplete
                ref="autoCompleteRef"
                v-model="item.value"
                :suggestions="item.filteredDb"
                :class="{ 'p-invalid': item.invalid && $parent.validated }"
                @complete="autoComplete(item, $event)"
                @item-select="itemSelect(item)"
                @input="$delete(item, 'invalid')"
            />
        </div>
    </div>
</template>
<script>
import AutoComplete from "primevue/autocomplete";
import Button from "primevue/button";
import Calendar from "primevue/calendar";
import Cascadeselect from "primevue/cascadeselect";
import Checkbox from "primevue/checkbox";
import Dialog from "primevue/dialog";
import Dropdown from "primevue/dropdown";
import FileUpload from "primevue/fileupload";
import InputNumber from "primevue/inputnumber";
import InputText from "primevue/inputtext";
import MultiSelect from "primevue/multiselect";
import RadioButton from "primevue/radiobutton";
import Toast from "primevue/toast";
export default {
    props: {
        item: {
            type: Object,
            required: true,
        },
        formIndex: {
            type: Number,
            required: true,
        },
        flowType: {
            type: String,
            required: true,
        },
        flowColId: {
            type: Number,
            required: true,
        },
    },
    components: {
        AutoComplete,
        Button,
        Calendar,
        Cascadeselect,
        Checkbox,
        Dialog,
        Dropdown,
        FileUpload,
        InputNumber,
        InputText,
        MultiSelect,
        RadioButton,
        Toast,
    },
    data() {
        return {};
    },
    mounted() {
        if (this.item.type == "time") {
            if (!this.item.value) {
                this.$set(this.item, "value", new Date());
            }
        }
        if (
            (this.item.type == "date" || this.item.type == "time") &&
            typeof this.item.value === "string" &&
            this.$parent.layout_id !== undefined
        ) {
            this.item.value = new Date(this.item.dateTime);
        } else if (
            (this.item.type == "date" || this.item.type == "time") &&
            typeof this.item.value === "string"
        ) {
            this.item.value = new Date(this.item.value);
        }
        if (
            this.$parent.layout_id == undefined &&
            this.item.type == "date" &&
            this.item.date.selected == "auto"
        ) {
            let dateTime = new Date();
            this.item.value = new Date(
                dateTime.setDate(dateTime.getDate() + this.item.date.day)
            );
        }
        if (
            ((this.item.type == "single" || this.item.type == "dropdown") &&
                typeof this.item.value === "object") ||
            (this.item.type == "cascade" && this.item.value)
        ) {
            this.choose(this.item);
        }
    },
    methods: {
        choose(item) {
            if (this.flowType == item.type && this.flowColId == item.id) {
                this.$emit("choose", item);
            }
        },
        openList(item) {
            this.$emit("openList", item);
            this.choose(item);
        },
        autoCompleteInput(item, event) {
            this.$delete(item, "invalid");
            item.value = event;
        },
        autoCompleteSelect(item, event) {
            this.selectRelatedDb(item, event.value.originIndex);
            this.$set(item, "value", event.value.value);
            this.$forceUpdate();
        },
        getACCCodeNumber(dataIndex) {
            let accCodeNumber = this.$parent.forms[this.formIndex].find(
                (el) => el.name == "會計代號"
            )?.selected.data[dataIndex];
            return accCodeNumber ? `- ${accCodeNumber}` : "";
        },
        setFilteredDb(item, event) {
            if (!event.query.trim().length) {
                //input沒輸入時不顯示資料
            } else {
                item.filteredDb = item.selected.data
                    .map((value, originIndex) => ({ value, originIndex }))
                    .filter((col) => {
                        if (
                            typeof col.value === "string" ||
                            typeof col.value === "number"
                        ) {
                            var val = "" + col.value;
                            return val
                                .toLowerCase()
                                .includes(event.query.toLowerCase());
                        } else {
                            return col.value == event.query;
                        }
                    })
                    .slice(0, 150);
                // 卡個限制150筆(接近開始卡頓的程度)，不然過多的資料導致過長的渲染時間
            }
        },
        // 選取關聯欄位DB內容
        selectRelatedDb(item, index) {
            const dataIndex = index;
            if ("forms" in this.$parent) {
                this.$parent.forms[this.formIndex].forEach((col) => {
                    if (
                        "filteredDb" in col &&
                        col.selected.db_id == item.selected.db_id
                    ) {
                        this.$set(col, "value", col.selected.data[dataIndex]);
                    }
                });
            } else {
                this.$parent.settingForm.forEach((col) => {
                    if (
                        "filteredDb" in col &&
                        col.selected.db_id == item.selected.db_id
                    ) {
                        this.$set(col, "value", col.selected.data[dataIndex]);
                    }
                });
            }

            // 自行指定關卡依專案人員代入
            // 依選項設定流程
            const setting_sign_role = this.$parent.flow_sign_role
                ? this.$parent.flow_sign_role[0].roles
                : this.$parent.sign_role;

            // 有設定資料庫
            setting_sign_role
                .filter((role) => role.db_id)
                .forEach((role) => {
                    // 有開啟自行指定
                    if (role.self_applicant) {
                        const dbKey = `db${role.db_id}column${role.column_id}`;

                        // 自動填入
                        const autoFillData = () => {
                            const dataIndex = item.selected.data.indexOf(
                                item.value
                            );

                            const sign_person = this.$parent.getEmployees.find(
                                (employee) =>
                                    employee.employee_number ===
                                    this.$parent.db_data[dbKey].data[dataIndex]
                            );

                            this.$set(role, "role_id", sign_person?.id ?? 0);
                        };

                        // 還沒抓過資料
                        if (!this.$parent.db_data[dbKey]) {
                            this.fetchDatabaseData(role).then(() => {
                                autoFillData();
                            });
                        } else {
                            autoFillData();
                        }
                    }
                });
        },
        // 抓資料庫資料
        fetchDatabaseData(role) {
            return new Promise((resolve, reject) => {
                axios
                    .get("/api/demand/submit/layout/db", {
                        params: {
                            db_id: role.db_id,
                            column_id: role.column_id,
                        },
                    })
                    .then((res) => {
                        this.$parent.db_data[
                            "db" + role.db_id + "column" + role.column_id
                        ] = {
                            db_id: role.db_id,
                            column_id: role.column_id,
                            data: res.data,
                        };
                        resolve(res.data);
                    })
                    .catch((error) => {
                        console.error(error);
                        reject(error);
                    });
            });
        },
        deleteTemp(files, index) {
            axios
                .post("/api/demand/delete/temp", {
                    base_name: files[index].base_name,
                })
                .then((response) => {
                    files.splice(index, 1);
                    this.$refs.fileInput.value = "";
                    this.$refs.toast.add({
                        severity: "success",
                        summary: "刪除成功",
                        life: 3000,
                    });
                })
                .catch((error) => {
                    console.error(error);
                    this.$refs.toast.add({
                        severity: "error",
                        summary: "刪除失敗",
                        life: 3000,
                    });
                });
        },
        uploadFile(e, currentFiles) {
            const files = e.target.files;
            let accumulatedFilesSize = 0;
            const cumulativeFilesSize = currentFiles.reduce(
                (acc, cur) => acc + cur.size,
                0
            );
            const validFileName = [
                "jpg",
                "jpeg",
                "png",
                "pdf",
                "heif",
                "xlx",
                "xlsx",
                "JPG",
                "JPEG",
                "PNG",
                "PDF",
                "HEIF",
                "XLX",
                "XLSX",
            ];
            const form = new FormData();

            // 沒選到檔案時取消
            if (!files.length) {
                return;
            }

            for (var i = 0; i < files.length; i++) {
                accumulatedFilesSize += files[i].size;
                // 檔案大於10MB時取消
                if (cumulativeFilesSize + accumulatedFilesSize > 10485760) {
                    this.$refs.toast.add({
                        severity: "warn",
                        summary: "檔案大於10MB",
                        life: 3000,
                    });
                    e.target.value = "";
                    return;
                }

                // 不適用的檔案類型
                if (
                    !validFileName.includes(
                        files[i].name.split(".").slice(-1)[0]
                    )
                ) {
                    this.$refs.toast.add({
                        severity: "warn",
                        summary: "檔案類型錯誤",
                        life: 3000,
                    });
                    e.target.value = "";
                    return;
                }

                // 選到得檔案與已上傳檔案同名
                if (currentFiles.some((el) => el.name == files[i].name)) {
                    this.$refs.toast.add({
                        severity: "warn",
                        summary: "同名檔案已上傳",
                        life: 3000,
                    });
                    e.target.value = "";
                    return;
                }

                form.append("files" + i, files[i]);
                form.append("fileName" + i, files[i].name);
            }

            const options = {
                method: "POST",
                headers: { "content-type": "multipart/form-data" },
                data: form,
                url: "/api/demand/upload/temp",
            };

            axios(options)
                .then((response) => {
                    if (response !== "") {
                        for (let i = 0; i < response.data.length; i++) {
                            currentFiles.push(response.data[i]);
                        }
                        this.$refs.toast.add({
                            severity: "success",
                            summary: "上傳成功",
                            life: 3000,
                        });
                    } else {
                        this.$refs.toast.add({
                            severity: "error",
                            summary: "上傳失敗",
                            life: 3000,
                        });
                    }
                })
                .catch((error) => {
                    console.error(error);
                    this.$refs.toast.add({
                        severity: "error",
                        summary: "上傳失敗!",
                        life: 3000,
                    });
                })
                .finally(() => {
                    e.target.value = "";
                });
        },
    },
};
</script>

<style>
.MultiSelect .p-multiselect-header {
    padding: 11px 16px 11px 16px !important;
}
</style>
