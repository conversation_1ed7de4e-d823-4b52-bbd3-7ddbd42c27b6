<?php

namespace App\Jobs;

use App\Modules\Demand\Models\Employee;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use App\Modules\par\Models\Postulate;
use App\Modules\par\Services\PublicService;
use App\Modules\par\Events\notificationEvent;
use App\Modules\Demand\Models\Notification;
use App\Modules\par\Models\Apparatus;
use Carbon\Carbon;
use Illuminate\Support\Facades\Config;

class ReserveNotify implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $notifies = Notification::where('employee_id', 0)
            ->where('payload->method', 'email')
            ->where('payload->start', '<=', now()->toISOString())
            ->where('payload->end', '>', now()->toISOString())
            ->select('id', 'payload->reserve_id as reserveId')
            ->get();

        if ($notifies->isEmpty()) return;

        $this->postulate($notifies);
        $this->apparatus($notifies);


        Notification::destroy($notifies->pluck('id'));
    }

    public function postulate($notifies)
    {
        $postulates = Postulate::with([
            'reserve' =>  function ($query)  use ($notifies) {
                $query
                    ->whereNull('metadata->release')
                    ->where('type', 'postulate')
                    ->wherein('id', $notifies->pluck('reserveId'));
            },
            'notified:id,payload->email as email'
        ])
            ->select('id', 'name', 'postulates.payload as payload')
            ->wherehas('reserve',  function ($query)  use ($notifies) {
                $query
                    ->whereNull('metadata->release')
                    ->where('type', 'postulate')
                    ->wherein('id', $notifies->pluck('reserveId'));
            })
            ->get();



        if ($postulates->isEmpty()) return;
        // info('notify_po');

        $notified = $postulates->pluck('payload->notified')->collapse()->unique();
        $email = $postulates->pluck('notified')->collapse()->unique();

        $notified->each(function ($empId) use ($postulates, $email) {
            $emailMsg = $postulates
                ->filter(function ($postulate)  use ($empId) {
                    // 若設施的通知人員內有這位人員
                    return $postulate->notified->contains($empId);
                })
                ->map(function ($postulate) {
                    // 跑每項預約的資料
                    return $postulate->reserve->map(function ($item) use ($postulate) {
                        return [
                            'user' => Employee::find($item->employee_id)->payload->get('name'),
                            'name' => $postulate->name,
                            'date' => Carbon::parse($item->payload['date'])->setTimezone('Asia/Taipei')->format('Y/m/d'),
                            'time' => PublicService::idToTimePeriod($item->payload['time'])->toarray(),
                            'reason' => $item->payload['reason'],
                            'remark' => $item->payload['remark'],
                            'notified' => array_column($item->payload['apparatus'], 'type'),
                            'isRemind' => true
                        ];
                    })->toarray();
                })
                ->flatten(1)
                ->values();

            $data = ['subject' => Config::get('mail.sys_title'), 'msg' => $emailMsg, 'email' => $email->firstWhere('id', $empId)->email];
            event(new notificationEvent($data));
        });
    }

    public function apparatus($notifies)
    {
        $apparatuses = Apparatus::with([
            'reserve' =>  function ($query)  use ($notifies) {
                $query
                    ->whereNull('metadata->release')
                    ->where('type', 'apparatus')
                    ->wherein('id', $notifies->pluck('reserveId'));
            },
            'reserve.employee',
            'notified:id,payload->email as email'
        ])
            ->select('id', 'name', 'apparatuses.payload as payload')
            ->wherehas('reserve',  function ($query)  use ($notifies) {
                $query
                    ->whereNull('metadata->release')
                    ->where('type', 'apparatus')
                    ->wherein('id', $notifies->pluck('reserveId'));
            })
            ->get();

        if ($apparatuses->isEmpty()) return;
        // info('notify_ap');

        $notified = $apparatuses->pluck('payload->notified')->collapse()->unique();
        $email = $apparatuses->pluck('notified')->collapse()->unique();

        $notified->each(function ($empId) use ($apparatuses, $email) {
            $emailMsg = $apparatuses
                ->filter(function ($apparatus)  use ($empId) {
                    // 若設施的通知人員內有這位人員
                    return $apparatus->notified->contains($empId);
                })
                ->map(function ($apparatus) {
                    // 跑每項預約的資料
                    return $apparatus->reserve->map(function ($item) use ($apparatus) {
                        return [
                            'user' => $item->employee ? $item->employee->payload['name'] : '',
                            'name' => $apparatus->name,
                            'date' => Carbon::parse($item->payload['date'])->format('Y/m/d'),
                            'time' => PublicService::idToTimePeriod($item->payload['time'])->toarray(),
                            'reason' => $item->payload['reason'],
                            'remark' => $item->payload['remark'],
                        ];
                    })->toarray();
                })
                ->flatten(1)
                ->values();

            $data = ['subject' => Config::get('mail.sys_title'), 'msg' => $emailMsg, 'email' => $email->firstWhere('id', $empId)->email];
            event(new notificationEvent($data));
        });
    }
}
