export const confirmMixin = {
    data() {
        return {
            confirmOption: {
                type: "",
                message: "",
                imgPath: "",
                centerBtn: {
                    text: "",
                    command: null,
                },
                leftBtn: {
                    text: "",
                    command: null,
                },
                rightBtn: {
                    text: "",
                    command: null,
                },
                isShow: false,
            },
        };
    },
    methods: {
        setConfirm(option) {
            this.resetConfirm();
            this.confirmOption = Object.assign({}, this.confirmOption, option);
        },
        resetConfirm() {
            this.confirmOption = {
                type: "",
                message: "",
                imgPath: "",
                centerBtn: {
                    text: "",
                    command: null,
                },
                leftBtn: {
                    text: "",
                    command: null,
                },
                rightBtn: {
                    text: "",
                    command: null,
                },
                isShow: false,
            };
        },
        toggleConfirm(isShow) {
            this.confirmOption.isShow = isShow;
        },
    },
};
