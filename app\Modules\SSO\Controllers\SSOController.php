<?php

namespace App\Modules\SSO\Controllers;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Modules\SSO\Models\SsoToken;
use Illuminate\Support\Facades\Session;
use Illuminate\Encryption\Encrypter;
use Illuminate\Support\Facades\Config;
use Carbon\Carbon;

class SSOController extends Controller
{
    //
    protected function hrap()
    {
        if (!Session::has('employee_id'))
            return redirect('/');
        $employee_id = Session::get('employee_id');
        $token = hash('sha1', now() . $employee_id);
        //刪掉過期的
        SsoToken::where('created_at', '<', Carbon::now()->addMinute(-10))->delete();
        //新增
        $ssoToken = SsoToken::firstOrNew([
            'employee_id' => $employee_id
        ]);
        if ($ssoToken->token)
            $token = $ssoToken->token;
        else {
            $ssoToken->token = $token;
            $ssoToken->save();
        }
        $newEncrypter = new Encrypter(md5(Config('sso_key')), 'AES-256-CBC');
        $urlToken = $newEncrypter->encrypt($token);
        $url = config('app.sso_hrap_url') . '?token=' . $urlToken;
        return redirect()->away($url);
    }
    protected function acc()
    {
        try {
            if (!Session::has('employee_id'))
                return redirect('\\');
            $employee_id = Session::get('employee_id');
            $token = hash('sha1', now());
            //刪掉過期的
            SsoToken::where('created_at', '<', Carbon::now()->addMinute(-10))->delete();
            //新增
            SsoToken::firstOrCreate([
                'token' => $token,
                'employee_id' => $employee_id
            ]);
            $newEncrypter = new Encrypter(md5(Config('sso_key')), 'AES-256-CBC');
            $urlToken = $newEncrypter->encrypt($token);
            $url = config('app.sso_acc_url') . '?token=' . $urlToken;
            return redirect()->away($url);
        } catch (Exception $e) {
            \Log::debug($e);
        }
    }
    protected function pmap()
    {
        if (!Session::has('employee_id'))
            return redirect('\\');
        try {
            $employee_id = Session::get('employee_id');
            $token = hash('sha1', now() . $employee_id);
            //刪掉過期的
            SsoToken::where('created_at', '<', Carbon::now()->addMinute(-10))->delete();
            //新增
            $ssoToken = SsoToken::firstOrNew([
                'employee_id' => $employee_id
            ]);
            if ($hasToken = !empty($ssoToken->token))
                $token = $ssoToken->token;
            else {
                $ssoToken->token = $token;
                $ssoToken->save();
            }
            $newEncrypter = new Encrypter(md5(Config('sso_key')), 'AES-256-CBC');
            $urlToken = $newEncrypter->encrypt($token);
            $url = config('app.sso_pmap_url') . '?token=' . $urlToken;
        } catch (Exception $e) {
            \Log::debug('hasToken : ' . $hasToken);
            \Log::debug($ssoToken);
        }
        return redirect()->away($url);
    }
}
