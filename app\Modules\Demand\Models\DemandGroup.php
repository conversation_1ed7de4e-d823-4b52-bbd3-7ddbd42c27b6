<?php

namespace App\Modules\Demand\Models;

use Illuminate\Database\Eloquent\Model;
use App\Modules\Demand\Models\DemandLayout;
use Illuminate\Database\Eloquent\SoftDeletes;
class DemandGroup extends Model
{
    use SoftDeletes;
    protected $fillable = [
        'id',
        'name',
        'company_id',
        'created_by',
        'updated_by'
    ];

    public function layouts()
    {
        return $this->hasMany(DemandLayout::class, 'group_id');
    }
}
