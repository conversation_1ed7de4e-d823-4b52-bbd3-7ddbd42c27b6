FORMAT: 1A
# explorer-api
檔案管理相關的API

## fetch folder and file data [api/explorer/:folderId]

### fetch folder and file data [GET]
獲取目標資料夾的資料

+ Response 200 (application/json)
    + Attributes
        + status: 200 (number) - HTTP 狀態碼
        + message: "OK" (string) - 成功訊息
        + data: (array)
            + (object)
                + id: 1 (number, required) - 資料夾id
                + name: "資料夾" (string, required) - 資料夾名稱
                + type: "folder" (string, required) - 類型 (folder or application/pdf)
                + path: [0, 1] (array, required) - 資料夾id路徑
                + parentId: 0 (number, required) - 父級資料夾id
                + size: 1000 (number, required) - 檔案大小 (單位: bytes)
                + timeCreated: Wed Mar 15 2016 08:00:00 GMT+0800 (CST) (DateObject, required) - 上傳時間
                + admin: false (boolean, required) - 管理權限
                + read: false (boolean, required) - 閱讀權限
                + download: false (boolean, required) - 下載權限

+ Response 400 (application/json)
    + Attributes
        + status: 400 (number) - HTTP 狀態碼
        + message: "error message" (string) - 錯誤訊息


## fetch directory [api/explorer/dir]

### fetch directory [GET]
獲取目錄

+ Response 200 (application/json)
    + Attributes
        + status: 200 (number) - HTTP 狀態碼
        + message: "OK" (string) - 成功訊息
        + data: (array)
            + (object)
                + id: 1 (number, required) - 資料夾id
                + name: "資料夾" (string, required) - 資料夾名稱
                + path: [0, 1] (array, required) - 資料夾路徑
                + parentId: 0 (number, required) - 父級資料夾id

+ Response 400 (application/json)
    + Attributes
        + status: 400 (number) - HTTP 狀態碼
        + message: "error message" (string) - 錯誤訊息


## create a new folder [api/explorer/folder]

### create a new folder [POST]
新增資料夾

+ Request (application/json)
    + Attributes
        + (object)
            + name: "新資料夾" (string, required) - 資料夾名稱
            + parentId: 0 (number, required) - 父級資料夾id

+ Response 201 (application/json)
    + Attributes
        + status: 201 (number) - HTTP 狀態碼
        + message: "Created" (string) 成功訊息
        + data (object)
            + id: 1 (number, required) - 資料夾id
            + name: "資料夾" (string, required) - 資料夾名稱
            + type: "folder" (string, required) - 類型 (folder or application/pdf)
            + path: [0, 1] (array, required) - 資料夾id路徑
            + parentId: 0 (number, required) - 父級資料夾id
            + size: 1000 (number, required) - 檔案大小 (單位: bytes)
            + timeCreated: Wed Mar 15 2016 08:00:00 GMT+0800 (CST) (DateObject, required) - 上傳時間
            + admin: false (boolean, required) - 管理權限
            + read: false (boolean, required) - 閱讀權限
            + download: false (boolean, required) - 下載權限

+ Response 400 (application/json)
    + Attributes
        + status: 400 (number) - HTTP 狀態碼
        + message: "error message" (string) - 錯誤訊息


## rename a folder or file [api/explorer/rename/:id]

## rename a folder or file  [PUT]
重新命名資料夾/檔案

+ Request (application/json)
    + Attributes
        + name: "newName" (string, required) - 新資料夾名/檔名

+ Response 200 (application/json)
    + Attributes
        + status: 200 (number) - HTTP 狀態碼
        + message: "OK" (string) 成功訊息

+ Response 400 (application/json)
    + Attributes
        + status: 400 (number) - HTTP 狀態碼
        + message: "error message" (string) - 錯誤訊息


## copy a folder or file [api/explorer/copy/:id]

### copy a folder or file [PUT]
複製資料夾/檔案

+ Request (application/json)
    + Attributes
        + targetFolderId: 1 (array, required) - 目標資料夾id

+ Response 200 (application/json)
    + Attributes
        + status: 200 (number) - HTTP 狀態碼
        + message: "OK" (string) 成功訊息

+ Response 400 (application/json)
    + Attributes
        + status: 400 (number) - HTTP 狀態碼
        + message: "error message" (string) - 錯誤訊息


## cut a folder or file [api/explorer/move/:id]

### cut a folder or file [PUT]
剪下資料夾/檔案

+ Request (application/json)
    + Attributes
        + targetFolderId: 1 (array, required) - 目標資料夾id

+ Response 200 (application/json)
    + Attributes
        + status: 200 (number) - HTTP 狀態碼
        + message: "OK" (string) 成功訊息

+ Response 400 (application/json)
    + Attributes
        + status: 400 (number) - HTTP 狀態碼
        + message: "error message" (string) - 錯誤訊息


## delete a folder or file [api/explorer/:id]

### delete a folder or file [DELETE]
刪除資料夾/檔案

+ Response 200 (application/json)
    + Attributes
        + status: 200 (number) - HTTP 狀態碼
        + message: "OK" (string) 成功訊息

+ Response 400 (application/json)
    + Attributes
        + status: 400 (number) - HTTP 狀態碼
        + message: "error message" (string) - 錯誤訊息


## upload a file [api/explorer/upload]

### upload a file [POST]
上傳檔案

+ Request (application/json)
    + Attributes
        + file (Blob, required) - 包含所有檔案資訊的物件

+ Response 201 (application/json)
    + Attributes
        + status: 201 (number) - HTTP 狀態碼
        + message: "Created" (string) 成功訊息

+ Response 400 (application/json)
    + Attributes
        + status: 400 (number) - HTTP 狀態碼
        + message: "error message" (string) - 錯誤訊息


## get a file info [api/explorer/file-info]

### get a file info [GET]
取得檔案資訊

+ Request (application/json)
    + Attributes
        + id: 1 (number, required) - 檔案id
        + type: "read" (string, required) - 操作類型 ("raad" or "download")
        + isOldVersion: True (boolean, required) - 是否為舊版本檔案

+ Response 200 (application/json)
    + Attributes
        + status: 200 (number) - HTTP 狀態碼
        + message: "OK" (string) 成功訊息
        + data: (object)
            + name: "文件1.pdf" (string, required) - 檔名
            + type: "application/pdf" (string, required) - 檔案類型
            + url: "http://xxxx.xxxx.xxxx" (string, required) - 檔案url位置

+ Response 400 (application/json)
    + Attributes
        + status: 400 (number) - HTTP 狀態碼
        + message: "error message" (string) - 錯誤訊息


## search file [api/explorer/search]

### search file [GET]
搜尋檔案

+ Request (application/json)
    + Attributes
        + keyword: "abc" (string, required) - 搜尋關鍵字

+ Response 200 (application/json)
    + Attributes
        + status: 200 (number) - HTTP 狀態碼
        + message: "OK" (string) 成功訊息
        + data: (array)
            + (object)
                + id: 1 (number, required) - 檔案id
                + name: "文件1.pdf" (string, required) - 檔案名稱
                + type: "application/pdf" (string, required) - 類型 (folder or application/pdf)
                + path: [0, 1] (array, required) - 檔案路徑
                + parentId: 0 (number, required) - 父級資料夾id
                + size: 1000 (number, required) - 檔案大小 (單位: bytes)
                + timeCreated: Wed Mar 15 2016 08:00:00 GMT+0800 (CST) (DateObject, required) - 上傳時間

+ Response 400 (application/json)
    + Attributes
        + status: 400 (number) - HTTP 狀態碼
        + message: "error message" (string) - 錯誤訊息


## get file version [api/explorer/version/:fileId]

### get file version [GET]
取得檔案版本歷程記錄

+ Response 200 (application/json)
    + Attributes
        + status: 200 (number) - HTTP 狀態碼
        + message: "OK" (string) 成功訊息
        + data: (array)
            + (object)
                + id: 1 (number, required) - 檔案版本id (供下載、瀏覽使用)
                + uploader: "李家安" (string, required) - 上傳人
                + timeCreated: Wed Mar 15 2016 08:00:00 GMT+0800 (CST) (DateObject, required) - 上傳時間

+ Response 400 (application/json)
    + Attributes
        + status: 400 (number) - HTTP 狀態碼
        + message: "error message" (string) - 錯誤訊息
