<?php

namespace App\Observers;

use App\Modules\Demand\Models\Demand;
use App\Modules\Demand\Models\Log;

class demandObserver
{

    /**
     * Handle the demand "created" event.
     *
     * @param  \App\Demand  $demand
     * @return void
     */
    public function saved(Demand $demand)
    {
        $payload = $demand->payload;
        unset($payload['sign_roles']);
        Log::create([
            'demand_id' => $demand->id,
            'no' => $demand->no,
            'payload' =>  $payload,
            'user_id' =>  \Session::get('employee_id',0),//排成的話想不到其他辦法，暫時給0
            'sign_roles'=> $demand->payload->get('sign_roles'),
        ]);

    }

}
