<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class notification extends Mailable
{
    use Queueable, SerializesModels;

    /**
     * Create a new message instance.
     *
     * @return void
     */

    public $data;
    public function __construct($data)
    {
        $this->data = $data;
    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {
        $template = $this->view('emails.reserveMsg')
            ->subject($this->data['subject']);

        // 如果ics存在  
        if (isset($this->data['ics'])) {
            // 將時間轉成ics格式
            $msg = $this->data['msg'][0];
            $date = $msg['date'];
            $time = $msg['time'];
            $timeStart = substr($time[0], 0, 5);
            $timeEnd = substr($time[count($time) - 1], 6);

            // 設定時間
            // 轉換為 UTC
            $this->data['ics']->VEVENT->DTSTART = Carbon::parse($date)
                ->setTimezone('Asia/Taipei')  // 先設為台北時區
                ->setTimeFromTimeString($timeStart)  // 設定台北時間
                ->setTimezone('UTC');  // 轉換為 UTC;

            $this->data['ics']->VEVENT->DTEND = Carbon::parse($date)
                ->setTimezone('Asia/Taipei')  // 先設為台北時區  
                ->setTimeFromTimeString($timeEnd)  // 設定台北時間
                ->setTimezone('UTC');

            // 設定主辦人
            // $this->data['ics']->VEVENT->ORGANIZER = 'MAILTO:' . $this->data['organizerMail'];

            // 設定參與者
            foreach ($this->data['email'] as $email) {
                $this->data['ics']->VEVENT->add("ATTENDEE", "MAILTO:" . $email);
            }

            // 設定header
            if ($this->data['ics']->METHOD->getValue() == 'CANCEL') {
                $header = [
                    // 'Content-Type' => 'text/calendar;charset=utf-8; method=CANCEL',
                    'mime' => 'text/calendar;charset=utf-8; method=CANCEL',
                ];
            } else {
                $header = [
                    // 'Content-Type' => 'text/calendar;charset="utf-8";method=REQUEST',
                    'mime' => 'text/calendar;charset=utf-8; method=REQUEST',
                    'Content-Disposition' => 'inline; filename=reserve.ics',
                ];
            }
            // 將ics轉成字串
            $this->data['ics'] = $this->data['ics']->serialize();


            // 第一個附件：作為普通 ICS 文件
            $template->attachData(
                $this->data['ics'],
                'reserve.ics',
                $header
            );

            // 第二個附件：作為普通 ICS 文件
            // $template->attachData(
            //     $this->data['ics'],
            //     'invite.ics',
            //     [
            //         'mime' => 'application/ics;charset=utf-8;name="invite.ics"',
            //         'Content-Disposition' => 'attachment; filename="invite.ics"',
            //     ]
            // );
        }
        return $template;
    }
}


// test


// 1.預約後，修改備註，取消預約  沒有取消信件
// 2.有預約沒有預約通知信
// 3.設備增減沒有信件