<?php

namespace App\Modules\Demand\Models;

use Illuminate\Database\Eloquent\Model;
use App\Modules\Demand\Models\ListLayout;
use Illuminate\Database\Eloquent\SoftDeletes;
class DemandLayout extends Model
{
    //
    use SoftDeletes;
    use \Staudenmeir\EloquentJsonRelations\HasJsonRelationships;
    protected $fillable = [
        'id',
        'group_id',
        'name',
        'open',
        'payload',
        'sign_role',
        'can_multi',
        'auto_finished',
        'created_by',
        'updated_by',
        'priv_id',
    ];

    protected $casts = [
        'payload' => 'collection',
        'sign_role' => 'collection'
    ];

    public function scopeLatestVersion($query, $id){
        return $query->where('payload->original_id',$id)->max('id');
    }
    public function lists()
    {
        return $this->hasMany(ListLayout::class, 'demand_layout_id');
    }

    public function group()
    {
        return $this->belongsTo(DemandGroup::class);
    }

    public function dataAuth()
    {
        return $this->hasOne(DataAuth::class, 'layout_id', 'id');
    }

    public function demands()
    {
        return $this->hasMany(Demand::class, 'layout_id');
    }
    // public function users()
    // {
    //     return $this->hasManyThrough(Employee::class,DataAuth::class, 'payload->user_list','id', 'id','layout_id');
    // }

}
