<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateDemandCachesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('demand_caches', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->bigInteger('layout_id')->comment('需求單號編號');
            $table->bigInteger('employee_id')->comment('員工編號');
            $table->bigInteger('company_id')->comment('公司編號');
            $table->json('payload');

            $table->index(['employee_id', 'company_id']);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('demand_caches');
    }
}
