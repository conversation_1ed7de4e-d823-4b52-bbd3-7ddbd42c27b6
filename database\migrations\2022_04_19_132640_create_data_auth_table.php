<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateDataAuthTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('data_auths', function (Blueprint $table) {
            // $table->bigIncrements('id');
            $table->bigInteger('layout_id'); //pk
            $table->jsonb('payload');
            $table->jsonb('metadata');
            $table->timestamps();
            $table->primary('layout_id');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('data_auths');
    }
}
