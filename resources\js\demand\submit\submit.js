import '@/bootstrap.js';

import Banner from '@/demand/common/Banner.vue';
import Submit from './submit.vue';
import List from './list.vue';
import History from './history.vue';
import Axios from 'axios';

const app = new Vue({
    el: '#content',
    components: {
        Banner,
        Submit,
        List,
        History
    },
    data: {
        titles: [
            '需求申請', '申請進度', '申請紀錄'
        ],
        currentTab: tab,
    },

    mounted() {

    }
});


window.demandCache = {
    get: async (layoutId) => {
        try {
            let { data } = await Axios.get(`/api/demand/submit/cache/${layoutId}`);
            return data;
        } catch (e) {
            console.warn(e);
            throw e;
        }
    },
    set: async (layoutId, data) => {
        try {
            Axios.put(`/api/demand/submit/cache/${layoutId}`, data);
        } catch (e) {
            console.warn(e);
            throw e;
        }
    },
    del: async (layoutId) => {
        try {
            Axios.delete(`/api/demand/submit/cache/${layoutId}`);
        } catch (e) {
            console.warn(e);
            throw e;
        }
    }
};
