<?php

namespace App\Modules\par\Models;

use App\Modules\Demand\Models\Employee;
use App\Modules\Demand\Models\OrgUnit;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\Session;

class Apparatus extends Model
{
    use \Staudenmeir\EloquentJsonRelations\HasJsonRelationships;
    use SoftDeletes;
    protected $fillable = ['company_id', 'name',  'payload', 'metadata', 'created_by', 'updated_by'];
    protected $casts = ['payload' => 'collection', 'metadata' => 'collection'];


    public function scopeCompany($query)
    {
        return $query->where('company_id', Session::get('CompanyId'));
    }

    public function scopeIsOpen($query)
    {
        return $query->where(function ($query) {
            $query->whereNull('metadata->is_open')
                ->orwhere('metadata->is_open', true);
        });
    }

    public function reserve()
    {
        return $this->hasMany(Reserve::class, 'pa_id')->where('type', 'apparatus');
    }

    public function orgs()
    {
        return $this->belongsTo(OrgUnit::class, 'payload->org_unit_id');
    }

    public function notified()
    {
        return $this->belongsToJson(Employee::class, 'payload->notified')->withTrashed();
    }

}
