<template>
  <div class="p-2 w-full">
    <div class="p-2 flex flex-row w-full">
      <div class="w-1/5">
        <span class="text-2xl font-semibold">審核提醒</span>
      </div>

      <div class="w-3/5">
        <InputSwitch v-model="noticeSend" />
      </div>
      <div class="w-1/2">
        <Button @click="save()" label="儲存" class="w-28 float-right" />
      </div>
    </div>

    <div v-if="noticeSend" class="p-2">
      <div class="my-3">
        <label class="">提醒通知頻率</label>
        <br />
        <InputNumber
          v-model="noticeFrequency"
          showButtons
          suffix=" 天"
          :min="1"
        />
      </div>
      <br />
      <div>
        <label class="">提醒內容設定</label>
        <label class="mx-5 text-red-500">*需帶出數量時，數量請填寫參數:{x}</label>
        <label class="mx-5 text-red-500">*需帶出名字時，數量請填寫參數:{name}</label>
        <br />
        <Editor
          v-model="msgText"
          editorStyle="height:24rem; font-size: 1rem;line-height: 1.5rem; "
        />
      </div>
    </div>
  </div>
</template>

<script>
import axios from "axios";
import Button from "primevue/button";
import InputSwitch from "primevue/inputswitch";
import InputNumber from "primevue/inputnumber";
import Editor from "primevue/editor";

export default {
  components: {
    InputNumber,
    Button,
    InputSwitch,
    Editor,
  },
  data() {
    return {
      settings: {},
      noticeSend: false,
      noticeFrequency: 0,
      msgText: "",
      url: "/api/demand/setting/signature-notification",
      //   excludedEmployees: [],
    };
  },

  methods: {
    fetch() {
      axios
        .get(this.url)
        .then((response) => {
          this.settings = response.data.settings;
          this.noticeSend = this.settings.noticeSend;
          this.noticeFrequency = this.settings.noticeFrequency;
          this.msgText = this.settings.msg;
        })
        .catch((error) => {
          console.error(error);
        });
    },
    save() {
      this.settings.noticeSend = this.noticeSend;
      this.settings.noticeFrequency = this.noticeFrequency;
      this.settings.msg = this.msgText;
      axios
        .put(this.url, {
          settings: this.settings,
        })
        .then((response) => {
          if (response.data) {
            this.$alert("儲存成功");
          } else {
            this.$alert("儲存失敗");
          }
        })
        .catch((error) => {
          console.error(error);
        });
    },
  },
  mounted() {
    this.fetch();
  },
};
</script>
