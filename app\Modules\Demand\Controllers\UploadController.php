<?php

namespace App\Modules\Demand\Controllers;


use Illuminate\Http\Request;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Validator;
use App\Http\Controllers\Controller;
use App\Modules\Demand\Models\Log;
use ErrorException;
use Illuminate\Support\Facades\Storage;

class UploadController extends Controller
{
    private function validateCreateRequest($data)
    {
        $validator = Validator::make(
            $data,
            [
                'file' => 'required|mimes:jpg,pdf,jpeg,heif,hevc,png,HEIF,HEVC,docx,xls,xlsx|max:10240'
            ]
        );
        return $validator;
    }
    public function uploadTemp(Request $request)
    {
        // 用於類型檢查
        $types = ["jpg", 'jpeg', 'png', "pdf", "heif", "hevc", 'xls', 'xlsx', 'doc', 'docx'];
        $request->types = $types;
        return $this->uploadTempFile($request);
    }
    public function uploadTempFile(Request $request)
    {
        try {
            $result = [];
            $index = 0;
            foreach ($request->files as $file) {
                $fileExt = strtolower($file->getClientOriginalExtension());
                // 檢查檔案類型
                if (!in_array($fileExt, $request->types)) {
                    return response()->json(['message' => 'Invalid file type'], 400);
                };
                $fileName = $request['fileName'. $index];
                $subName = pathinfo($fileName, PATHINFO_EXTENSION);
                $formattedFile['file'] = $file;
                $validator = $this->validateCreateRequest($formattedFile);
                if ($validator->fails()) {
                    return response()->json(['error' => '大小或類型錯誤'], 400);
                } else {
                    $hashName = time() . '_' . md5($fileName . \Str::random(3));
                    $path = Storage::putFileAs(
                        'public/demand/temp',
                        $file,
                        $hashName . "." . $subName
                    );
                    $fileInfo = [
                        'URL' => Storage::url($path),
                        'base_name' => basename($path),
                        'name' => $fileName,
                        'size' => $file->getSize()
                    ];
                    array_push($result, $fileInfo);
                }
                $index += 1;
            };
            return $result;
        } catch (\Exception $ex) {
            \Log::error('上傳檔案錯誤');
            \Log::error($request->all());
            \Log::error($ex);
        }
    }
    public function uploadTemplete(Request $request)
    {
        $types = ['docx', 'xls', 'xlsx'];
        $request->types = $types;
        return $this->uploadTempFile($request);
    }
    public function deleteTemp(Request $request)
    {
        $baseName = $request->get('base_name');
        if (!$request->get('base_name')) {
            return 0;
        }
        $exists = Storage::disk('public')->exists('demand/temp/' . $baseName);

        if ($exists) {
            Storage::disk('public')->delete('demand/temp/' . $baseName);
        }

        return 1;
    }

    public function moveFiles($lastPath, $baseName)
    {
        //確認temp在不在 不在就不執行
        $exists = Storage::disk('public')->exists('demand/temp/' . $baseName);
        if ($exists) {
            //  Storage::move('demand/temp/', $lastPath);
            $isMoved = Storage::disk('public')->move('demand/temp/' . $baseName, $lastPath);
            if (!$isMoved) {
                \Log::error('檔案移動錯誤');
                \Log::error("lastPath : $lastPath");
                \Log::error("baseName : $baseName");
                throw new ErrorException('檔案系統錯誤!');
            }
            // Storage::disk('public')->delete('demand/temp/' . $baseName);
            return 1;
        } else {
            return 0;
        }
    }

    public function downloadFile($no, $hashName, $oriName)
    {
        $filePath = 'app/public/demand/upload/' . $no . '/' .  $hashName;
        // $contents = Storage::get($fileName);
        $headers = ['Content-Type: multipart/form-data'];
        return response()->download(storage_path($filePath), $oriName, $headers);
    }

    public function cleanUpUnusedFiles(string $originalId, array $currentFiles)
    {
        $fullPath = 'demand/upload/demandLayout/' . $originalId;
    
        // 取得資料夾內所有檔案
        $allFiles = Storage::disk('public')->files($fullPath);
    
        // 將目前還存在的檔名整理出來
        $keepFiles = array_map(function ($file) {
            return $file['base_name'];
        }, $currentFiles);

        foreach ($allFiles as $filePath) {
            $baseName = basename($filePath);
            if (!in_array($baseName, $keepFiles)) {
                Storage::disk('public')->delete($filePath);
            }
        }
    }
}
