import axios from "axios";

const service = axios.create({
    baseURL: "/api/explorer",
});

const pendingRequests = new Map();

service.interceptors.request.use((config) => {
    if (config.url === "/upload") return config;

    // 儲存請求的url作為key
    const requestKey = config.url;

    config.cancelToken = new axios.CancelToken((cancel) => {
        if (pendingRequests.has(requestKey)) {
            // 如果當前請求存在，取消上一次請求
            const previousCancel = pendingRequests.get(requestKey);
            if (previousCancel && previousCancel.cancel) {
                previousCancel.cancel();
            }
        }

        // 儲存當前請求的 cancel 方法
        pendingRequests.set(requestKey, { cancel });
    });

    return config;
});

service.interceptors.response.use(
    (response) => {
        const { data, status } = response;
        const requestKey = response.config.url;
        pendingRequests.delete(requestKey);
        if (status === 200) return data;
    },
    async (error) => {
        if (error.config) {
            const requestKey = error.config.url;
            pendingRequests.delete(requestKey);
        }
        if (error.name === "CanceledError") return;
        let errorMessage = "網路請求異常，請稍後再試";

        if (error.response) {
            const { status, data } = error.response;
            if (
                error.request.responseType === "blob" &&
                data instanceof Blob &&
                data.type &&
                data.type.toLowerCase().indexOf("json") != -1
            ) {
                errorMessage = JSON.parse(await data.text()).msg;
            } else {
                switch (status) {
                    case 500:
                        errorMessage = "伺服器錯誤，請稍後再試";
                        break;
                    case 403:
                        errorMessage = "無訪問權限";
                        break;
                    case 404:
                        errorMessage = "找不到資源";
                        break;
                    default:
                        errorMessage =
                            error.response.data.msg ||
                            error.response.data.message ||
                            errorMessage;
                        break;
                }
            }
        }
        return Promise.reject({ message: errorMessage });
    }
);

function request(options) {
    options.method = options.method || "get";
    if (options.method.toLowerCase() === "get") {
        options.params = options.data;
    }

    return service(options);
}

export default request;
