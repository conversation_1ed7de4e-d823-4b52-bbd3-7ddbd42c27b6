<?php

namespace App\Jobs;

use App\Mail\DemandsMail;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Mail;

class CancelReserveNotify implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $name;
    protected $type;
    protected $emails;
    /**
     * Create a new job instance.
     *
     * @param boolean $type
     * @return void
     */
    public function __construct($emails ,$name, $type)
    {
        $this->emails = $emails;
        $this->name = $name;
        $this->type = $type;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $type = $this->type ? '關閉' :  '刪除';

        $emailMsg = '您預約的' . $this->name . '因' . $type . '緣故，已自動取消該筆預約，請至';
        $emailMsg .=  '<a href=\"' . Config::get('mail.sso_url') . '?to=asap' . '" target=\"_blank\">系統</a>上確認';

        $data = ['subject' => Config::get('mail.sys_title'), 'msg' => $emailMsg];
        Mail::to(Config::get('mail.from.address'))
            ->bcc($this->emails)
            ->queue(new DemandsMail($data));
    }
}
