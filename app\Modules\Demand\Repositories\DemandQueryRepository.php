<?php

namespace App\Modules\Demand\Repositories;

use App\Modules\Demand\Models\DemandQuery;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Log;


class DemandQueryRepository
{
    protected $company_id;
    protected $user_id;
    protected $demandQuery;

    public function __construct(DemandQuery $demandQuery)
    {
        $this->demandQuery = $demandQuery;
        $this->company_id = Session::get('CompanyId');
        $this->user_id = Session::get('employee_id');
    }

    /**
     * 撈需求單資料
     * @param int $apiType 請求來源: 0: 需求單查詢, 1: 審核紀錄
     * @param int $review 查詢人執行的動作類型(同意, 退回, 會簽等..): -1: 查詢"除"未開始審核階段的所有單據, 0: 為需求單查詢傳入，不區分單據審核階段，剩餘請參考 ApplyStatus.php
     * @param int $type 查找類型?: 0: 不分 1: 未結案 2: 已結案
     * @param string|null $keyWord 搜尋關鍵字
     * @param int $layoutId 需求單 layout
     * @param string $start 開始時間
     * @param string $end 結束時間
     * @return \Illuminate\Database\Query\Builder
     */
    public function fetchDemandQuery($apiType, $review, $type, $keyWord, $layoutId, $start, $end)
    {
        switch ($review) {
            case 0:
                // 需求單查詢API，不分審核類型
                $applyStatus = 0;
                break;
            case -1:
                $applyStatus = [2, 3, 5, 6, 7];
                break;
            case 6:
                $applyStatus = [6, 7];
                break;
            default:
                $applyStatus = [$review];
                break;
        }
        // 0不分 1未結案 2已結案
        $subQuery = DemandQuery::where('company_id', $this->company_id)
            ->when($applyStatus, function ($q) use ($applyStatus) {
                $q->whereIn('apply_status', $applyStatus);
            })
            ->when($type, function ($q) use ($type) {
                $q->where('demand_type', $type);
            })
            ->when(
                $keyWord,
                function ($q) use ($keyWord) {
                    return  $q->where(function ($q) use ($keyWord) {
                        $q->where('payload', 'like', '%' . $keyWord . '%')
                            ->orWhere('no', 'like', $keyWord . '%');
                    });
                }
            );

        //  0:需求單查詢API，必須要給 layoutId 否則回傳空物件
        //  1:審核紀錄API，不必強制 layoutId 允許找尋審核人審過的所有單
        if ($apiType == 0) {
            $subQuery->whereBetween('created_at', [$start, $end]);
            if (empty($layoutId)) {
                $subQuery->whereRaw('1 = 0'); // 強制不回傳任何結果
            } else {
                if (is_array($layoutId)) {
                    $subQuery->whereIn('payload->layout_original_id', $layoutId);
                } else {
                    $subQuery->where('payload->layout_original_id', $layoutId);
                }
            }    
        } else {
            $subQuery->whereBetween('raw_time', [$start, $end])
                ->where('role_id', intval($this->user_id));
            if ($layoutId) {
                $subQuery->where('payload->layout_original_id', $layoutId);
            }
        }


        // 找特定單子
        // if ($layoutId) {
        //     if (is_array($layoutId)) {
        //         $subQuery = $subQuery->whereIn('payload->layout_original_id', $layoutId);
        //     } else {
        //         $subQuery = $subQuery->where('payload->layout_original_id', $layoutId);
        //     }
        // }

        $subQuery->selectRaw("
            ROW_NUMBER() OVER(PARTITION BY id) AS rn, *, CASE demand_type WHEN 1 THEN '未結案' ELSE '已結案' END AS type")
            ->orderByDesc('raw_time');

        $demandQuery = $this->demandQuery::with([
            'customList',
            'employee' => function ($q) {
                return $q->withTrashed();
            },
            'employee.orgs'
        ])->fromSub($subQuery, 'demands_query')
            ->where('rn', 1);
        return  $demandQuery->orderByDesc('raw_time');
    }
}
