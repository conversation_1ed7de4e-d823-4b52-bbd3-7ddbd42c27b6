import '@/bootstrap.js';

import Banner from "@/demand/common/Banner.vue";
import room from "./room.vue";
import tool from "./tool.vue";

const app = new Vue({
    el: "#content",
    components: {
        Banner,
        room,
        tool,
    },
    data: {
        titles: ["設施管理"],
        currentTab: 0,
        loading: false,
    },

    mounted() {
        if (isDevelopment) {
            // this.titles.push("設備管理");
        }
    },
});
