<?php

namespace App\Modules\Demand\Controllers;

use App\Events\DemandUpdated;
use App\Handlers\RefreshMaterializedViewDemandQuery;
use App\Modules\Demand\Services\SubmitService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Log;
use App\Http\Controllers\Controller;
use App\Modules\Demand\Models\Demand;
use App\Modules\Demand\Models\DemandLog;
use App\Modules\Demand\Models\DemandGroup;
use App\Modules\Demand\Models\DemandLayout;
use App\Modules\Demand\Models\Employee;
use App\Modules\Demand\Models\OrgUnit;
use App\Modules\Demand\Models\OrgUnitMember;
use App\Modules\Demand\Models\Code;
use App\Modules\Demand\Models\CustomList;
use App\Traits\GetColumnValue;
use Carbon\Carbon;
use Exception;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;
use PhpParser\Node\Stmt\TryCatch;

class SubmitController extends Controller
{
    use GetColumnValue;
    protected $company_id;
    protected $user_id;
    protected $timezone;

    public function __construct()
    {
        $this->company_id = Session::get('CompanyId');
        $this->user_id = Session::get('employee_id');
        $this->timezone = Session::get('timezone');
    }

    public function fetchGroupSetting(Request $request)
    {
        // 是否為子單
        $isChild = filter_var($request->get('is_child'), FILTER_VALIDATE_BOOLEAN);
        $groups = [];
        $layoutIds = collect();

        if ($isChild) {
            $layoutIds = DemandLayout::join('demand_groups', 'demand_layouts.group_id', '=', 'demand_groups.id')
                ->where('demand_groups.company_id', $this->company_id)->whereRaw(
                    "EXISTS (
                    SELECT 1
                    FROM jsonb_array_elements(sign_role) as elem
                    WHERE elem->>'type' = '1'
                )"
                )
                ->select('demand_layouts.id')
                ->pluck('id');
        }

        $groups = DemandGroup::with([
            'layouts' => function ($layout) use ($layoutIds) {
                $layout->where('open', true)
                    ->when($layoutIds->count() > 0, function ($q) use ($layoutIds) {
                        $q->whereIn('id', $layoutIds);
                    });
            }
        ])
            ->where('company_id', $this->company_id)
            ->select('id', 'name')
            ->orderBy('id', 'asc')
            ->get();

        if (!$groups) {
            return [];
        }

        return $groups->each(function ($group, $i) {
            $group->layouts->makeHidden([
                'group_id',
                'open',
                'payload',
                'sign_role',
                'can_multi',
                'auto_finished',
                'created_by',
                'updated_by',
                'created_at',
                'updated_at',
                'deleted_at'
            ])->each(function ($layout) {
                $layout->sort = isset($layout->payload['sort']) ? intval($layout->payload['sort']) : null;
            });
        });
    }


    //根據設定 提交同格式是否多筆單
    public function submit(Request $request, SubmitService $submitService)
    {
        $required = [
            'employee_id' => Session::get('employee_id'),
            'CompanyId' => Session::get('CompanyId'),
            'timezone' => Session::get('timezone', 'Asia/Taipei'),
        ];
        $demandId = $submitService->createDemand($required, $request);
        RefreshMaterializedViewDemandQuery::handle();
        return $demandId;
    }

    public function againSubmit(Request $request)
    {
        try {

            $layoutId = $request->get('layout_id');
            $demandId = $request->get('demand_id');
            $requestForms = $request->get('forms');

            if (!$requestForms && !$layoutId && !$demandId)
                return 0;
            $demand = Demand::find($demandId);
            $layout = DemandLayout::withTrashed()->find($layoutId);

            $roles = $demand->payload->get('sign_roles');
            $nextSignOff = array_search(0, array_column($roles, 'apply_status'));
            $roles[$nextSignOff]['apply_status'] = 1;


            //發通知
            (new NotificationController)->createNotify($roles[0]['role_id'], $layout->name, 1);
            //填寫後的表單
            $forms = collect();
            $customList = collect();
            collect($requestForms)->each(function ($cols, $formIndex) use ($forms, &$customList) {

                $cols = collect($cols)->transform(function ($col, $columnIndex) use ($formIndex, &$customList) {

                    if ($col['type'] == 'customList') {
                        $customList->push([
                            'form_index' => $formIndex,
                            'column_index' => $columnIndex,
                            'payload' => json_encode(['form_setting' => $col['form_setting']]),
                            'list' => $col['custom_list'],
                            'updated_by' => $this->user_id,
                        ]);
                        unset($col['form_setting']);
                        unset($col['custom_list']);
                    }
                    return $col;
                });
                $forms->push(['columns' => $cols]);
            });

            // 摘要
            $summaryColId = $layout->payload->get('summaryColId');
            if (isset($summaryColId)) {
                $tempColumn = $forms[0]['columns'][$summaryColId - 1];
                $summaryValue = $this->getValue($tempColumn);
            } else
                $summaryValue = '';

            $demand->setAttribute('payload->forms', $forms)
                ->setAttribute('payload->sign_roles', $roles)
                ->setAttribute('payload->summaryValue', $summaryValue)
                ->setAttribute('payload->status', 1);
            $demand->save();
            // 更新CustomList
            if ($customList->count() > 0) {

                CustomList::where('demand_id', $demandId)
                    ->get()
                    ->each(function ($item) use ($customList) {
                        $filtered = $customList->findList($item->form_index, $item->column_index);
                        // ->where('form_index', $item->form_index)
                        // ->where('column_index', $item->column_index)
                        // ->first();
                        $item['list'] = $filtered['list'];
                        $item->save();
                    });
            }

            //找附件把temp files move到單號資料夾
            $Upload = new UploadController;


            collect($requestForms)->each(function ($requestForm, $index) use ($demand, $Upload) {
                $doc = collect($requestForm)->where('type', 'document')->first();
                if (!empty($doc)) {
                    $files = $doc['files'];
                    foreach ($files as $key => $file) {

                        $lastPath = '/demand/upload/' . $demand->no . '/' . $file['base_name'];
                        $uploadResult = $Upload->moveFiles($lastPath, $file['base_name']);
                        if ($uploadResult) {
                            $files[$key]['URL'] = '/storage' . $lastPath;
                            $files[$key]['path'] = '/' . $demand->no . '/' . $file['base_name'] . '/' . $file['name'];
                        }
                    }
                    $columnId = $doc['id'];
                    $forms = $demand->payload->get('forms');
                    $ii = 0;
                    foreach ($forms[$index]['columns'] as $i => $column) {
                        if ($column['id'] == $columnId)
                            $ii = $i;
                    }
                    $forms[$index]['columns'][$ii]['files'] = $files;
                    $demand->setAttribute('payload->forms', $forms);
                    $demand->save();
                }
            });

            return 1;
        } catch (Exception $e) {
            Log::error($request->all());
            Log::error($e);
            return 0;
        }
    }
    public function delete(Request $request)
    {
        $id = $request['id'];
        if (!$id)
            return 0;

        Demand::where('id', $id)->delete();
        CustomList::where('demand_id', $id)->delete();
        return 1;
    }

    //沒自動結案 的撈出來 不管status
    public function fetchLists(Request $request)
    {
        $keyword = $request->get('keyword') ?? false;
        $demands = Demand::with(
            'customList',
            'employee:id,payload->name as name,payload->job_title as job_title',
            'employee.orgs:org_units.id,org_units.payload->name as orgname'
        )
            ->where('created_by', $this->user_id)
            ->when($keyword, function ($query) use ($keyword) {
                return $query->where(function ($query) use ($keyword) {
                    $query->where('payload->layout_name', 'like', '%' . $keyword . '%')
                        ->orWhere('no', 'like', '%' . $keyword . '%')
                        ->orWhere(DB::raw("TO_CHAR(created_at,'YYYY/MM/DD')"), 'like', '%' . $keyword . '%')
                        ->orWhere('payload->summaryValue', 'like', '%' . $keyword . '%');
                });
            })
            ->orderBy('id', 'desc')
            ->get();

        return $this->sortSignRoles($demands);
    }

    //應該是撈已結案DemandLog的表
    public function fetchHistory(Request $request)
    {
        if (!$request->has('start') || !$request->has('end')) {
            return [];
        }
        //layout_id 申請單類型
        $layout_id = $request->get('id');
        $result = intval($request->input('result'));
        $key_word = $request->get('key_word');
        $end = Carbon::parse($request->get('end'))->addDay(1)->subSecond();

        // {value: -1 ,name: '不分'}
        // 同意
        if ($result == -1 || $result == 2) {

            $demandLog = DemandLog::with(
                'customList',
                'employee:id,payload->name as name,payload->job_title as job_title',
                'employee.orgs:org_units.id,org_units.payload->name as orgname'
            )
                ->when(
                    $key_word,
                    function ($q) use ($key_word) {
                        return $q->where(function ($q) use ($key_word) {
                            $q->where('payload->layout_name', 'like', '%' . $key_word . '%')
                                ->orWhere('payload->summaryValue', 'like', '%' . $key_word . '%')
                                ->orWhere('no', 'like', '%' . $key_word . '%');
                        });
                    }
                )
                ->where('created_by', $this->user_id)
                ->orderBy('id', 'desc')
                ->whereBetween('created_at', [$request->get('start'), $end])
                ->when($layout_id, function ($q) use ($layout_id) {
                    return $q->where('payload->layout_original_id', $layout_id);
                })
                ->get();
        }

        //  駁回
        if ($result == -1 || $result == 3) {

            $demand = Demand::with(
                'customList',
                'employee:id,payload->name as name,payload->job_title as job_title',
                'employee.orgs:org_units.id,org_units.payload->name as orgname'
            )
                ->when(
                    $key_word,
                    function ($q) use ($key_word) {
                        return $q->where(function ($q) use ($key_word) {
                            $q->where('payload->layout_name', 'like', '%' . $key_word . '%')
                                ->orWhere('payload->summaryValue', 'like', '%' . $key_word . '%')
                                ->orWhere('no', 'like', '%' . $key_word . '%');
                        });
                    }
                )
                ->where('payload->status', 3)
                ->where('created_by', $this->user_id)
                ->orderBy('id', 'desc')
                ->whereBetween('created_at', [$request->get('start'), $end])
                ->when($layout_id, function ($q) use ($layout_id) {
                    return $q->where('payload->layout_original_id', $layout_id);
                })
                ->get();
        }

        if ($result == -1) {
            $demands = $demandLog->merge($demand);
        } else if ($result == 2) {
            $demands = $demandLog;
        } else if ($result == 3) {
            $demands = $demand;
        }

        $demands = $this->sortSignRoles($demands);

        // 不用sql paginate是因為
        // 申請結果是所有時，會是多表抓取，用這方法最快
        return (new CommonController)->arrayPaginator($demands->toArray(), $request);
    }

    public function sortSignRoles($demands)
    {
        try {
            $employees = CommonController::fetchEmployees($includeTrashed = true);
            $codeTable = Code::where('code_kind', 'AA')->get();

            $demands->transform(function ($demand) use ($codeTable, $employees) {

                $demand->status = $demand->payload['status'];
                $demand->forms = $demand->payload['forms'];
                $demand->is_child = isset($demand->payload['is_child']) ? $demand->payload['is_child'] : false;
                $demand->remarks = isset($demand->payload['remarks']) ? $demand->payload['remarks'] : null;

                $signRoles = $demand->payload['sign_roles'];
                $roles = collect($employees)->whereIn('id', collect($signRoles)->pluck('role_id'))->values();
                foreach ((array) $signRoles as $roleIndex => $role) {
                    $signRoles[$roleIndex]['name'] = $roles->firstWhere('id', $role['role_id']) ? $roles->firstWhere('id', $role['role_id'])['name'] : '';
                    if (isset($role['isRepresent']) && $role['isRepresent']) {
                        $signRoles[$roleIndex]['name'] .= '(代)';
                    }
                }
                $demand->sign_roles = $signRoles;
                $demand->name = $demand->payload['layout_name'];
                //轉換日期格式
                $demand->created = $this->signTimestamp(Carbon::parse($demand->created_at), $codeTable, '');

                $demand->summaryValue = $demand->payload->get('summaryValue');
                $demand->forms = collect($demand->forms)
                    ->map(function ($form, $formIndex) use ($codeTable, $demand) {
                        $form['columns'] = collect($form['columns'])
                            ->map(function ($column, $columnIndex) use ($formIndex, $codeTable, $demand) {

                                if ($column['type'] == 'customList') {
                                    $customList = $demand->customList->findList($formIndex, $columnIndex);
                                    // ->where('form_index', $formIndex)
                                    // ->where('column_index', $columnIndex)
                                    // ->first();
                                    $column['form_setting'] = $customList ? $customList->payload->get('form_setting') : [];
                                    $column['custom_list'] = $customList ? $customList->list : [];
                                }

                                if (isset($column['type']) && ($column['type'] == 'date' || $column['type'] == 'time') && isset($column['value'])) {
                                    try {
                                        // 當再次申請時需要原始值
                                        $column['dateTime'] = $column['value'];
                                        $column['value'] = $this->signTimestamp(Carbon::parse($column['value']), $codeTable, '');
                                    } catch (Exception $e) {
                                        Log::error($demand->id);
                                        Log::error($e);
                                    }
                                }
                                return $column;
                            });
                        return $form;
                    });
                $demand->applicant = $demand->payload->get('applicant');
                $demand->applicant_id = $employees->firstWhere('id', $demand->created_by)['employee_number'];
                unset($demand->customList);
                unset($demand->created_by);
                unset($demand->updated_by);
                unset($demand->updated_at);
                unset($demand->created_at);
                unset($demand->payload);
                unset($demand->layout);
                return $demand;
            });
            // ->makeHidden(['created_by', 'updated_by', 'updated_at', 'created_at', 'payload', 'layout','custom_list']);
            return $demands;
        } catch (Exception $e) {
            Log::error($e);
            Log::error($demands);
        }
    }

    //該需求單流程json
    public function fetchSignRolesDropdown(Request $request)
    {
        if ($request->get('id')) {
            $Demand = Demand::where('id', $request->get('id'))
                ->first();
            $signRoles = $Demand->payload['sign_roles'];
            $roles = [];
            foreach ($signRoles as $role) {
                $org_id = OrgUnitMember::where('employee_id', $role['role_id'])->first();
                $orgName = $org_id ? OrgUnit::find($org_id->org_unit_id)->payload['name'] : '';
                if (isset($role['type']) && $role['type'] == 1) {
                    $role['rank'] = $orgName;
                }
                array_push($roles, [
                    'rank' => $role['rank'] ?? '',
                    'id' => $role['id'],
                    'name' => Employee::withTrashed()->find($role['role_id'])->payload['name']
                ]);
            }
            return $roles;
        }
        return [];
    }

    //結案要move到DemandLogTable
    public function closeCase(Request $request)
    {
        if ($request->get('id')) {
            return app(SubmitService::class)->moveToDemandLog($request->get('id'));
        }
        return 0;
    }

    //reviewAgain再審(退回後)後新增節點
    public function addNodeToSignRols(Request $request)
    {
        $reviewType = 4;
        $codeTable = Code::where('code_kind', 'AA')->get();
        //給單號id 跟簽核節點id 再push該節點
        if ($request->has('id', 'remark', 'node_id')) {
            $Demand = Demand::with(['employee.orgs'])->where('id', $request->get('id'))->first();
            $layoutName = isset($Demand->payload['layout_name']) ? $Demand->payload['layout_name'] : '';
            if (!$Demand) {
                return 0;
            }
            $signRols = collect($Demand->payload['sign_roles']);
            $statusName = Code::where('code_kind', 'AI')->where('code_id', 'self')->first();
            $statusName = $statusName ? $statusName->nm_zh_tw : '';
            $thisNode = (object) [
                'id' => $signRols->count() + 1,
                'type' => 2,
                'role_id' => $this->user_id,
                'self_name' => $Demand->employee->orgs->pluck('payload.name')->first(), //這裡放部門名稱
                'apply_status' => $reviewType,
                'timestamp' => $this->signTimestamp(Carbon::now(), $codeTable, ' ' . $statusName),
                'raw_time' => Carbon::now(),
                'remark' => $request->get('remark'),

            ];

            $newNode = $signRols->where('id', $request->get('node_id'))->first();
            $newNode['id'] = $signRols->count() + 2;
            $newNode['apply_status'] = 1;
            $newNode['review'] = 1;
            $newNode['remark'] = '';
            $newNode['timestamp'] = '';
            try { //發通知
                $Notify = new NotificationController;
                $Notify->createNotify($request->get('node_id'), $layoutName, 1);
            } catch (Exception $e) {

                Log::error($e);
                return 0;
            }

            $Demand->setAttribute('payload->status', $reviewType)
                ->setAttribute('payload->review', true)
                ->setAttribute('payload->sign_roles', $signRols->push($thisNode)->push($newNode));

            $Demand->save();
            return 1;
        }
        return 0;
    }

    public function signTimestamp($times, $codeTable, $str = '')
    {
        $times = $times->toDateTimeString();
        $date = Carbon::parse($times)->setTimezone($this->timezone);
        $day = $date->format('Y/m/d');
        $time = $date->format('H:i');
        $w = 'W' . Carbon::parse($date)->dayOfWeek;
        $codes = $codeTable->where('code_id', $w)->first()->nm_zh_tw;

        return $day . "(" . $codes . ")" . $time . $str;
        // return $stamp . '2033/07/22(四)17:00 已簽核';
    }

    public function fetchDemandTypeDropdown()
    {
        return DemandLayout::whereHas('group', function ($g) {
            $g->where('company_id', $this->company_id);
        })
            ->select('payload->original_id as id', 'name')
            ->get();
    }

    public function fetchDemandQueryTypeDropdown()
    {

        $auths = DemandLayout::with([
            'dataAuth:layout_id,data_auths.payload->user_list as user_list,payload',
            'dataAuth.users:id,payload->name as name'
        ])->whereHas('dataAuth', function ($q) {
            $q->whereJsonContains('payload->user_list', (int) $this->user_id);
        })->select('payload->original_id as id', 'name')
            ->get();

        return $auths;
    }
}
