## getLayout [api/demand/submit/layout]
獲取layouts相關資訊
### get layout [GET]

Request (application/json)
    Attributes
        id (array) - 需求單的Id
            205 (number, required) - demand_layouts的id
Response 200 (application/json)
    Attributes
        auto_finished: true
        can_multi: true
        columns: [{id: 1, name: "報銷金額", type: "money", selected: {id: 0, type: "money", fname: "輸入題(金額)"}},…]
        id: 235
        import: true
        name: "欄位帶入設定"
        open: true
        + sign_role (array) - 簽核關卡
            + (object)
                + id : 1 (number, required) - 關卡的id
                + sign_id : 161  or null (number, required) - 需簽核人的id(沒有id則給null)
                + self_applicant: true or false (string) - 是否有開啟自行指定人員
