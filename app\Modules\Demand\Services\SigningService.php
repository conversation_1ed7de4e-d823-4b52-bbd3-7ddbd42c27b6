<?php

namespace App\Modules\Demand\Services;

use App\Modules\Demand\Repositories\DemandQueryRepository;
use App\Modules\Demand\Models\Code;
use App\Modules\Demand\Models\OrgUnitMember;
use App\Modules\Demand\Controllers\CommonController;
use App\Traits\FormatDate;
use Carbon\Carbon;
use Exception;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Session;

class SigningService
{
    protected $company_id;
    protected $user_id;
    protected $demandQuery;
    protected $codeTable;
    protected $timezone;
    use FormatDate;


    public function __construct(DemandQueryRepository $demandQuery)
    {
        $this->demandQuery = $demandQuery;
        $this->company_id = Session::get('CompanyId');
        $this->user_id = Session::get('employee_id');
        $this->codeTable = Code::where('code_kind', 'AA')->get();
        $this->timezone = Session::get('timezone');
    }


    /**
     * 撈需求單資料
     * @param int $review 審核類型: 前端傳入 -1 為不分
     * @param int $type 查找類型?: 0: 不分 1: 未結案 2: 已結案
     * @param string|null $keyWord 搜尋關鍵字
     * @param int $layoutId 需求單 layout
     * @param string $start 開始時間
     * @param string $end 結束時間
     * @return \Illuminate\Support\Collection
     */
    public function fetchSignHistoryLists($request)
    {

        $codeTable = $this->codeTable;
        $review = intval($request->input('review'));

        $getDemands = collect();
        $start = $request->get('start');

        // 結束日期要加一天
        $end = Carbon::parse($request->get('end'))->addDay()->toIso8601String();

        // 回傳查詢query
        $getDemands = $this->demandQuery->fetchDemandQuery($request->api_type, $review, $request->type, $request->key_word, $request->layout_id, $start, $end);

        $employees = CommonController::fetchEmployees($includeTrashed = true);
        // 撈取離職人員的部門
        $orgUnitMember = OrgUnitMember::withTrashed()->with('orgUnit')->whereIn('employee_id', $employees->pluck('id'))->get()->sortByDesc('updated_at');

        // 分頁器
        $perPage = isset($request->per) ? intval($request->per) : 10;

        $pageDemands = $getDemands->paginate($perPage);

        $getDemands = $pageDemands->getCollection();


        $getDemands->transform(
            function ($demand)  use ($codeTable,  $employees, $orgUnitMember) {
                $demand->status = $demand->apply_status;
                $demand->forms = $demand->payload['forms'];
                $demand->remarks = $demand->payload->get('remarks') ?: '';
                $signRoles = $demand->payload['sign_roles'];

                $roles = collect($employees)->whereIn('id', collect($signRoles)->pluck('role_id'))->values();
                foreach ($signRoles as $roleIndex => $role) {
                    $person = $roles->first(function ($q) use ($role) {
                        return $q['id'] == $role['role_id'];
                    });
                    $signRoles[$roleIndex]['name'] =  $person ?  $person['name'] : '';
                    if (isset($role['isRepresent']) && $role['isRepresent']) {
                        $signRoles[$roleIndex]['name'] .= '(代)';
                    }
                }

                $demand->sign_roles = $signRoles;
                $demand->createdBy = isset($demand->employee->payload['name']) ? $demand->employee->payload['name'] : '';
                if (isset($demand->employee)) {
                    $org_name =  $orgUnitMember->firstWhere('employee_id', $demand->employee->id)->orgUnit ?
                        $orgUnitMember->firstWhere('employee_id', $demand->employee->id)->orgUnit->payload['name'] : '';
                } else {
                    $org_name = '';
                }
                $demand->org_name = $org_name;
                $demand->name = $demand->payload->get('layout_name') ?: '';
                $demand->created = $this->formatDateWeek($demand->created_at, 'date_time', $codeTable);
                $demand->created_title = isset($demand->employee->payload['job_title']) ? $demand->employee->payload['job_title'] : '';
                // 摘要
                $demand->summaryValue =  $demand->payload->get('summaryValue');

                $demand->forms = collect($demand->forms)->map(function ($form, $formIndex) use ($codeTable, $demand) {
                    $form['columns'] = collect($form['columns'])->map(function ($column, $columnIndex) use ($formIndex, $codeTable, $demand) {

                        if ($column['type'] == 'customList') {
                            $customList =  $demand->customList->findList($formIndex, $columnIndex);
                            $column['form_setting'] = $customList  ? $customList->payload['form_setting'] : [];
                            $column['custom_list'] = $customList  ? $customList->list : [];
                        }

                        if (isset($column['type']) && ($column['type'] == 'date' || $column['type'] == 'time') && isset($column['value'])) {
                            try {
                                $column['value'] =  $this->formatDateWeek($column['value'], 'date_time', $codeTable);
                            } catch (Exception $e) {
                                Log::error($column);
                                Log::error($e);
                            }
                        }
                        return $column;
                    });
                    return $form;
                });
                $demand->applicant =  $demand->payload->get('applicant');
                $demand->applicant_id = $employees->firstWhere('id', $demand->created_by)['employee_number'];
                return $demand;
            }
        );
        return $pageDemands->setCollection($getDemands);
    }
}
