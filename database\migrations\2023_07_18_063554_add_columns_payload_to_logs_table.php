<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddColumnsPayloadToLogsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('logs', function (Blueprint $table) {
            $table->jsonb('demand_id')->nullable();
            $table->jsonb('sign_roles')->nullable();
            $table->renameColumn('action', 'payload');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('logs', function (Blueprint $table) {
            $table->renameColumn('payload', 'action');
            $table->dropColumn('sign_roles');
            $table->dropColumn('demand_id');
        });
    }
}
