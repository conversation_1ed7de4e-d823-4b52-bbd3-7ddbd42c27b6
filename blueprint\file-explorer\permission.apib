FORMAT: 1A
# permissionSettings-api
權限設定相關的API

## get function permission data [api/explorer/permission/function]

### get function permission data [GET]
獲取功能權限資料

+ Response 200 (application/json)
    + AttributesF
        + status: 200 (number) - HTTP 狀態碼
        + message: "OK" (string) 成功訊息
        + data: (array)
            + (object)
                + id: 1 (number, required) - id
                + name: "權限設定" (string, required) - 功能名稱
                + member: (array) 可使用人員
                    + (object)
                        + id: 1 (number, required) - 員工id
                        + name: "李家安" (string, required) - 員工姓名
            + (object)
                + id: 2 (number, required) - id
                + name: "建立檔案" (string, required) - 功能名稱
                + member: (array) 可使用人員
                    + (object)
                        + id: 1 (number, required) - 員工id
                        + name: "李家安" (string, required) - 員工姓名

+ Response 400 (application/json)
    + Attributes
        + status: 400 (number) - HTTP 狀態碼
        + message: "error message" (string) - 錯誤訊息


## update function permission data [api/explorer/permission/function/:id]

### update function permission data [PUT]
更新功能權限可使用人員

+ Request (application/json)
    + Attributes
        + type: "auth" (string, required) - 更新功能 (auth or createWhite)
        + users: (array) 可使用人員
            + id: 1 (number, required) - 員工id

+ Response 200 (application/json)
    + Attributes
        + status: 200 (number) - HTTP 狀態碼
        + message: "OK" (string) 成功訊息

+ Response 400 (application/json)
    + Attributes
        + status: 400 (number) - HTTP 狀態碼
        + message: "error message" (string) - 錯誤訊息


## folder permission [api/explorer/permission/folder]

### get folder permission data [GET]
獲取所有資料夾權限資料

+ Response 200 (application/json)
    + Attributes
        + status: 200 (number) - HTTP 狀態碼
        + message: "OK" (string) 成功訊息
        + data: (array)
            + (object)
                + id: 1 (number, required) - 資料夾id
                + name: "資料夾" (string, required) - 資料夾名稱
                + parentId: 0 (number, required) - 父級資料夾id
                + admin: (array)
                    + (object)
                        + id: 1 (number, required) - 員工id
                        + name: "李家安" (string, required) - 員工姓名
                + read: (array)
                    + (object)
                        + id: 1 (number, required) - 員工id
                        + name: "李家安" (string, required) - 員工姓名
                + download: (array)
                    + (object)
                        + id: 1 (number, required) - 員工id
                        + name: "李家安" (string, required) - 員工姓名

+ Response 400 (application/json)
    + Attributes
        + status: 400 (number) - HTTP 狀態碼
        + message: "error message" (string) - 錯誤訊息


## folder permission [api/explorer/permission/folder/:folderId]

### get folder permission data [GET]
獲取單個資料夾權限資料

+ Response 200 (application/json)
    + Attributes
        + status: 200 (number) - HTTP 狀態碼
        + message: "OK" (string) 成功訊息
        + data: (object)
            + admin: (array)
                + (object)
                    + id: 1 (number, required) - 員工id
                    + name: "李家安" (string, required) - 員工姓名
            + read: (array)
                + (object)
                    + id: 1 (number, required) - 員工id
                    + name: "李家安" (string, required) - 員工姓名
            + download: (array)
                + (object)
                    + id: 1 (number, required) - 員工id
                    + name: "李家安" (string, required) - 員工姓名

+ Response 400 (application/json)
    + Attributes
        + status: 400 (number) - HTTP 狀態碼
        + message: "error message" (string) - 錯誤訊息


### folder permission settings [PUT]
設定資料夾權限

+ Request (application/json)
    + Attributes
        + type: "admin" (string, required) - 權限類別
        + users: (array) - 權限人員
            + id: 1 (number, required) - 員工id

+ Response 200 (application/json)
    + Attributes
        + status: 200 (number) - HTTP 狀態碼
        + message: "OK" (string) 成功訊息

+ Response 400 (application/json)
    + Attributes
        + status: 400 (number) - HTTP 狀態碼
        + message: "error message" (string) - 錯誤訊息


## remove folder permission members [api/explorer/permission/folder/remove/:folderId]

### remove folder permission members [PUT]
清除資料夾瀏覽和下載權限人員

+ Response 200 (application/json)
    + Attributes
        + status: 200 (number) - HTTP 狀態碼
        + message: "OK" (string) 成功訊息

+ Response 400 (application/json)
    + Attributes
        + status: 400 (number) - HTTP 狀態碼
        + message: "error message" (string) - 錯誤訊息


## inherit folder permission [api/explorer/permission/folder/inherit/:folderId]

### inherit folder permission [POST]
繼承資料夾權限

+ Response 200 (application/json)
    + AttributesF
        + status: 200 (number) - HTTP 狀態碼
        + message: "OK" (string) 成功訊息

+ Response 400 (application/json)
    + Attributes
        + status: 400 (number) - HTTP 狀態碼
        + message: "error message" (string) - 錯誤訊息
