<template>
    <div>
        <div class="thead">
            <div
                class="w-1/3"
                v-for="(column, col_index) in columns"
                :key="col_index"
            >
                <span class="text-sm text-gray-400"> {{ column }}</span>
            </div>
        </div>
        <br />

        <div v-for="(dataList, index) in dataLists" :key="dataList.id">
            <div class="bg-white rounded-xl shadow p-4 pl-6 mb-5 flex w-full">
                <div class="w-1/3">
                    <span class="mr-4 my-auto font-bold"
                        >{{ dataList.name }}
                    </span>
                </div>
                <div class="w-1/3 flex-grow">
                    <div class="flex text-black">
                        <p
                            v-for="(
                                user, user_index
                            ) in dataList.user_names.slice(0, 5)"
                            :key="user.id"
                        >
                            {{ user.name }}

                            <span
                                v-if="
                                    user_index !==
                                    dataList.user_names.length - 1
                                "
                                >、</span
                            >
                        </p>
                        <span v-if="dataList.user_names.length > 5">...</span>
                    </div>
                </div>
                <div>
                    <button @click="openModel(index)">
                        <img src="@images/icon/edit_enable.png" alt="" />
                    </button>
                </div>
            </div>
        </div>
        <Dialog
            header="可編輯之人員"
            :visible.sync="displayModal"
            :dismissableMask="true"
            :containerStyle="{
                width: '30vw',
            }"
            :modal="true"
        >
            <div class="my-auto font-bold text-gray-400">
                <!-- :options="getRoles"  v-model="reviewToNodeId" @input="invalids.orgs = false"-->

                <div class="pb-8">
                    <span>可查看之人員</span>
                    <br />

                    <MultiSelect
                        class="w-full"
                        placeholder="選擇人員"
                        :options="getEmployees"
                        optionLabel="name"
                        optionValue="id"
                        data-key="id"
                        :showClear="true"
                        :filter="true"
                        v-model="users"
                    >
                        <template #option="slotProps">
                            <div>
                                <span>{{ slotProps.option.org_name }}</span>
                                <span>{{ slotProps.option.name }}</span>
                            </div>
                        </template>
                    </MultiSelect>
                </div>
            </div>
            <template #footer>
                <Button
                    @click="displayModal = false"
                    label="取消"
                    class="p-button-outlined p-button-secondary w-28 mr-5"
                />
                <Button @click="save()" label="儲存" class="w-28" />
            </template>
        </Dialog>
        <ConfirmDialog ref="confirmDialog" />

        <Toast ref="toast" position="top-center" />
    </div>
</template>
<script>
import axios from "axios";
import MultiSelect from "primevue/multiselect";
import Button from "primevue/button";
import Toast from "primevue/toast";
import ConfirmDialog from "primevue/confirmdialog";
import Dialog from "primevue/dialog";
export default {
    components: {
        MultiSelect,
        Toast,
        ConfirmDialog,
        Dialog,
        Button,
    },
    data() {
        return {
            columns: ["需求單", "可編輯之人員", ""],
            dataLists: [],
            apiURL: "/api/demand/setting/auth/data",
            displayModal: false,
            index: 0,
            getEmployees: [],
            users: [],
        };
    },
    mounted() {
        this.fetchAuth();
        this.getEmployeesDropDown();
    },
    methods: {
        fetchAuth() {
            axios
                .get(this.apiURL)
                .then((response) => {
                    this.dataLists = response.data ?? [];
                })
                .catch((error) => {
                    console.error(error);
                });
        },
        openModel(index) {
            this.displayModal = true;
            this.index = index;
            this.users = this.dataLists[index].users;
        },
        save() {
            let param = {
                id: this.dataLists[this.index].id,
                users: this.users,
            };
            axios
                .post(this.apiURL, param)
                .then((response) => {
                    if (response.data) {
                        this.displayModal = false;
                        this.fetchAuth();
                        this.$refs.toast.add({
                            severity: "success",
                            summary: "編輯成功",
                            life: 3000,
                        });
                    }
                })
                .catch((error) => {
                    console.error(error);
                    this.$refs.toast.add({
                        severity: "warn",
                        summary: "編輯失敗",
                        life: 3000,
                    });
                });
        },
        getEmployeesDropDown() {
            axios
                .get("/api/demand/employees")
                .then((response) => {
                    this.getEmployees = response.data ?? [];
                })
                .catch((error) => {
                    console.error(error);
                });
        },
    },
};
</script>
<style>
.p-dialog-content {
    height: 25rem;
}
</style>
