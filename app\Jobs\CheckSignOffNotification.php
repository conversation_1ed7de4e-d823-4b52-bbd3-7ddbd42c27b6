<?php

namespace App\Jobs;

use App\Modules\Demand\Models\Company;
use App\Modules\Demand\Models\Demand;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use App\Mail\DemandsMail;
use App\Modules\Demand\Models\Employee;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;
use Exception;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Str;

class CheckSignOffNotification implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $company_id;
    protected $msg;
    protected $employees;
    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($company_id, $msg)
    {
        $this->company_id = $company_id;
        $this->msg = $msg;
        $this->employees = Employee::all();
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        try {
            // 全部合併指寄出一封信
            $this->allSignOffRequest();
            // 更新通知日期
            $this->updateNotifydate();
        } catch (Exception $e) {
            Log::error($e);
        }
    }
    private function updateNotifydate()
    {
        Company::find($this->company_id)
            ->forceFill(['metadata->lastNotificationDate->demandSignOff' => now()->toISOString()])
            ->save();
    }

    private function listCount($key, $list, $isProxy)
    {
        $count = $list->where('user_id', $key)->count();
        if ($isProxy) {
            $agentCount = $list->where('agent', $key)->count();
            $count = $count + $agentCount;
        }
        return  $count;
    }

    private function sendEmail($demandList)
    {
        foreach ($demandList as $key => $demand) {
            try {
                $employee = $this->employees->where('id', $key)->first();
                $email = $employee ? $employee->payload->get('email') : null;
                $name = $employee ? $employee->payload->get('name') : null;
                if ($email && Str::contains($email, '@')) {
                    $msg = $this->msg;
                    // 替換文本內容，改為需求單筆數
                    $msg = str_replace('{name}', $name, $msg);
                    $msg = str_replace('{x}', $demand['count'], $msg);
                    $data = ['subject' => '行政服務系統通知', 'msg' => $msg . '<br/ > 寄出時間: ' . Carbon::now()->setTimezone('Asia/Taipei')->format('Y-m-d H:i:s'), 'email' => $email];
                    Mail::to($email)
                        ->queue((new DemandsMail($data))->onQueue('sending_email'));
                }
            } catch (Exception $e) {
                Log::error('mail:' . $email);
                Log::error($e);
            }
        }
    }

    public function allSignOffRequest()
    {
        //撈出需求單
        $demandList = $this->demandsCheck();

        Log::info(json_encode($demandList));
        $this->sendEmail($demandList);
    }
    public function demandsCheck()
    {
        $demands = Demand::whereIn('payload->status', [1, 4])->where('payload->company_id', $this->company_id)->get();
        $newDemands = $demands->groupBy(function ($demand) {
            return collect(collect($demand->payload->get('sign_roles'))->where('apply_status', 1)->first())->get('role_id');
        });
        $newDemands = $newDemands->map(function ($demand, $key) {
            return [
                'employee_id' => $key,
                'count' => $demand->count()
            ];
        });
        return $newDemands;
    }
}
