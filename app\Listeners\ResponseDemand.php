<?php

namespace App\Listeners;

use App\Events\DemandUpdated;
use App\Modules\Demand\Models\CustomList;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Http;

class ResponseDemand
{
    /**
     * Create the event listener.
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(DemandUpdated $event): void
    {
        try {
            $demand = $event->demand;
            //"responseUrl": "http://dev-acc.fdmc.com.tw/api/acc/asap-resonse/pj/report"
            // $responseUrl = "http://127.0.0.1:8000/api/acc/asap-resonse/pj/report";
            $responseUrl = is_array($demand) ? $demand[0]->metadata?->get('responseUrl') : $demand->metadata?->get('responseUrl');
            if (!isset($responseUrl)) {
                return;
            }

            \Log::info('ResponseDemand start');
            // 系統跑流程時會因為量太大，打api太頻繁，導致429失敗，所以改成array傳送
            if (is_array($demand)) {
                $ids = array_column($demand, 'id');
                $customList = CustomList::wherein('demand_id', $ids)->get();
            } else {
                $customList = CustomList::where('demand_id', $demand->id)->first();
            }
            $data = [
                'demand' => $demand,
                'customList' => $customList,
            ];

            $response = Http::post($responseUrl, $data);
            if ($response->failed()) {
                $response->throw();
            }
            \Log::info('ResponseDemand end');
        } catch (\Exception $e) {
            // DB::rollBack();
            \Log::error($e);

        }
    }
}
