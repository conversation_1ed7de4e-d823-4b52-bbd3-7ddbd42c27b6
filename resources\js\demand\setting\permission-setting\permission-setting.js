import '@/bootstrap.js';

import Banner from '@/demand/common/Banner.vue';
import PermissionSetting from './permission-setting.vue';
import DataAuthSetting from './data-auth-setting.vue';
const app = new Vue({
    el: '#content',
    components: {
        Banner,
        PermissionSetting,
        DataAuthSetting
    },
    data: {
        titles: [
            '功能權限',
            '資料權限'
        ],
        currentTab : 0,
    },

    mounted() {

    }
});
