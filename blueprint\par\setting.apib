## create apparatus [api/par/apparatus]
新增設備明細
### create apparatus [POST]

+ Request (application/json)
    + Attributes
        + form (object): 新增設備的相關資訊
            name : 測量儀 (string, required) - 設備名稱
            type : 工程用 (string, required) - 設備分類
            amount : 2 (number, required) - 設備數量
            no : PA112233 (string, required) - 設備編號
            org_unit_id : 12 (number, required) - 所屬部門
            admin_ids : [12,13] (Array, required) - 設備管理人
            remark : 說明內文 (string, required) - 設備說明
            images (Array):
                (object): 設備圖片的相關資訊
+ Response 200 (application/json)
    + Attributes
        state : 1 or 0 (number) - 成功或失敗
        error (string) 失敗原因 - 失敗原因


## edit apparatus [api/par/apparatus/2]
編輯設備明細
### edit apparatus [GET]

+ Request (application/json)
    + Attributes
        + form (object): 新增設備的相關資訊
            name : 測量儀 (string, required) - 設備名稱
            type : 工程用 (string, required) - 設備分類
            amount : 2 (number, required) - 設備數量
            no : PA112233 (string, required) - 設備編號
            org_unit_id : 12 (number, required) - 所屬部門
            admin_ids : [12,13] (Array, required) - 設備管理人
            remark : 說明內文 (string, required) - 設備說明
            images (Array):
                (object): 設備圖片的相關資訊
+ Response 200 (application/json)
    + Attributes
        state : 1 or 0 (number) - 成功或失敗
        error (string) 失敗原因 - 失敗原因


## apparatus [api/par/apparatus]
設備列表
### apparatus [GET]

+ Request (application/json)
    + Attributes
        + page : 1 (number, required) - 頁數
        + per : 10 (number, required) - 分頁數
        + key : 測量 (string, required) - 搜尋文字
+ Response 200 (application/json)
    + Attributes
        (object): 設備的相關資訊(含分頁器)
            data (Array) :
                (object) : 設備列表
                    id : 12 (number, required) - 類別id
                    image_url : /storage/par/image/.... (string, required) - 設備圖片(第一張)
                    name : 測量儀 (string, required) - 設備名稱
                    amount : 2 (number, required) - 設備剩餘數量
                    total_amount : 3 (number, required) - 設備總數量
                    no : PA112233 (string, required) - 設備編號
                    state : 可預約/預約中 (string, required) - 預約狀態
                    open : true or false (boolean, required) - 開放狀態
                    subscribers (Array) :
                        設二部 李家安 (string, required) - 預約人
                    recipients (Array) :
                        (object) : 領取人資料
                            employee_id :   12 (number, required) - 領取人id
                            name : 設二部 李家安 (string, required) - 領取人
                            time : 2023/03/29 15:00  (string, required) - 領取時間
                            remark : 備註內容  (string, required) - 備註
            current_page : 1 (number, required) - 當前頁數
            per_page : 10 (number, required) - 分頁數
            total : 10 (number, required) - 總頁數


## create apparatus [api/par/apparatus/recipients]
新增領取人明細
### create apparatus [POST]

+ Request (application/json)
    + Attributes
        + (object) : 領取人資料
             apparatus_id : 12 (number, required) - 類別id
             employee_id : 12 (number, required) - 領取人id
             remark : 備註內容  (string) - 備註
+ Response 200 (application/json)
    + Attributes
        state : 1 or 0 (number) - 成功或失敗
        error (string) 失敗原因 - 失敗原因


## edit apparatus [api/par/apparatus/recipients]
移除領取人明細
### edit apparatus [DELETE]

+ Request (application/json)
    + Attributes
        + (object) : 領取人資料
             apparatus_id : 12 (number, required) - 類別id
             employee_id : 12 (number, required) - 領取人id
+ Response 200 (application/json)
    + Attributes
        state : 1 or 0 (number) - 成功或失敗
        error (string) 失敗原因 - 失敗原因


## delete apparatus [api/par/apparatus/delete]
批次刪除設備明細
### delete apparatus [POST]

+ Request (application/json)
    + Attributes
        + id : 1 (number, required) or [1, 2, 3] (Array) - 設備明細的id
+ Response 200 (application/json)
    + Attributes
        state : 1 or 0 (number) - 成功或失敗
        error (string) 失敗原因 - 失敗原因

## switch apparatus/postulates [api/par/switch]
設備或設施開關
### switch apparatus/postulates [PUT]

+ Request (application/json)
    + Attributes
        + id : 12 (number, required) - 類別id
        + type : postulate/apparatus - 設施/設備
        + open : true or false (boolean, required) - 開放狀態
+ Response 200 (application/json)
    + Attributes
        state : 1 or 0 (number) - 成功或失敗
        error (string) 失敗原因 - 失敗原因

## postulates [api/par/postulate]
設施列表
### postulate [GET]

+ Request (application/json)
    + Attributes
        + page : 1 (number, required) - 頁數
        + per : 10 (number, required) - 分頁數
        + key : 會議室 (string, required) - 搜尋文字
+ Response 200 (application/json)
    + Attributes
        (object):設施的相關資訊(含分頁器)
            data (Array) :
                (object) : 設備列表
                    id : 12 (number, required) - 類別id
                    image_url : /storage/par/image/.... (string, required) - 設備圖片(第一張)
                    name : 測量儀 (string, required) - 設備名稱
                    state : 可預約/預約中 (string, required) - 預約狀態
                    subscribers (Array) :
                        設二部 李家安 (string, required) - 預約人
            current_page : 1 (number, required) - 當前頁數
            per_page : 10 (number, required) - 分頁數
            total : 10 (number, required) - 總頁數


## create postulate [api/par/postulate]
新增設施明細
### create postulate [POST]

+ Request (application/json)
    + Attributes
        + form (object): 新增設施的相關資訊
            name : 會議室 (string, required) - 設備名稱
            place : 10樓 (string, required) - 設施地點
            people : 10 (number, required) - 容納人數
            remark : 說明內文 (string, required) - 設施說明
            images (Array):
                (object): 設備圖片的相關資訊
            apparatus_type (Array) - 設備支援類別或是無此類別(字串)
                1 (number, required) - 設施類別的Id
                攝影機 (string, required) - 無類別
+ Response 200 (application/json)
    + Attributes
        state : 1 or 0 (number) - 成功或失敗
        error (string) 失敗原因 - 失敗原因


## edit postulate [api/par/postulate/2]
編輯設施明細
### edit postulate [GET]

+ Request (application/json)
    + Attributes
        + form (object): 新增設施的相關資訊
            name : 會議室 (string, required) - 設備名稱
            place : 10樓 (string, required) - 設施地點
            people : 10 (number, required) - 容納人數
            remark : 說明內文 (string, required) - 設施說明
            images (Array):
                (object): 設備圖片的相關資訊
            apparatus_type (Array) - 設備支援類別或是無此類別(字串)
                1 (number, required) - 設施類別的Id
                攝影機 (string, required) - 無類別
            notified (Array) - 需通知人員的Id
                [1, 2, 3]
+ Response 200 (application/json)
    + Attributes
        state : 1 or 0 (number) - 成功或失敗
        error (string) 失敗原因 - 失敗原因


## delete postulate [api/par/postulate/delete]
批次刪除設備明細
### delete postulate [POST]

+ Request (application/json)
    + Attributes
        + id : 1 (number, required) or [1, 2, 3] (Array) - 設施明細的id
+ Response 200 (application/json)
    + Attributes
        state : 1 or 0 (number) - 成功或失敗
        error (string) 失敗原因 - 失敗原因
