<?php

namespace App\Traits;

use Illuminate\Http\JsonResponse;
use Symfony\Component\HttpFoundation\Response;

trait ApiResponder
{
    public function successResponse($data, int $code = Response::HTTP_OK): JsonResponse
    {
        return response()->json([
            'data' => $data,
            'code' => $code,
        ], $code)->header('Content-Type', 'application/json');
    }

    public function errorResponse($error, int $code): JsonResponse
    {
        return response()->json([
            'error' => $error,
            'code'  => $code,
        ], $code)->header('Content-Type', 'application/json');
    }
}
