<?php

namespace App\Modules\par\Models;

use App\Modules\Demand\Models\Employee;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\Session;

class Reserve extends Model
{
    use SoftDeletes;
    protected $fillable = ['company_id', 'employee_id', 'pa_id', 'type', 'payload', 'metadata', 'created_by', 'updated_by'];
    protected $casts = ['payload' => 'collection', 'metadata' => 'collection'];

    public function scopeCompany($query)
    {
        return $query->where('reserves.company_id', Session::get('CompanyId'));
    }

    public function scopeNotRelease($query)
    {
        return $query->whereNull('reserves.metadata->release');
    }

    public function scopeDate($query, $date)
    {
        return $query->where('payload->date', $date);
        // ->orWhereBetween('payload->date', [$date,$date]);
    }

    public function scopeLiveReserve($query, $today)
    {
        return $query->where('reserves.payload->date', '>=', $today);
    }

    public function scopePostulateAp($query, $masterReserveId)
    {
        return $query->when(
            is_array($masterReserveId)
            ,function($query) use($masterReserveId) {
                $query->wherein('payload->master_reserve_id', $masterReserveId);
            }
            ,function($query) use($masterReserveId) {
                $query->where('payload->master_reserve_id', $masterReserveId);
            }
        );

    }


    public function employee()
    {
        return $this->belongsTo(Employee::class, 'employee_id');
    }

    public function postulate()
    {
        return $this->belongsTo(Postulate::class, 'pa_id', 'id');
    }
    public function apparatus()
    {
        return $this->belongsTo(Apparatus::class, 'pa_id', 'id');
    }
}
