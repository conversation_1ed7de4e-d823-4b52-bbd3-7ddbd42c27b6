<?php

namespace Database\Factories;

/** @var \Illuminate\Database\Eloquent\Factory $factory */

use App\Modules\par\Models\Apparatus;
use App\Modules\par\Models\Postulate;
use App\Modules\par\Models\Reserve;
use Faker\Generator as Faker;

$factory->define(Reserve::class, function (Faker $faker) {
    // apparatus
    // $apparatus = Apparatus::all()->random();

    $apparatus = Apparatus::inRandomOrder()->first();
    return [
        'company_id' => 1,
        'employee_id' => $faker->numberBetween(1, 600),
        'pa_id' => $apparatus->id,
        'type' => 'apparatus',
        'payload' => [
            "name" => $apparatus->name,
            // $faker->optional($weight = 0.9, $default = 'abc')->word; // 10% chance of 'abc'
            "amount" => $faker
                ->optional(0.9, $apparatus->payload['amount'])
                ->numberBetween(1, $apparatus->payload['amount']),
            "date" => now()->format('Y-m-d'),
            "time" => [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21],
            // "date" => $faker->iso8601,
            // dateTimeBetween($startDate = '-30 years', $endDate = 'now', $timezone = null)
            // "date" => $faker->dateTimeBetween('-6 months', '+6 months', 'Asia/Taipei'),
            // "time" => $faker->randomElements([0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16], 2),
            "reason" => $faker->text(20),
            "remark" => $faker->text(20)
        ],
        'metadata' => [],
        'created_by' => 1,
        'updated_by' => 1,
    ];


    // postulate


    $postulate = Postulate::inRandomOrder()->first();
    return [
        'company_id' => 1,
        'employee_id' => $faker->numberBetween(1, 600),
        'pa_id' => $postulate->id,
        'type' => 'postulate',
        'payload' => [
            "name" => $postulate->name,
            "apparatus_type" => $faker->randomElements($postulate->payload['apparatus_type'], 2),
            "apparatus_id" => $faker->randomElements([0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16], 2),

            "date" => now()->format('Y-m-d'),
            "time" => [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21],
            "reason" => $faker->text(20),
            "remark" => $faker->text(20)
        ],
        'metadata' => [],
        'created_by' => 1,
        'updated_by' => 1,
    ];
});
