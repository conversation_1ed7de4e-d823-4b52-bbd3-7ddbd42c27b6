<template>
    <div class="w-full p-6 bg-gray-50">
        <div class="flex flex-wrap gap-8 md:justify-end w-full">
            <p class="my-auto">每頁資訊</p>
            <Dropdown
                class="w-36"
                v-model="selectedPer"
                :options="per"
                optionLabel="name"
                optionValue="value"
                @input="setParentsPer($event)"
            />
            <p class="my-auto">共{{ data.total }}筆</p>
            <div class="flex gap-5">
                <button
                    @click="fetchPage(data.current_page - 1, selectedPer)"
                    class="inline-block"
                    :disabled="data.current_page == 1"
                    :class="[
                        data.current_page <= 1
                            ? 'text-gray-200 cursor-default'
                            : 'text-current',
                    ]"
                >
                    <i class="fas fa-chevron-left"></i>
                </button>
                <p class="my-auto">{{ data.current_page }}</p>
                <button
                    @click="fetchPage(data.current_page + 1, selectedPer)"
                    :disabled="
                        data.next_page_url == null && data.last_page == 1
                    "
                    class="inline-block"
                    :class="[
                        data.next_page_url == null && data.last_page == 1
                            ? 'text-gray-200 cursor-default'
                            : 'text-current',
                    ]"
                >
                    <i class="fas fa-chevron-right"></i>
                </button>
            </div>
        </div>
    </div>
</template>
<script>
import Dropdown from "primevue/dropdown";
export default {
    components: {
        Dropdown,
    },
    props: ["data", "sPer"],
    watch: {
        selectedPer: {
            handler: function () {
                this.fetchPage(1, this.selectedPer);
            },
        },
    },
    data() {
        return {
            per: [
                { name: "100 　 筆", value: 100 },
                { name: "60　　筆", value: 60 },
                { name: "30　　筆", value: 30 },
                { name: "10　　筆", value: 10 },
            ],
            selectedPer: this.sPer || 10,
        };
    },
    methods: {
        fetchPage(page, per) {
            this.$emit("page", page, per);
        },
        setParentsPer(e) {
            if (this.$parent.sPer) {
                this.$parent.sPer = e;
            }
        },
    },
};
</script>
