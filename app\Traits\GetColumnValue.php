<?php

namespace App\Traits;

use Carbon\Carbon;
use Illuminate\Support\Facades\Session;

trait GetColumnValue
{
    // protected $company_id;
    // protected $user_id;
    // protected $timezone;

    // public function __construct()
    // {
    //     // $this->company_id = Session::get('CompanyId');
    //     // $this->user_id = Session::get('employee_id');
    //     $this->timezone = Session::get('timezone');
    // }

    public function getValue($column, $pdf = false)
    {
        $value = '';
        switch ($column['type']) {
            case 'input':
            case 'number':
            case 'total':
            case 'database':
            case 'cascade':
                $value = isset($column['value']) ? $column['value'] : '';
                break;
            case 'dropdown':
                $value = isset($column['value']) ?
                    // 是不是多選
                    (isset($column['mul']) ?
                        ($column['mul'] == 1 ?
                            collect($column['value'])->implode(',') :
                            $column['value']['name']) :
                        $column['value']['name'])
                    : '';
                break;
            case 'employee':
                $value = isset($column['value']) ? $column['value']['name'] : '';
                break;
            case 'money':
                if ($pdf) {
                    $value = isset($column['value']) ? number_format($column['value'], 2, '.', ',') . '元' : '';
                } else {
                    $value = isset($column['value']) ? $column['value'] : 0;
                }
                break;
            case 'single':
                $value = isset($column['value']) ?
                    ($column['value']['name'] == '其他' ? ($column['value']['option'] ?? '其他') : $column['value']['name'])
                    : '';
                break;
            case 'date':
                $value = isset($column['value']) ? Carbon::parse($column['value'])->setTimezone(Session::get('timezone','Asia/Taipei'))->format('Y-m-d') : '';
                break;
            case 'multi':
                $value = isset($column['value']) ? collect($column['value'])->pluck('name')->implode(' 、 ') : '';
                break;
            default:
                $value = '';
        }

        return $value;
    }
}
