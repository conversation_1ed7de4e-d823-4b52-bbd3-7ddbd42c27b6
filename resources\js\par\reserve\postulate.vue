<template>
    <div>
        <!-- 切換預約模式 -->
        <SelectButton
            v-show="$root.action == 0"
            v-model="is_search_by_time"
            :options="search_by_type"
            optionLabel="name"
            optionValue="is_by_time"
            class="my-4 shadow-none"
            @input="
                if_search = true;
                app_id = null;
                data_lists = [];
                form.date = new Date();
                displayReservesInfo = false;
                selected_begin = null;
                selected_end = null;
                selected_begin_date = null;
                selected_end_date = null;
            "
        />

        <div v-show="$root.action == 0">
            <!-- 依日期查詢預約狀況 -->
            <section v-if="is_search_by_time">
                <SearchBar
                    :begin_time="begin_time"
                    :end_time="end_time"
                    :selected_date="form.date"
                    :selected_begin="selected_begin"
                    :selected_end="selected_end"
                    :searchInput="{ app_id, date_search }"
                    :if_search="if_search"
                    :todayMidnight="todayMidnight"
                    @disableButton="(val) => (if_search = val)"
                    @selectedId="(val) => (app_id = val)"
                    @selectedDate="
                        (val) => {
                            form.date = val;
                            selected_begin = null;
                            selected_end = null;
                        }
                    "
                    @updateSelectedBegin="(val) => (selected_begin = val)"
                    @updateSelectedEnd="(val) => (selected_end = val)"
                    @search="
                        fetchDataAndCallReserved(app_id, form.date);
                        displayReservesInfo = true;
                    "
                />
                <hr class="my-6 text-gray-200" />
                <!-- 未搜尋前的圖片 -->
                <div
                    :style="{
                        height: `calc(100vh - ${search_photo_y}px )`,
                    }"
                    ref="search_photo"
                    class="flex flex-col justify-center items-center"
                    v-if="!displayReservesInfo"
                >
                    <img src="@images/page_state/search.png" alt="" />
                    <p class="text-center mt-4">選擇您要查詢的日期與時間</p>
                </div>
                <!-- 搜尋結果 -->
                <div class="flex flex-wrap m-4" v-if="displayReservesInfo">
                    <div
                        v-for="(list, listIndex) in data_lists"
                        :key="list.id"
                        class="w-full p-4 box-border lg:w-1/2"
                    >
                        <ReserveItem
                            ref="reserveItem"
                            :data="list"
                            :dataIndex="listIndex"
                            :displayReserves="1"
                            :selectedBegin="selected_begin"
                            :selectedEnd="selected_end"
                            @selectedDuration="
                                (val) =>
                                    (form.time_ids = val.res_id) &&
                                    (form.pa_id = val.list_id) &&
                                    (form.name = val.list_name)
                            "
                        />
                    </div>
                </div>
            </section>
            <!-- 依會議室查詢可預約時段 -->
            <section v-else>
                <search-bar2
                    :selected_begin_date="selected_begin_date"
                    :selected_end_date="selected_end_date"
                    :selected_id="app_id"
                    :if_search="if_search"
                    :todayMidnight="todayMidnight"
                    @disableButton="(val) => (if_search = val)"
                    @updateSelectedId="(val) => (app_id = val)"
                    @updateSelectedBeginDate="
                        (val) => (selected_begin_date = val)
                    "
                    @updateSelectedEndDate="(val) => (selected_end_date = val)"
                    @search2="
                        fetchDataAndCallReserved2(app_id);
                        displayReservesInfo = true;
                    "
                />
                <hr class="my-6 text-gray-200" />
                <!-- 未搜尋前的圖片 -->
                <div
                    :style="{
                        height: `calc(100vh - ${search_photo_y}px )`,
                    }"
                    ref="search_photo"
                    class="flex flex-col justify-center items-center"
                    v-if="!displayReservesInfo"
                >
                    <img src="@images/page_state/search.png" alt="" />
                    <p class="text-center mt-4">選擇您要查詢的日期與時間</p>
                </div>
                <!-- 搜尋結果 -->
                <div class="flex flex-wrap m-4" v-if="displayReservesInfo">
                    <div
                        v-for="(list, listIndex) in data_lists"
                        :key="list.date"
                        class="w-full p-4 box-border"
                    >
                        <reserve-item2
                            ref="reserveItem2"
                            :data="list"
                            :dataIndex="listIndex"
                            :displayReserves="1"
                            :selectedBegin="selected_begin"
                            :selectedEnd="selected_end"
                            @selectedDuration2="(val) => selectedDuration2(val)"
                        />
                    </div>
                </div>
            </section>
        </div>
        <!-- 表單 -->
        <div v-if="$root.action == 1">
            <div class="text-center relative">
                <span
                    class="absolute left-0 mt-0.5 cursor-pointer"
                    @click="
                        $root.action = 0;
                        is_search_by_time
                            ? fetchDataAndCallReserved(app_id, form.date)
                            : fetchDataAndCallReserved2(app_id);
                    "
                >
                    <i class="fas fa-arrow-left"></i>
                </span>
                <h2 class="text-2xl font-semibold">設施預約</h2>
            </div>
            <ReserveItem
                :data="
                    edit_data == undefined ? data_lists[list_index] : edit_item
                "
                :dataIndex="list_index"
                :displayReserves="0"
                class="my-12"
            />
            <div class="demandList">
                <div class="block md:flex py-8 border-b border-gray-200">
                    <span class="my-auto mr-8">
                        預約日期<span class="text-red-500">*</span>
                    </span>
                    <div>
                        <Calendar
                            :class="{ 'p-invalid': formInvalid.date }"
                            placeholder="選擇預約日期"
                            v-model="form.date"
                            dateFormat="yy-mm-dd"
                            :manualInput="false"
                            :minDate="todayMidnight"
                            @date-select="
                                selected_begin = null;
                                selected_end = null;
                            "
                        />
                        <small
                            v-if="formInvalid.date"
                            class="text-red-500 block"
                            >請選擇預約日期</small
                        >
                    </div>
                </div>
                <div class="block md:flex py-8 border-b border-gray-200">
                    <span class="my-auto mr-8">
                        預約時段<span class="text-red-500">*</span>
                    </span>
                    <div class="flex">
                        <div class="pr-6">
                            <Dropdown
                                class="lg:w-44 xl:w-48 rounded-md shadow"
                                optionLabel="time"
                                optionValue="id"
                                data-key="id"
                                placeholder="請選擇開始時間"
                                :options="begin_time"
                                v-model="selected_begin"
                                optionDisabled="disabled"
                                @input="formInvalid.time_ids = false"
                            />
                        </div>
                        <p class="my-auto mr-8">至</p>
                        <div class="pr-6">
                            <Dropdown
                                class="lg:w-44 xl:w-48 rounded-md shadow"
                                optionLabel="time"
                                optionValue="id"
                                data-key="id"
                                placeholder="請選擇結束時間"
                                :options="end_time"
                                v-model="selected_end"
                                optionDisabled="disabled"
                                @input="formInvalid.time_ids = false"
                            />
                            <!--   timeValidate(); -->
                        </div>
                        <small
                            v-if="formInvalid.time_ids"
                            class="text-red-500 block"
                            >請選擇預約時段</small
                        >
                    </div>
                </div>
                <div class="block md:flex py-8 border-b border-gray-200">
                    <span class="my-auto mr-8">
                        預約事由<span class="text-red-500">*</span>
                    </span>
                    <div>
                        <Textarea
                            :class="{ 'p-invalid': formInvalid.reason }"
                            class="lg:w-96 w-72"
                            v-model="form.reason"
                            placeholder="輸入預約事由"
                            @input="formInvalid.reason = false"
                        />
                        <small
                            v-if="formInvalid.reason"
                            class="text-red-500 block"
                            >請填寫預約事由</small
                        >
                    </div>
                </div>
                <div
                    v-if="
                        edit_data == undefined
                            ? data_lists[list_index].apparatus_type.length !== 0
                            : edit_apps.length !== 0
                    "
                    class="block md:flex py-8 border-b border-gray-200"
                >
                    <span class="mr-10"> 請勾選需要IT支援的項目 </span>
                    <div
                        class="w-44"
                        v-for="(app_type, index) in edit_data == undefined
                            ? data_lists[list_index].apparatus_type
                            : edit_apps"
                        :key="index"
                    >
                        <div>
                            <Checkbox
                                class="mb-1"
                                :id="index"
                                v-model="type_checks[index].check"
                                :value="app_type"
                                :binary="true"
                                @change="typeToApps(app_type, index)"
                            />
                            <label class="my-auto" :for="index">{{
                                app_type
                            }}</label>
                        </div>
                        <Dropdown
                            v-if="
                                type_checks[index].check &&
                                type_checks[index].options !== null &&
                                type_checks[index].options.length > 0
                            "
                            v-model="type_checks[index].selected"
                            :options="type_checks[index].options"
                            optionLabel="name"
                        />
                    </div>
                </div>
                <div class="block md:flex py-8">
                    <span class="my-auto mr-16"> 備註 </span>
                    <div>
                        <Textarea
                            v-model="form.remark"
                            class="ml-1 lg:w-96 w-72"
                            placeholder="輸入備註"
                        />
                    </div>
                </div>
                <div
                    class="buttons mt-4 w-full flex justify-between md:justify-end"
                >
                    <Button
                        @click="
                            $root.action = 0;
                            is_search_by_time
                                ? fetchDataAndCallReserved(app_id, form.date)
                                : fetchDataAndCallReserved2(app_id);
                        "
                        label="取消"
                        class="p-button-outlined p-button-secondary w-28 mr-5"
                    />
                    <Button
                        :disabled="$root.loading"
                        @click="
                            reserve();
                            displayReservesInfo = false;
                        "
                        :label="edit_data == undefined ? '預約' : '再次預約'"
                        class="w-28"
                    />
                </div>
            </div>
        </div>
        <Toast ref="toast" position="top-center" />
        <!-- 預約時段重複警示彈窗 -->
        <Dialog
            :visible.sync="displayReservedWarning"
            :modal="true"
            :dismissableMask="true"
            :closable="false"
            :containerStyle="{
                width: '25vw',
                textAlign: 'center',
            }"
        >
            <img
                class="mx-auto mb-4"
                src="@images/popup_state/warning.png"
                alt=""
            />
            <p class="mb-2">以下時段已被預約</p>
            <p class="mb-12">{{ reserved_info }}</p>
            <Button
                label="我知道了"
                @click="
                    displayReservedWarning = false;
                    reserved_info = [];
                "
            ></Button>
        </Dialog>
        <div v-if="$root.loading">
            <div
                class="fixed top-1/2 left-1/2 transform -translate-y-1/2 -translate-x-1/2 z-50"
            >
                <ProgressSpinner />
            </div>
        </div>
    </div>
</template>
<script>
import axios from "axios";
import Button from "primevue/button";
import Calendar from "primevue/calendar";
import Checkbox from "primevue/checkbox";
import Dialog from "primevue/dialog";
import Dropdown from "primevue/dropdown";
import MultiSelect from "primevue/multiselect";
import InputText from "primevue/inputtext";
import InputNumber from "primevue/inputnumber";
import Textarea from "primevue/textarea";
import Toast from "primevue/toast";
import SearchBar from "@/par/common/search-bar";
import SearchBar2 from "@/par/common/search-bar2";
import SelectButton from "primevue/selectbutton";
import ReserveItem from "@/par/common/reserve-item";
import ReserveItem2 from "@/par/common/reserve-item2";
import ProgressSpinner from "primevue/progressspinner";
import reserveDuration from "@/share/ReserveDuration";

export default {
    components: {
        Button,
        Calendar,
        Checkbox,
        Dropdown,
        MultiSelect,
        InputText,
        InputNumber,
        Textarea,
        Toast,
        SearchBar,
        SearchBar2,
        SelectButton,
        ReserveItem,
        ReserveItem2,
        ProgressSpinner,
        Dialog,
    },
    props: ["edit_data"],
    data() {
        return {
            data_lists: [],
            list_index: null,
            date: new Date(),
            date_search: null,
            app_id: null,
            // 依日期查詢預約狀況
            selected_begin: null,
            selected_end: null,
            // 依會議室查詢可預約時段
            selected_begin_date: null,
            selected_end_date: null,
            if_search: true,
            displayReservesInfo: false,
            form: {
                pa_id: null,
                name: "",
                date: new Date(),
                time_ids: [],
                reason: "",
                remark: "",
                apparatus: [],
            },
            formInvalid: {
                date: false,
                time_ids: false,
                reason: false,
            },
            url: "/api/par/reserve/postulate",
            type_checks: [],
            type_dropdown: [],
            edit_item: {},
            edit_reserves: [],
            edit_apps: [],
            begin_time: new reserveDuration().durations,
            end_time: new reserveDuration().durations,
            search_photo_y: null,
            displayReservedWarning: false,
            reserved_info: [],
            is_search_by_time: true,
            search_by_type: [
                { name: "依日期查詢預約狀況", is_by_time: true },
                { name: "依會議室查詢可預約時段", is_by_time: false },
            ],
            search_by_time: {},
            todayMidnight: new Date(),
        };
    },
    mounted() {
        this.resetDateTime(this.form.date);
        this.resetDateTime(this.todayMidnight);
        this.fetchList(null, this.date);
        this.search_photo_y =
            this.$refs.search_photo.getBoundingClientRect().top;

        this.disabledTime(this.begin_time);
        this.disabledTime(this.end_time);
    },
    watch: {
        "$root.action": function (newValue) {
            if (newValue == 1) {
                this.form.date = this.date_search;
                this.data_lists[this.list_index].apparatus_type.forEach(
                    (key) => {
                        this.type_checks.push({
                            name: key,
                            check: false,
                            options: [],
                            selected: null,
                        });
                    }
                );
            } else {
                // this.form.date = null;
                this.form.time_ids = [];
                this.form.reason = "";
                this.form.remark = "";
                this.form.apparatus = [];
                this.type_checks = [];
            }
        },
        selected_begin(newVal) {
            this.end_time.forEach((item) => {
                item.disabled = false;
                if (item.id <= newVal && newVal != null) {
                    item.disabled = true;
                }
            });
            this.disabledTime(this.begin_time);

            this.formAddCheckedTime();
        },
        selected_end(newVal) {
            this.begin_time.forEach((item) => {
                item.disabled = false;
                if (item.id >= newVal && newVal != null) {
                    item.disabled = true;
                }
            });
            this.disabledTime(this.end_time);

            this.formAddCheckedTime();
        },
        "form.date"(newVal) {
            this.begin_time = this.getDurations();
            this.end_time = this.getDurations();
            this.disabledTime(this.begin_time);
            this.disabledTime(this.end_time);
            if (this.is_search_by_time) this.if_search = true;
        },
    },
    methods: {
        // 創建時間區段
        getDurations() {
            return new reserveDuration().durations;
        },
        // 今日已過時間不可選
        disabledTime(timeDuration) {
            if (new Date() < this.form.date) return;

            let currentTime = new Date().getHours();
            timeDuration.forEach((item) => {
                if (item.time.split(":")[0] < currentTime) {
                    item.disabled = true;
                }
            });
        },
        // 將該日期的時間重置
        resetDateTime(date) {
            date.setHours(0);
            date.setMinutes(0);
            date.setSeconds(0);
            date.setMilliseconds(0);
        },
        selectedDuration2(val) {
            this.selected_begin = val.reserve_ids[0];
            // 原本是半小時區間，現在改成一小時區間，所以要+1
            this.selected_end = val.reserve_ids[1] + 1;
            this.form.pa_id = val.list_id;
            this.form.name = val.list_name;
            this.date_search = new Date(val.date);
        },
        // 依日期查詢預約狀況
        async fetchList(id, date) {
            this.data_lists = [];

            try {
                const response = await axios.get(this.url, {
                    params: {
                        id: id == 0 ? null : id,
                        date: date,
                    },
                });

                this.date_search = this.form.date;
                this.data_lists = response.data;
                if (this.edit_data !== undefined) {
                    this.importValue();
                }
            } catch (error) {
                console.error(error);
            }
        },
        // 依會議室查詢可預約時段
        async fetchList2(id) {
            this.data_lists = [];
            try {
                const response = await axios.get(this.url + "/date", {
                    params: {
                        id,
                        start_date: this.selected_begin_date,
                        end_date: this.selected_end_date,
                    },
                });

                this.date_search = this.form.date;
                this.data_lists = response.data;
            } catch (error) {
                console.error(error);
            }
        },
        typeToApps(app_type, index) {
            // if (this.type_checks[index].check) {
            //     axios
            //         .post("/api/par/reserve/apparatus/dropdown", {
            //             date: this.form.date,
            //             time: this.form.time_ids,
            //             name: app_type,
            //         })
            //         .then((response) => {
            //             if (response.data)
            //                 this.type_checks[index].options.push(response.data);
            //             if (response.data == null) {
            //                 this.type_checks[index].selected = {
            //                     type: this.type_checks[index].name,
            //                 };
            //             } else {
            //                 // this.type_checks[index].options.forEach((op) => {
            //                 //     this.$set(op, "type", app_type);
            //                 // });
            //             }
            //         })
            //         .catch((error) => {
            //             console.error(error);
            //         });
            // } else {
            //     this.type_checks[index].selected = null;
            // }
            if (this.type_checks[index].check) {
                this.type_checks[index].selected = {
                    type: this.type_checks[index].name,
                };
            } else {
                this.type_checks[index].selected = null;
            }
        },
        // 處理送出驗證
        validate() {
            let error = false;
            this.formInvalid.date = this.form.date === null;
            this.formInvalid.time_ids =
                this.form.time_ids.length == 0 || this.form.time_ids[0] == null;
            this.formInvalid.reason = this.form.reason === "";
            Object.values(this.formInvalid).forEach((item) => {
                if (item == true) {
                    error = true;
                }
            });
            if (this.reserved_info.length > 0) {
                this.displayReservedWarning = true;
                error = true;
            }
            return error;
        },
        reserve() {
            if (this.validate() == true) {
                return;
            }
            this.$root.loading = true;
            this.type_checks.forEach((type_check) => {
                if (type_check.check && type_check.selected !== null) {
                    this.form.apparatus.push(type_check.selected);
                }
            });
            this.resetDateTime(this.form.date);
            axios
                .post(
                    this.edit_data == undefined
                        ? this.url
                        : "/api/par/reserve/again",
                    {
                        id: this.$root.reserve_id,
                        form: this.form,
                    }
                )
                .then((response) => {
                    if (response.data.state) {
                        this.$refs.toast.add({
                            severity: "success",
                            summary: "預約成功",
                            life: 3000,
                        });
                        this.$root.action = 0;
                        this.fetchList(null, this.date_search);
                    } else {
                        this.reserved_info = response.data.error;
                        // 預約時段合併
                        let combined_count = 0;
                        let reserved_info_length = this.reserved_info.length;
                        for (let i = 0; i < reserved_info_length - 1; i++) {
                            if (
                                this.reserved_info[combined_count].slice(6) ===
                                this.reserved_info[combined_count + 1].slice(
                                    0,
                                    5
                                )
                            ) {
                                let combined_duration =
                                    this.reserved_info[combined_count].slice(
                                        0,
                                        6
                                    ) +
                                    this.reserved_info[
                                        combined_count + 1
                                    ].slice(6);
                                this.reserved_info.splice(
                                    combined_count,
                                    2,
                                    combined_duration
                                );
                                combined_count--;
                            }
                            combined_count++;
                        }
                        this.reserved_info = this.reserved_info.join(" 、 ");
                        if (this.reserved_info.length > 0) {
                            this.displayReservedWarning = true;
                        }
                    }
                })
                .catch((error) => {
                    console.error(error);
                    this.$refs.toast.add({
                        severity: "error",
                        summary: "預約失敗",
                        life: 3000,
                    });
                })
                .finally(() => {
                    this.$root.loading = false;
                    // 清空表單暫存
                    this.selected_begin = null;
                    this.selected_end = null;
                    this.form.apparatus = [];
                    this.if_search = true;
                    if (!this.is_search_by_time) {
                        this.app_id = null;
                        this.selected_begin_date = null;
                        this.selected_end_date = null;
                    }
                });
        },
        // 修改預約匯入資料
        importValue() {
            this.$set(this.form, "type", "postulate");
            this.form.pa_id = this.edit_data.list.pa_id;
            this.form.name = this.edit_data.list.name;
            this.selected_begin = this.edit_data.times[0];
            this.selected_end =
                this.edit_data.times[this.edit_data.times.length - 1] + 1;
            this.form.time_ids = this.edit_data.times;
            this.form.remark = this.edit_data.remark;
            this.form.reason = this.edit_data.reason;
            this.form.date = new Date(this.edit_data.date);
            this.list_index = this.data_lists.findIndex(
                (list) => list.id == this.edit_data.list.pa_id
            );
            this.edit_item = this.edit_data.list;
            this.edit_reserves = this.data_lists[this.list_index].reserves;
            this.edit_reserves.forEach((reserve) => {
                delete reserve.disabled;
            });
            this.edit_apps = this.data_lists[this.list_index].apparatus_type;
            this.data_lists[this.list_index].apparatus_type.forEach((key) => {
                this.type_checks.push({
                    name: key,
                    check: false,
                    options: [],
                    selected: null,
                });
            });
            // if (this.edit_data.apparatus.length !== 0) {
            //     this.edit_data.apparatus.forEach((app) => {
            //         this.type_checks.forEach((type_check, index) => {
            //             if (type_check.name == app.type) {
            //                 type_check.check = true;
            //                 this.typeToApps(app.type, index);
            //                 type_check.selected = app;
            //             }
            //         });
            //     });
            // }
            if (this.edit_data.apparatus.length !== 0) {
                this.edit_data.apparatus.forEach((app) => {
                    this.type_checks.forEach((type_check, index) => {
                        if (type_check.name == app.type) {
                            type_check.check = true;
                            this.typeToApps(app.type, index);
                            type_check.selected = app;
                        }
                    });
                });
            }
        },
        // 依日期查詢預約狀況
        async fetchDataAndCallReserved(app_id, date) {
            this.resetDateTime(date);
            await this.fetchList(app_id, date);
            this.callReservedMethod();
            this.disabledTime(this.begin_time);
            this.disabledTime(this.end_time);
        },
        // 依會議室查詢可預約時段
        async fetchDataAndCallReserved2(app_id) {
            await this.fetchList2(app_id);
        },

        callReservedMethod() {
            if (this.$refs.reserveItem)
                this.$refs.reserveItem.forEach((item) => {
                    item.isReserved();
                });
        },
        /**
         * 將所選時段新增至表單
         */
        formAddCheckedTime() {
            this.form.time_ids = [];
            let bg_time = this.selected_begin;
            let count = this.selected_end - this.selected_begin;
            for (let i = 0; i < count; i++) {
                // if (this.begin_time[bg_time].disabled != true) {
                this.form.time_ids.push(bg_time);
                // }
                bg_time++;
            }
        },
    },
};
</script>
