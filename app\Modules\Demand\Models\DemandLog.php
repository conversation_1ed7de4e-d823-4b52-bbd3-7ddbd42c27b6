<?php

namespace App\Modules\Demand\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
class DemandLog extends Model
{
    //
    use SoftDeletes;
    use \Staudenmeir\EloquentJsonRelations\HasJsonRelationships;
    protected $fillable = ['id', 'no', 'layout_id', 'payload', 'created_by', 'updated_by','created_at'];
    protected $casts = ['payload' => 'collection'];

    public function layout()
    {
        return $this->belongsTo(DemandLayout::class);
    }

    public function employee()
    {
        return $this->hasOne(Employee::class, 'id', 'created_by');
    }

    public function customList(){
        return $this->hasMany(CustomList::class,'demand_id','payload->demand_id');
    }
}
