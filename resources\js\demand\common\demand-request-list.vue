<template>
    <div>
        <!-- 申請單列表 -->
        <div v-if="layer == 'outside'">
            <div
                @click="getListsRow"
                class="demandListSelect flex flex-col mb-5 cursor-pointer"
                :class="
                    !data.is_child && !is_counterSign && !is_againSign
                        ? 'pt-6 pl-6'
                        : ''
                "
            >
                <div>
                    <span
                        v-if="data.is_child"
                        class="px-1.5 text-xs py-1.5 bg-indigo-300 text-white rounded-tl-xl rounded-br-md"
                        >子單</span
                    >
                    <span
                        v-if="is_counterSign"
                        class="px-1.5 text-xs py-1.5 bg-indigo-600 text-white"
                        :class="
                            data.is_child
                                ? 'rounded-b-lg'
                                : 'rounded-tl-xl rounded-br-md'
                        "
                        >會簽</span
                    >
                    <span
                        v-if="is_againSign"
                        class="px-1.5 text-xs py-1.5 bg-yellow-500 text-white"
                        :class="
                            data.is_child || is_counterSign
                                ? 'rounded-b-lg'
                                : 'rounded-tl-xl rounded-br-md'
                        "
                        >再審</span
                    >
                </div>
                <div
                    class="sm:text-base text-sm flex sm:gap-6 gap-2 justify-between items-center"
                    :class="
                        data.is_child || is_counterSign || is_againSign
                            ? 'pl-6'
                            : ''
                    "
                >
                    <div class="flex w-1/6">
                        <input
                            @click.stop
                            @change="getId"
                            v-if="signOff"
                            :value="data.id"
                            :checked="isChecked"
                            class="mr-0 md:mr-4 mr-2"
                            type="checkbox"
                        />
                        <p class="cursor-pointer">
                            {{ data.name }}
                        </p>
                    </div>
                    <div class="flex w-1/6">
                        <p class="cursor-pointer">
                            {{ data.no }}
                        </p>
                    </div>
                    <div class="flex w-1/6">
                        <p class="cursor-pointer">
                            {{ data.created }}
                        </p>
                    </div>
                    <div v-if="'createdBy' in data" class="flex w-1/6">
                        <p class="cursor-pointer">
                            {{ data.createdBy }}
                        </p>
                    </div>
                    <div
                        v-if="'org_name' in data"
                        class="flex w-1/6 hidden sm:block"
                    >
                        <p class="cursor-pointer">
                            {{ data.org_name }}
                        </p>
                    </div>
                    <div class="flex w-1/6 hidden sm:block">
                        <p class="cursor-pointer">
                            {{ data.summaryValue }}
                        </p>
                    </div>
                    <div
                        class="w-16 rounded-full py-1.5 border sm:text-sm text-xs text-center my-auto"
                        :class="
                            signOff == 1 && data.status == 4
                                ? changeState(1, 0)
                                : signOff == 1 &&
                                  data.sign_roles.length >= 2 &&
                                  data.sign_roles[data.sign_roles.length - 2]
                                      .apply_status == 6
                                ? changeState(6, 0)
                                : type
                                ? [changeTypeColor(data.type)]
                                : [changeState(data.status, 0)]
                        "
                    >
                        <span>
                            {{
                                signOff == 1 && data.status == 4
                                    ? changeState(1, 1)
                                    : signOff == 1 &&
                                      data.sign_roles.length >= 2 &&
                                      data.sign_roles[
                                          data.sign_roles.length - 2
                                      ].apply_status == 6
                                    ? changeState(6, 1)
                                    : type
                                    ? tag
                                    : changeState(data.status, 1)
                            }}
                        </span>
                    </div>
                </div>
            </div>
        </div>
        <!-- 申請單內頁 -->
        <div v-if="layer == 'inner'" style="max-width: 1624px" class="mx-auto">
            <div class="flex justify-between noPrint mb-4">
                <Button
                    @click="back()"
                    class="nowrap-button p-button-outlined h-12 p-button-secondary flex gap-2"
                >
                    <i class="fas fa-arrow-left"></i>
                    <span>返回</span>
                </Button>
                <div class="flex gap-2">
                    <Button
                        @click="pdfBtn('excel')"
                        label="匯出 Excel"
                        class="nowrap-button p-button-outlined w-28 h-12 p-button-secondary transition-all duration-300"
                        :class="{ 'w-36': printingExcel }"
                        :loading="printingExcel"
                        :disabled="printingExcel"
                    />
                    <Button
                        @click="pdfBtn('word')"
                        label="匯出 PDF"
                        class="nowrap-button p-button-outlined w-28 h-12 p-button-secondary transition-all duration-300"
                        :class="{ 'w-36': printingPdf }"
                        :loading="printingPdf"
                        :disabled="printingPdf"
                    />
                </div>
            </div>
            <div style="max-width: 1624px" class="demandList">
                <div class="w-full relative">
                    <h2
                        class="text-left md:text-center text-2xl font-semibold whitespace-nowrap"
                    >
                        {{ data.name }}
                    </h2>
                </div>
                <div class="flex justify-start md:justify-end mt-6">
                    <div>
                        <span class="text-gray-400">申請日期｜</span
                        >{{ data.created }}
                    </div>
                    &emsp;
                    <div>
                        <span class="text-gray-400">單號｜</span>{{ data.no }}
                    </div>
                </div>
                <!-- applicant info ↓ -->
                <div class="flex justify-around w-full mt-8 mb-7">
                    <div class="flex flex-col items-center flex-1">
                        <span> 申請人 </span>
                        <p class="text-lg font-semibold text-center">
                            {{ data.applicant.name
                            }}{{
                                data.applicant_id ? "_" + data.applicant_id : ""
                            }}
                        </p>
                    </div>
                    <div class="flex flex-col items-center flex-1">
                        <span> 部門 </span>
                        <p class="text-lg font-semibold text-center">
                            {{ data.applicant.dep }}
                        </p>
                    </div>
                    <div class="flex flex-col items-center flex-1">
                        <span> 職稱 </span>
                        <p class="text-lg font-semibold text-center">
                            {{ data.applicant.title }}
                        </p>
                    </div>
                </div>
                <!-- applicant info ↑ -->
                <div class="w-full overflow-auto">
                    <table width="100%">
                        <thead>
                            <th class="w-16 border-b p-2 border-gray-300">
                                &nbsp;
                            </th>
                            <th
                                v-for="col in data.forms[0].columns"
                                :key="col.id"
                                class="whitespace-nowrap text-left border-b p-1 border-gray-300"
                            >
                                <span v-if="signerMust(col)">*</span
                                >{{ col.name }}
                            </th>
                        </thead>
                        <tbody class="border-b border-gray-300">
                            <tr
                                v-for="(form, form_index) in data.forms"
                                :key="form.id"
                                class="bg-white even:bg-gray-100"
                            >
                                <td class="font-semibold p-2 whitespace-nowrap">
                                    {{ form_index + 1 }}.
                                </td>
                                <td
                                    class="p-2"
                                    v-for="col in form.columns"
                                    :key="col.id"
                                >
                                    <!-- 審核人填寫欄位 -->
                                    <div
                                        v-if="
                                            sign == 1 &&
                                            signOff == 1 &&
                                            'fill_column_ids' in
                                                data.sign_roles[
                                                    data.sign_roles.length - 1
                                                ] &&
                                            data.sign_roles[
                                                data.sign_roles.length - 1
                                            ].fill_column_ids.includes(
                                                col.id
                                            ) &&
                                            col.audit_must == true
                                        "
                                    >
                                        <InputText
                                            @input="
                                                col.value == ''
                                                    ? delete col.update
                                                    : $set(col, 'update', 1);
                                                $set(
                                                    data,
                                                    'invalidInput',
                                                    false
                                                );
                                            "
                                            class="w-24"
                                            :class="{
                                                'p-invalid': emptyInput(col),
                                            }"
                                            v-model="col.value"
                                            v-if="col.type == 'input'"
                                        />
                                        <InputNumber
                                            @input="
                                                col.value == ''
                                                    ? delete col.update
                                                    : $set(col, 'update', 1);
                                                $set(
                                                    data,
                                                    'invalidInput',
                                                    false
                                                );
                                            "
                                            inputClass="w-24"
                                            :class="{
                                                'p-invalid': emptyInput(col),
                                            }"
                                            :maxFractionDigits="4"
                                            v-model="col.value"
                                            v-else-if="
                                                col.type == 'money' ||
                                                col.type == 'number'
                                            "
                                        />
                                        <div v-else-if="col.type == 'dropdown'">
                                            <Dropdown
                                                v-if="
                                                    !('mul' in col) ||
                                                    col.mul !== 1
                                                "
                                                v-model="col.value"
                                                @input="
                                                    col.value == ''
                                                        ? delete col.update
                                                        : $set(
                                                              col,
                                                              'update',
                                                              1
                                                          );
                                                    $set(
                                                        data,
                                                        'invalidInput',
                                                        false
                                                    );
                                                "
                                                :options="col.options"
                                                optionLabel="name"
                                                placeholder="選項"
                                                appendTo="content"
                                                class="max-w-full w-48"
                                                :class="{
                                                    'p-invalid':
                                                        emptyInput(col),
                                                }"
                                                @change="openList(col)"
                                            />
                                            <MultiSelect
                                                v-else
                                                v-model="col.value"
                                                @input="
                                                    col.value == ''
                                                        ? delete col.update
                                                        : $set(
                                                              col,
                                                              'update',
                                                              1
                                                          );
                                                    $set(
                                                        data,
                                                        'invalidInput',
                                                        false
                                                    );
                                                "
                                                :options="col.options"
                                                optionLabel="name"
                                                optionValue="name"
                                                placeholder="選項"
                                                appendTo="content"
                                                class="max-w-full w-48"
                                                :class="{
                                                    'p-invalid':
                                                        emptyInput(col),
                                                }"
                                            />
                                        </div>
                                        <Calendar
                                            v-else-if="col.type == 'time'"
                                            v-model="col.value"
                                            @input="
                                                col.value == ''
                                                    ? delete col.update
                                                    : $set(col, 'update', 1);
                                                $set(
                                                    data,
                                                    'invalidInput',
                                                    false
                                                );
                                            "
                                            :timeOnly="true"
                                            hourFormat="12"
                                            :manualInput="false"
                                            placeholder="請輸入時間"
                                            appendTo="content"
                                            class="w-28"
                                            :class="{
                                                'p-invalid': emptyInput(col),
                                            }"
                                        />
                                        <Calendar
                                            v-else-if="col.type == 'date'"
                                            v-model="col.value"
                                            @input="
                                                col.value == ''
                                                    ? delete col.update
                                                    : $set(col, 'update', 1);
                                                $set(
                                                    data,
                                                    'invalidInput',
                                                    false
                                                );
                                            "
                                            :minDate="
                                                col.date.selected == 'future'
                                                    ? new Date()
                                                    : null
                                            "
                                            :maxDate="
                                                col.date.selected == 'pass'
                                                    ? new Date()
                                                    : null
                                            "
                                            dateFormat="yy-mm-dd"
                                            :manualInput="false"
                                            placeholder="請輸入日期"
                                            appendTo="content"
                                            class="w-32"
                                            :class="{
                                                'p-invalid': emptyInput(col),
                                            }"
                                        />
                                        <input
                                            @input="
                                                col.value == ''
                                                    ? delete col.update
                                                    : $set(col, 'update', 1);
                                                $set(
                                                    data,
                                                    'invalidInput',
                                                    false
                                                );
                                            "
                                            class="w-24 border"
                                            :class="{
                                                'p-invalid': emptyInput(col),
                                            }"
                                            v-model="col.value"
                                            v-else
                                            type="text"
                                        />
                                    </div>
                                    <!-- 以上審核人填寫欄位 -->
                                    <div
                                        v-else
                                        style="
                                            min-width: 5rem;
                                            max-width: 20rem;
                                            width: max-content;
                                        "
                                    >
                                        <p
                                            v-if="
                                                col.type !== 'list' &&
                                                col.type !== 'document' &&
                                                col.type !== 'customList' &&
                                                !('value' in col)
                                            "
                                        ></p>
                                        <p v-else-if="col.type == 'money'">
                                            {{ FormatValue(col.value) }}
                                            <span>元</span>
                                        </p>
                                        <p
                                            v-else-if="
                                                col.type == 'single' ||
                                                (col.type == 'dropdown' &&
                                                    typeof col.value ===
                                                        'object' &&
                                                    (!('mul' in col) ||
                                                        col.mul == 0) &&
                                                    col.value != undefined)
                                            "
                                        >
                                            {{
                                                col.value !== null
                                                    ? col.value.name
                                                    : null
                                            }}
                                            <span
                                                v-if="
                                                    col.value !== null &&
                                                    'prompt' in col.value &&
                                                    col.value.prompt !== null
                                                "
                                                class="text-red-500 block"
                                                >*{{ col.value.prompt }}</span
                                            >
                                            <span
                                                v-if="
                                                    col.value !== null &&
                                                    'option' in col.value &&
                                                    col.value.option != null
                                                "
                                                >: {{ col.value.option }}</span
                                            >
                                        </p>
                                        <p
                                            v-else-if="col.type == 'multi'"
                                            v-for="(mul, index) in col.value"
                                            :key="mul.id"
                                        >
                                            {{ mul.name }}
                                            <span v-if="'option' in mul"
                                                >: {{ mul.option }}</span
                                            >
                                            <span
                                                :class="
                                                    index ===
                                                    col.value.length - 1
                                                        ? 'hidden'
                                                        : ''
                                                "
                                                >、</span
                                            >
                                            <span v-if="!'value' in col">
                                            </span>
                                        </p>
                                        <p
                                            v-else-if="
                                                col.type == 'dropdown' &&
                                                col.mul == 1
                                            "
                                            v-for="(drop, index) in col.value"
                                            :key="index"
                                            class="inline-block"
                                        >
                                            {{ drop }}
                                            <span
                                                v-if="col.value.length > 1"
                                                :class="
                                                    index ===
                                                    col.value.length - 1
                                                        ? 'hidden'
                                                        : ''
                                                "
                                                >、</span
                                            >
                                            <span v-if="!'value' in col">
                                            </span>
                                        </p>
                                        <p v-else-if="col.type == 'employee'">
                                            {{
                                                col.value.org_name +
                                                " " +
                                                col.value.job_title +
                                                " " +
                                                col.value.name
                                            }}
                                        </p>
                                        <p
                                            class="w-28 lg:w-full"
                                            v-else-if="col.type == 'date'"
                                        >
                                            {{
                                                "value" in col &&
                                                col.value !== null
                                                    ? typeof col.value ==
                                                      "string"
                                                        ? col.value.slice(0, 13)
                                                        : formatDate(col.value)
                                                    : ""
                                            }}
                                        </p>
                                        <p
                                            class="w-28 lg:w-full"
                                            v-else-if="col.type == 'time'"
                                        >
                                            {{
                                                "value" in col &&
                                                col.value !== null
                                                    ? typeof col.value ==
                                                      "string"
                                                        ? col.value.slice(-5)
                                                        : formatTime(col.value)
                                                    : ""
                                            }}
                                        </p>
                                        <Button
                                            v-else-if="
                                                col.type == 'list' ||
                                                col.type == 'customList'
                                            "
                                            @click="
                                                menuView(form_index, col.id)
                                            "
                                            :label="
                                                col.type == 'list'
                                                    ? '項目清單'
                                                    : '自訂表單'
                                            "
                                            class="p-button-outlined p-button-secondary w-28"
                                        />
                                        <a
                                            v-else-if="
                                                col.type == 'document' &&
                                                col.files[0] !== null
                                            "
                                            @click.stop
                                            v-for="(
                                                item_file, index_file
                                            ) in col.files"
                                            :key="index_file"
                                            class="underline mr-2 inline-block"
                                            :href="
                                                '/api/demand/get/file' +
                                                (item_file.path ||
                                                    `/${data.no}/${item_file.base_name}/${item_file.name}`)
                                            "
                                        >
                                            {{ item_file.name }}
                                            <span
                                                :class="
                                                    index_file ===
                                                    col.files.length - 1
                                                        ? 'hidden'
                                                        : ''
                                                "
                                                >、</span
                                            >
                                        </a>
                                        <p class="" v-else>
                                            {{ col.value ?? "" }}
                                        </p>
                                    </div>
                                </td>
                            </tr>
                            <tr
                                v-if="
                                    data.forms[0].columns.some(
                                        (col) => col.type == 'money'
                                    ) ||
                                    data.forms[0].columns.some(
                                        (col) => col.displayTotalRow == true
                                    )
                                "
                                class="bg-white even:bg-gray-100"
                            >
                                <td class="p-2">小計</td>
                                <td
                                    v-for="(tCol, index) in totalCols"
                                    :key="index"
                                    class="p-2 font-semibold whitespace-nowrap"
                                >
                                    {{
                                        tCol == 0 ? "&nbsp;" : FormatValue(tCol)
                                    }}
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                <!-- <hr v-if="data.remarks !== null" class="w-full text-gray-200" /> -->
                <!-- remarks ↓ -->
                <div v-if="!sign" class="mt-4">
                    <div
                        v-for="(
                            remark, remark_index
                        ) in customRemarksWithoutReviewer"
                        :key="remark_index"
                        class="py-3 flex items-baseline"
                    >
                        <div class="w-5">
                            <p class="w-3 text-primary text-xl">•</p>
                        </div>
                        <template v-if="hasHtmlTag(remark.value)">
                            <p v-html="remark.value" class="leading-7"></p>
                        </template>
                        <template v-else>
                            <p class="leading-7">{{ remark.value }}</p>
                        </template>
                    </div>
                </div>
                <div v-if="sign" class="mt-4">
                    <div
                        v-for="(
                            remark, remark_index
                        ) in customRemarksWithoutApplicant"
                        :key="remark_index"
                        class="py-3 flex items-baseline"
                    >
                        <div class="w-5">
                            <p class="w-3 text-primary text-xl">•</p>
                        </div>
                        <template v-if="hasHtmlTag(remark.value)">
                            <p v-html="remark.value" class="leading-7"></p>
                        </template>
                        <template v-else>
                            <p class="leading-7">{{ remark.value }}</p>
                        </template>
                    </div>
                </div>
                <!-- remarks ↑ -->
            </div>
            <br />
            <!-- 項目清單 ↓ -->
            <template v-for="(form, form_index) in data.forms">
                <template v-for="(menu, menu_index) in form.columns">
                    <template
                        v-if="menu.type == 'list' || menu.type == 'customList'"
                    >
                        <div
                            :key="menu.id"
                            class="demandList mb-4 overflow-x-auto"
                        >
                            <h2
                                :id="'menu' + form_index + menu.id"
                                class="text-center text-2xl font-semibold mb-2"
                            >
                                {{ form_index + 1 + ". " + menu.name }}
                            </h2>
                            <table
                                v-if="menu.type == 'list'"
                                width="100%"
                                class="overflow-auto whitespace-nowrap"
                            >
                                <thead class="border-b border-gray-200">
                                    <tr>
                                        <th
                                            style="
                                                width: 150px;
                                                max-width: 336px;
                                            "
                                            class="text-left"
                                        >
                                            項目
                                        </th>
                                        <th
                                            style="
                                                width: 150px;
                                                max-width: 336px;
                                            "
                                            class="text-left"
                                        >
                                            數量
                                        </th>
                                        <th
                                            style="
                                                width: 150px;
                                                max-width: 336px;
                                            "
                                            class="text-left"
                                            v-if="'unit' in menu.list[0]"
                                        >
                                            單位
                                        </th>
                                        <th
                                            style="
                                                width: 150px;
                                                max-width: 336px;
                                            "
                                            class="text-left"
                                            v-if="'price' in menu.list[0]"
                                        >
                                            單價
                                        </th>
                                        <th
                                            style="
                                                width: 150px;
                                                max-width: 336px;
                                            "
                                            class="text-left"
                                            v-if="'total' in menu.list[0]"
                                        >
                                            <p>總價</p>
                                        </th>
                                        <th
                                            style="
                                                width: 150px;
                                                max-width: 336px;
                                            "
                                            class="text-left"
                                            v-if="'memo' in menu.list[0]"
                                        >
                                            備註
                                        </th>
                                    </tr>
                                </thead>
                                <tbody class="border-gray-200">
                                    <tr
                                        v-for="(list, list_index) in menu.list"
                                        :key="form_index + list_index"
                                        class="w-full border-b border-gray-200"
                                    >
                                        <td
                                            style="
                                                width: 150px;
                                                max-width: 336px;
                                            "
                                            class="py-3"
                                        >
                                            <p>{{ list.name }}</p>
                                        </td>
                                        <td
                                            style="
                                                width: 150px;
                                                max-width: 336px;
                                            "
                                            class="py-3"
                                        >
                                            <p>{{ list.count }}</p>
                                        </td>
                                        <td
                                            style="
                                                width: 150px;
                                                max-width: 336px;
                                            "
                                            class="py-3"
                                            v-if="'unit' in menu.list[0]"
                                        >
                                            <p>{{ list.unit }}</p>
                                        </td>
                                        <td
                                            style="
                                                width: 150px;
                                                max-width: 336px;
                                            "
                                            class="py-3"
                                            v-if="'price' in menu.list[0]"
                                        >
                                            <p>
                                                {{ FormatValue(list.price)
                                                }}<span v-show="list.price > 0"
                                                    >元</span
                                                >
                                            </p>
                                        </td>
                                        <td
                                            style="
                                                width: 150px;
                                                max-width: 336px;
                                            "
                                            class="p-2"
                                            v-if="'total' in menu.list[0]"
                                        >
                                            <p>{{ menuTotal(list) }}</p>
                                        </td>
                                        <td
                                            style="
                                                width: 150px;
                                                max-width: 336px;
                                            "
                                            class="py-3"
                                            v-if="'memo' in menu.list[0]"
                                        >
                                            <p>{{ list.memo }}</p>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td
                                            class="py-3"
                                            v-if="'name' in menu.list[0]"
                                        ></td>
                                        <td
                                            class="py-3"
                                            v-if="'count' in menu.list[0]"
                                        ></td>
                                        <td
                                            class="py-3"
                                            v-if="'unit' in menu.list[0]"
                                        ></td>
                                        <td
                                            class="py-3"
                                            v-if="'price' in menu.list[0]"
                                        ></td>
                                        <td v-if="'total' in menu.list[0]">
                                            <p>
                                                合計 ${{
                                                    finallyTotal(menu.list)
                                                }}
                                            </p>
                                        </td>
                                        <td
                                            class="py-3"
                                            v-if="'memo' in menu.list[0]"
                                        ></td>
                                    </tr>
                                </tbody>
                            </table>
                            <div v-else>
                                <CustomForm
                                    :action="2"
                                    :dem_custom_list="menu.custom_list"
                                    :dem_form_setting="menu.form_setting"
                                    :dem_can_insert="menu.can_insert"
                                    :signOff="signOff"
                                    :edit="editCustomForm"
                                    :signer="
                                        data.sign_roles[
                                            data.sign_roles.length - 1
                                        ]
                                    "
                                    :menu_index="menu_index"
                                    :mode="mode"
                                    @fill="$set(menu, 'update', 1)"
                                    ref="customForm"
                                />
                            </div>
                        </div>
                    </template>
                </template>
            </template>
            <!-- 項目清單 ↑ -->
            <!-- 意見區 ↓ -->
            <div
                style="max-width: 1624px"
                class="bg-white w-full shadow p-6"
                :class="sign !== 1 ? 'rounded-xl' : 'rounded-t-xl'"
            >
                <h2 class="mb-4 text-center text-2xl font-semibold">
                    {{ sign !== 1 ? "審核進度" : "簽核意見" }}
                </h2>
                <div
                    v-for="role in data.sign_roles"
                    :key="role.id"
                    class="px-3 py-6 bg-white even:bg-gray-100 flex gap-5 justify-between flex-wrap md:flex-nowrap"
                >
                    <!-- 部門 & 姓名 -->
                    <div class="flex gap-3 items-center justify-center order-1">
                        <div class="w-24">{{ role.self_name }}</div>
                        <div>{{ "l" }}</div>
                        <div class="whitespace-nowrap">{{ role.name }}</div>
                    </div>
                    <!-- 回覆 -->
                    <div
                        class="flex-grow my-auto order-3 md:order-2 w-full md:w-fit"
                    >
                        {{ role.remark }}
                    </div>
                    <!-- 上傳檔案 -->
                    <div
                        class="flex justify-end items-center order-4 md:order-3"
                        v-if="'document_info' in role"
                    >
                        <div
                            @click.stop
                            v-for="(
                                item_file, index_file
                            ) in role.document_info"
                            :key="index_file"
                            class="truncate inline-block underline"
                            style="max-width: 5rem"
                        >
                            <a
                                :href="'/api/demand/get/file' + item_file.path"
                                >{{ item_file.name }}</a
                            >
                            <span
                                :class="
                                    index_file === role.document_info.length - 1
                                        ? 'hidden'
                                        : ''
                                "
                                >、</span
                            >
                        </div>
                    </div>
                    <div
                        class="flex md:whitespace-nowrap gap-3 order-2 md:order-4"
                    >
                        <!-- 時間戳 -->
                        <span v-if="role.timestamp" class="my-auto">{{
                            role.timestamp
                        }}</span>
                        <!-- 意思表示 -->
                        <div
                            class="w-16 my-auto rounded-full py-1.5 border text-sm text-center"
                            :class="[changeState(role.apply_status, 0)]"
                        >
                            <span>{{ changeState(role.apply_status, 1) }}</span>
                        </div>
                    </div>
                </div>
            </div>
            <!-- 意見區 ↑ -->
        </div>
    </div>
</template>
<script>
import Button from "primevue/button";
import Calendar from "primevue/calendar";
import CustomForm from "@/demand/common/custom-form";
import Dialog from "primevue/dialog";
import Dropdown from "primevue/dropdown";
import InputNumber from "primevue/inputnumber";
import InputText from "primevue/inputtext";
import MultiSelect from "primevue/multiselect";

export default {
    components: {
        Button,
        Calendar,
        CustomForm,
        Dialog,
        Dropdown,
        InputNumber,
        InputText,
        MultiSelect,
    },
    props: [
        "data",
        "layer",
        "sign",
        "signOff",
        "checked",
        "type",
        "value",
        "editCustomForm",
        "mode",
    ],
    model: {
        prop: "value",
        event: "change",
    },
    data() {
        return {
            displayModal: false,
            stateDefault: "bg-gray-200 text-gray-400 border-gray-400",
            stateAgree: "bg-green-50 text-green-400 border-green-300",
            stateAgain: "bg-yellow-50 text-yellow-400 border-yellow-400",
            stateOver: "bg-red-50 text-red-400 border-red-400",
            stateCounter: "bg-blue-50 text-blue-400 border-blue-400",
            stateSign: "bg-indigo-50 text-indigo-400 border-indigo-400",
            tag: "",
            menus: [],
            printingExcel: false,
            printingPdf: false,
        };
    },
    mounted() {
        if (this.layer == "inner") {
            this.data.forms.forEach((form, form_index) =>
                form.columns.forEach((col) => {
                    if (col.type == "list" || col.type == "customList")
                        if (this.menus[form_index]) {
                            this.menus[form_index].push(col);
                        } else {
                            this.menus[form_index] = [col];
                        }
                })
            );
            this.$forceUpdate();
        }
        if (!this.data.applicant) {
            this.data.applicant = {
                name: this.data.employee?.name || this.data.createdBy,
                dep:
                    this.data.employee?.orgs?.[0]?.orgname ||
                    this.data.org_name,
                title: this.data.employee?.job_title || this.data.created_title,
            };
        }
    },
    methods: {
        FormatValue(value) {
            const formatter = new Intl.NumberFormat("en-US", {
                currency: "USD",
            });
            if (value == null || isNaN(value)) {
                return (value = 0);
            } else {
                return (value = formatter.format(value));
            }
        },
        formatDate(dateObject) {
            const dateTime = new Date(dateObject);
            const year = dateTime.getFullYear();
            const month = dateTime.getMonth() + 1;
            const date = dateTime.getDate();
            const day = (day) => {
                switch (day) {
                    case 1:
                        return "一";
                    case 2:
                        return "二";
                    case 3:
                        return "三";
                    case 4:
                        return "四";
                    case 5:
                        return "五";
                    case 6:
                        return "六";
                    case 0:
                        return "日";
                    default:
                        break;
                }
            };
            return (
                year +
                "/" +
                month +
                "/" +
                date +
                " (" +
                day(dateTime.getDay()) +
                ")"
            );
        },
        formatTime(dateObject) {
            const date = new Date(dateObject);
            let hours = date.getHours();
            let minutes = date.getMinutes();
            const ampm = hours >= 12 ? "PM" : "AM";
            hours = hours % 12;
            hours = hours ? hours : 12;
            hours = hours < 10 ? "0" + hours : hours;
            minutes = minutes < 10 ? "0" + minutes : minutes;

            return hours + ":" + minutes + " " + ampm;
        },
        menuView(form_index, id) {
            var element = document.getElementById("menu" + form_index + id);
            const yOffset = -100;
            const y =
                element.getBoundingClientRect().top +
                window.pageYOffset +
                yOffset;
            window.scrollTo({ top: y, behavior: "smooth" });
        },
        back() {
            this.$emit("back", 0);
            this.$emit("counterRoles", [0]);
            this.$emit("files", []);
            this.$emit("remark", "");
            this.$emit("invalid_remark", false);
        },
        getListsRow() {
            this.$emit("click");
        },
        changeState(state, type) {
            if (state !== null) {
                switch (state) {
                    case 0:
                        return type ? "未審核" : this.stateDefault;
                    case 1:
                        return type ? "審核中" : this.stateSign;
                    case 2:
                        return type ? "同意" : this.stateAgree;
                    case 3:
                        return type ? "駁回" : this.stateOver;
                    case 4:
                        return type ? "再審" : this.stateAgain;
                    case 5:
                        return type ? "會簽" : this.stateSign;
                    case 6:
                        return type ? "退回" : this.stateOver;
                    case 7:
                        return type ? "退回" : this.stateOver;
                    default:
                        return type ? "未知" : "";
                }
            }
        },
        changeTypeColor(type) {
            if (type !== null) {
                switch (type) {
                    case "未結案":
                        this.tag = type;
                        return this.stateSign;
                    case "已結案":
                        this.tag = type;
                        return this.stateAgree;
                }
            }
        },
        openList(item) {
            // this.menuForm = col.list;
            // this.displayModal = true;
            this.$emit("openList", item);
        },
        menuTotal(list) {
            list.total = (list.count * 10000 * list.price) / 10000 || 0;
            return list.total;
        },
        finallyTotal(list) {
            let list_totals = list.map((lis) => lis.total);
            return list_totals.reduce((a, m) => {
                return a + m || 0;
            });
        },
        getId(event) {
            let newValue = [...this.value];
            if (!event.target.checked) {
                newValue.splice(newValue.indexOf(this.data.id), 1);
            } else {
                newValue.push(this.data.id);
            }
            if (newValue <= 0) {
                this.$parent.visibleBottom = false;
            } else {
                this.$parent.visibleBottom = true;
            }
            this.$emit("change", newValue);
        },
        pdfBtn(type) {
            if (type == "excel") {
                this.printingExcel = true;
            } else if (type == "word") {
                this.printingPdf = true;
            }

            axios({
                url: "/api/demand/word",
                method: "GET",
                headers: {
                    "Content-Type":
                        "application/x-www-form-urlencoded;charset=utf-8",
                },

                params: {
                    id: this.data.id,
                    type: type,
                    finished:
                        this.data.demand_type == 1
                            ? false
                            : this.signOff == 2
                            ? true
                            : false,
                },
                responseType: "blob",
            })
                .then((response) => {
                    if (
                        response.headers["content-type"].includes(
                            "application/pdf"
                        )
                    ) {
                        var blob = new Blob([response.data], {
                            type: "application/pdf",
                        });
                        if (
                            window.navigator &&
                            window.navigator.msSaveOrOpenBlob
                        ) {
                            window.navigator.msSaveOrOpenBlob(blob);
                            return;
                        }
                        const data = window.URL.createObjectURL(blob);
                        var link = document.createElement("a");
                        link.href = data;
                        link.download = "resume.pdf";
                        link.click();
                        setTimeout(function () {
                            window.URL.revokeObjectURL(data);
                        }, 100);
                    } else if (
                        response.headers["content-type"].includes(
                            "application/octet-stream"
                        )
                    ) {
                        var blob = new Blob([response.data], {
                            type: "application/vnd.ms-excel;charset=utf-8",
                        });

                        var filename = decodeURIComponent(
                            response.headers["content-disposition"].split(
                                "filename*=utf-8''"
                            )[1]
                        );
                        var url = window.URL.createObjectURL(blob);
                        var aLink = document.createElement("a");
                        aLink.style.display = "none";
                        aLink.href = url;
                        aLink.setAttribute("download", filename);
                        document.body.appendChild(aLink);
                        aLink.click();
                        setTimeout(function () {
                            window.URL.revokeObjectURL(url);
                        }, 100);
                    } else {
                        window.print();
                    }
                })
                .catch((error) => {
                    console.error(error);
                })
                .finally(() => {
                    if (type == "excel") {
                        this.printingExcel = false;
                    } else if (type == "word") {
                        this.printingPdf = false;
                    }
                });
        },
        totalCompute() {
            this.data.forms.forEach((form, index) => {
                form.columns.forEach((col) => {
                    if (col.type == "total") {
                        let col1 = this.data.forms[index].columns.find(
                            (c) => c.id == col.cols.col1.id
                        );
                        let col2 = this.data.forms[index].columns.find(
                            (c) => c.id == col.cols.col2.id
                        );
                        const col1Value = parseFloat(col1.value);
                        const col2Value = parseFloat(col2.value);
                        let computedValue;
                        switch (col.compute) {
                            case "+":
                                computedValue = col1Value + col2Value;
                                break;
                            case "-":
                                computedValue = col1Value - col2Value;
                                break;
                            case "*":
                                computedValue = col1Value * col2Value;
                                break;
                            case "/":
                                computedValue = col1Value / col2Value;
                                break;
                        }
                        this.$set(
                            col,
                            "value",
                            col.parseInt
                                ? computedValue.toFixed(0)
                                : computedValue.toFixed(2)
                        );
                        this.$set(col, "update", 1);
                    }
                });
            });
        },
        signerMust(col) {
            let signer = this.data.sign_roles[this.data.sign_roles.length - 1];
            let dollar_cols = this.data.forms[0].columns.filter((form) => {
                if ("fill_column_ids" in signer) {
                    return signer?.fill_column_ids.includes(form.id);
                }
            });
            let display_must = false;
            if (
                this.sign == 1 &&
                dollar_cols.length !== 0 &&
                signer.must &&
                signer.apply_status == 1
            ) {
                dollar_cols.forEach((d_c) => {
                    if (d_c.audit_must && d_c.must && col.id == d_c.id) {
                        display_must = true;
                    }
                });
                return display_must;
            }
        },
        emptyInput(col) {
            return (
                this.data.invalidInput &&
                (col.value === null ||
                    col.value === "" ||
                    col.value?.length == 0 ||
                    !("value" in col)) &&
                col.must
            );
        },
        updateCL(data) {
            this.menus[0].custom_list[data.seq[0]][data.seq[1]] = data.value;
        },
        hasHtmlTag(str) {
            // 只檢查是否包含有效的<a>標籤
            // 排除純文字中的 < > 符號
            const regex = /<\/?[a-zA-Z][a-zA-Z0-9]*(\s[^>]*)?>/;
            return regex.test(str);
        },
    },
    watch: {
        "data.forms": {
            handler: function (val, oldVal) {
                if (this.layer == "inner") {
                    this.totalCompute();
                }
            },
            deep: true,
        },
    },
    computed: {
        isChecked() {
            if (this.layer == "outside") {
                return this.value.indexOf(this.data.id) !== -1;
            }
        },
        is_counterSign() {
            if (
                this.data.sign_roles[this.data.sign_roles.length - 1]
                    .apply_status == 5 ||
                this.data.sign_roles[this.data.sign_roles.length - 1]
                    .to_counter == 1 ||
                this.data.sign_roles[this.data.sign_roles.length - 1]
                    .to_counter == 2
            ) {
                return true;
            } else {
                return false;
            }
        },
        is_againSign() {
            if (this.data.sign_roles.some((role) => role.apply_status == 4)) {
                return true;
            } else {
                return false;
            }
        },
        totalCols() {
            const moneyForms = this.data.forms.map((form) => {
                return form.columns.map((col) => {
                    if (col.type == "money" || col.displayTotalRow == true) {
                        return Number(col.value) || 0;
                    } else {
                        return null;
                    }
                });
            });
            const sum = moneyForms[0].map((number, index) => {
                return moneyForms.reduce((acc, array) => {
                    return acc + array[index];
                }, 0);
            });
            return sum;
        },
        customRemarksWithoutReviewer() {
            if (this.data.remarks) {
                return this.data.remarks
                    .map((i) => {
                        if (i instanceof Object) {
                            return i;
                        } else {
                            return { value: i, recipient: "所有人" };
                        }
                    })
                    .filter((i) => i.recipient !== "審核人");
            }
            return "";
        },
        customRemarksWithoutApplicant() {
            if (this.data.remarks) {
                return this.data.remarks
                    .map((i) => {
                        if (i instanceof Object) {
                            return i;
                        } else {
                            return { value: i, recipient: "所有人" };
                        }
                    })
                    .filter((i) => i.recipient !== "申請人");
            }
            return "";
        },
    },
};
</script>

<style>
.nowrap-button .p-button-label {
    white-space: nowrap;
}
</style>
