<template>
  <Dialog
    :baseZIndex="9999"
    :visible.sync="computedConfirmVisible"
    :containerStyle="{ maxWidth: '90%', width: '384px', maxHeight: '90%' }"
    :modal="true"
    dismissableMask
    :closable="false"
  >
    <div class="flex flex-col justify-center items-center">
      <img v-if="option.imgPath" :src="option.imgPath" :alt="option.type" />
      <div
        v-if="option.message"
        class="font-bold text-lg mt-8 text-explorerPrimary"
      >
        {{ option.message }}
      </div>
    </div>
    <template #footer>
      <div
        class="flex items-center mt-20 px-6 pb-2"
        :class="option.centerBtn.text ? 'justify-center' : 'justify-between'"
      >
        <button
          v-if="option.leftBtn.text"
          style="border: 1px solid #1d2939"
          class="bg-white hover:bg-gray-200 px-6 py-2 mb-8 text-explorerPrimary border-gray-800 rounded-lg font-bold text-base whitespace-nowrap"
          @click="option.leftBtn.command"
        >
          <span>{{ option.leftBtn.text }}</span>
        </button>
        <button
          v-if="option.centerBtn.text"
          class="bg-primary hover:bg-indigo-900 px-6 py-2 mb-8 text-white rounded-lg font-bold text-base whitespace-nowrap"
          @click="option.centerBtn.command"
        >
          <span>{{ option.centerBtn.text }}</span>
        </button>
        <button
          v-if="option.rightBtn.text"
          class="bg-primary hover:bg-indigo-900 px-6 py-2 mb-8 text-white rounded-lg font-bold text-base whitespace-nowrap"
          @click="option.rightBtn.command"
        >
          <span>{{ option.rightBtn.text }}</span>
        </button>
      </div>
    </template>
  </Dialog>
</template>

<script>
import Dialog from "primevue/dialog";

export default {
  name: "Confirm",
  components: {
    Dialog,
  },
  props: {
    option: { type: Object, required: true },
  },
  computed: {
    computedConfirmVisible: {
      get() {
        return this.option.isShow;
      },
      set(value) {
        this.$emit("onToggle", value);
      },
    },
  },
};
</script>

<style scoped>
.p-dialog .p-dialog-footer button {
  margin-right: 0 !important;
}
</style>
