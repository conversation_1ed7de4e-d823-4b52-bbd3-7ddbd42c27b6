<template>
  <!-- 建檔規則 -->
  <Dialog
    :visible.sync="computedSettingsVisible"
    :modal="true"
    :containerStyle="{ maxWidth: '90%', width: '800px', maxHeight: '90%' }"
    dismissableMask
    class="file-settings"
  >
    <template #header>
      <div class="flex flex-col md:flex-row items-start p-2">
        <h1
          class="text-3xl font-bold mr-6 text-start text-explorerPrimary whitespace-nowrap"
        >
          建檔規則
        </h1>
        <button
          @click="saveRules(rules)"
          style="color: '#1D2939'"
          class="text-base text-explorerPrimary mt-4 md:mt-0 px-6 py-3 font-bold bg-white rounded-lg border border-gray-800 hover:bg-gray-200 transition"
        >
          儲存規則
        </button>

        <div class="md:ml-6">
          <button
            @click="useRule(rules, rules[activeRule])"
            class="text-base text-white px-6 py-3 mt-4 md:mt-0 font-bold bg-primary rounded-lg hover:bg-indigo-900 transition relative"
          >
            套用此規則
          </button>

          <div
            v-if="currentRule"
            class="text-explorerPrimary text-xs mt-1 flex item-center"
          >
            <img src="@images/icon/check.svg" alt="check-icon" />
            <span class="ml-0.5">已套用{{ currentRule?.title }}</span>
          </div>
          <div v-else style="color: #98a2b3" class="text-xs mt-1">
            未套用規則
          </div>
        </div>
      </div>
    </template>

    <div class="relative">
      <!-- TabBar -->
      <ul
        class="tab-bar flex items-center mt-2 mb-2 border-b border-gray-200 w-auto overflow-y-hidden overflow-x-auto sticky top-0 bg-white z-50"
      >
        <li
          v-for="(rule, index) in rules"
          :key="rule.id"
          class="h-9 mr-4 px-2 cursor-pointer tab transition"
          :class="activeRule === index && 'tab-active'"
          @click="activeRule = index"
        >
          <div class="flex items-center justify-center">
            <span
              class="pr-2 text-explorerPrimary text-xs whitespace-nowrap"
              v-show="!rule.isEditTitle"
              >{{ rule.title }}</span
            >
            <input
              ref="ruleTitleInput"
              type="text"
              v-model="rule.title"
              v-show="rule.isEditTitle"
              class="w-16 text-explorerPrimary text-xs rename text-center border outline-none focus:border-blue-500"
              @input="ruleState = STATE_CHANGED"
            />
            <button
              class="hover:bg-gray-200 rounded-full w-6 h-6 transition ml-1"
            >
              <img
                v-show="!rule.isEditTitle"
                src="@images/icon/edit.svg"
                alt="edit"
                class="w-4 mx-auto"
                @click="editRuleTitle(index)"
              />
              <img
                v-show="rule.isEditTitle"
                src="@images/icon/save.svg"
                alt="save"
                class="w-4 mx-auto"
                @click="saveRuleTitle(index)"
              />
            </button>
          </div>
        </li>
        <li class="h-9">
          <button
            @click="addFileSettings"
            class="h-6 transition flex items-center p-2 rounded-lg hover:bg-gray-200"
          >
            <img src="@images/icon/add.svg" alt="add" class="w-4 mr-2" />
            <span class="text-explorerPrimary text-xs whitespace-nowrap"
              >新增建檔規則</span
            >
          </button>
        </li>
      </ul>

      <div
        class="relative mt-10 whitespace-nowrap"
        v-show="activeRule === index"
        v-for="(rule, index) in rules"
        :key="rule.id"
      >
        <button
          class="flex absolute items-center right-0 px-2 py-1 rounded-lg hover:bg-gray-200 transition"
          @click="deleteRule(rule, index, $event)"
        >
          <img src="@images/icon/delete.svg" alt="delete" />
          <span class="ml-2 text-explorerPrimary">刪除此規則</span>
        </button>
        <div class="flex flex-col mb-6">
          <h3 class="mb-4 font-bold text-explorerPrimary text-lg">
            STEP.1 設定存放位置
          </h3>
          <div
            style="border-left: #98a2b3 2px solid"
            class="px-4 pb-4"
            v-for="(folder, index) in rule.folder"
            :key="index"
          >
            <div class="flex items-center">
              <h3
                class="folder relative mr-52 ml-4 font-bold w-28 text-explorerPrimary"
              >
                第{{ toChineseNumber(index + 1) }}層資料夾
              </h3>
              <button
                v-show="rule.folder.length > 1"
                @click="deleteFolderOrFileRule(rule.folder, index, $event)"
                class="hover:bg-gray-200 rounded-full w-8 h-8 transition"
              >
                {{ folder.length }}
                <img
                  src="@images/icon/delete.svg"
                  alt="delete"
                  class="mx-auto"
                />
              </button>
            </div>
            <div class="flex flex-col">
              <div class="mt-4 flex items-center">
                <RadioButton
                  :id="'directFolder' + [index + 1]"
                  name="folder-name"
                  :value="0"
                  v-model="folder.type"
                  @input="ruleState = STATE_CHANGED"
                />
                <label
                  :for="'directDir' + [index + 1]"
                  class="ml-2 text-explorerPrimary"
                  >直接命名</label
                >
              </div>
              <div
                class="ml-8 mt-2 text-explorerPrimary"
                v-show="folder.type === 0"
              >
                <InputText
                  type="text"
                  placeholder="請輸入文字"
                  class="mr-4"
                  :class="
                    ruleState === STATE_FAILED &&
                    folder.value.trim() === '' &&
                    'p-invalid border-red-500 placeholder-red-500'
                  "
                  v-model="folder.value"
                  @input="ruleState = STATE_CHANGED"
                />
                <span>為資料夾名稱</span>
              </div>
            </div>
            <div class="flex flex-col">
              <div class="mt-4 flex items-center">
                <RadioButton
                  :id="'scanFolder' + [index + 1]"
                  name="folder-name"
                  :value="1"
                  v-model="folder.type"
                  @input="ruleState = STATE_CHANGED"
                />
                <label
                  :for="'scanFolder' + [index + 1]"
                  class="ml-2 text-explorerPrimary"
                  >掃描命名</label
                >
              </div>
              <div class="mt-2 text-explorerPrimary" v-show="folder.type === 1">
                <span>取</span
                ><InputText
                  type="text"
                  placeholder="請輸入文字"
                  class="mx-4"
                  :class="
                    ruleState === STATE_FAILED &&
                    folder.value.trim() === '' &&
                    'p-invalid border-red-500 placeholder-red-500'
                  "
                  v-model="folder.value"
                  @input="ruleState = STATE_CHANGED"
                /><span>為資料夾名稱</span>
              </div>
            </div>
          </div>

          <div>
            <button
              class="mt-4 px-2 py-1 text-start rounded-lg hover:bg-gray-200 transition text-explorerPrimary"
              @click="addFolderOrFileRule(index, 'folder')"
            >
              <i class="pi pi-plus mr-2"></i>
              <span>新增資料夾</span>
            </button>
          </div>
        </div>

        <div class="flex flex-col mt-12">
          <h3 class="mb-4 font-bold text-explorerPrimary text-lg">
            STEP.2 設定檔名規則
          </h3>

          <div
            style="border-left: #98a2b3 2px solid"
            class="px-4 pb-8"
            v-for="(file, index) in rule.file"
            :key="index"
          >
            <div class="flex items-center">
              <h3
                class="folder relative mr-52 ml-4 font-bold w-28 text-explorerPrimary"
              >
                第{{ toChineseNumber(index + 1) }}段檔名
              </h3>
              <button
                v-show="rule.file.length > 1"
                @click="deleteFolderOrFileRule(rule.file, index, $event)"
                class="hover:bg-gray-200 rounded-full w-8 h-8 transition"
              >
                <img
                  src="@images/icon/delete.svg"
                  alt="delete"
                  class="mx-auto"
                />
              </button>
            </div>
            <div class="mt-4 flex items-center text-explorerPrimary">
              <RadioButton
                :id="'directFile' + [index + 1]"
                name="file-name"
                :value="0"
                v-model="file.type"
                @input="ruleState = STATE_CHANGED"
              />
              <label :for="'directFile' + [index + 1]" class="ml-2"
                >直接命名</label
              >
            </div>
            <div
              class="ml-8 mt-2 text-explorerPrimary"
              v-show="file.type === 0"
            >
              <InputText
                type="text"
                placeholder="請輸入文字"
                class="mr-4"
                :class="
                  ruleState === STATE_FAILED &&
                  file.value.trim() === '' &&
                  'p-invalid border-red-500 placeholder-red-500'
                "
                v-model="file.value"
                @input="ruleState = STATE_CHANGED"
              />
              <span>為檔名</span>
            </div>
            <div class="flex flex-col text-explorerPrimary">
              <div class="mt-4 flex items-center">
                <RadioButton
                  :id="'scanFile' + [index + 1]"
                  name="file-name"
                  :value="1"
                  v-model="file.type"
                  @input="ruleState = STATE_CHANGED"
                />
                <label :for="'scanFile' + [index + 1]" class="ml-2"
                  >掃描命名</label
                >
              </div>
              <div class="mt-2 text-explorerPrimary" v-show="file.type === 1">
                <span>取</span
                ><InputText
                  type="text"
                  placeholder="請輸入文字"
                  class="mx-4"
                  :class="
                    ruleState === 'failed' &&
                    file.value.trim() === '' &&
                    'p-invalid border-red-500 placeholder-red-500'
                  "
                  v-model="file.value"
                  @input="ruleState = STATE_CHANGED"
                /><span>為檔名</span>
              </div>
              <div
                class="flex items-center mt-6 text-explorerPrimary"
                v-if="rule.file.length > 1 && index !== rule.file.length - 1"
              >
                <Checkbox
                  v-model="file.hasDash"
                  :binary="true"
                  @input="ruleState = STATE_CHANGED"
                />
                <label :for="file.hasDash" class="ml-2">添加間隔 -</label>
              </div>
            </div>
          </div>
          <div>
            <button
              class="mt-4 px-2 py-1 text-start rounded-lg hover:bg-gray-200 transition text-explorerPrimary"
              @click="addFolderOrFileRule(index, 'file')"
            >
              <i class="pi pi-plus mr-2"></i>
              <span class="">新增檔名</span>
            </button>
          </div>
        </div>
      </div>
    </div>
    <template #footer>
      <div></div>
    </template>
  </Dialog>
</template>

<script>
import { nanoid } from "nanoid";
import Dialog from "primevue/dialog";
import RadioButton from "primevue/radiobutton";
import InputText from "primevue/inputtext";
import Checkbox from "primevue/checkbox";
import {
  getFileRules,
  updateRules,
  useRule,
  removeRule,
} from "@/axios/explorerApi.js";

export default {
  name: "RuleSettings",
  components: {
    Dialog,
    RadioButton,
    InputText,
    Checkbox,
  },
  props: {
    isShow: { type: Boolean, required: true },
    currentRule: { required: true },
    confirmOption: { type: Object, required: true },
  },
  data() {
    return {
      rules: [],
      activeRule: 0,
      ruleState: "initial",
      STATE_INITIAL: "initial",
      STATE_CHANGED: "changed",
      STATE_SAVING: "saving",
      STATE_USING: "using",
      STATE_FAILED: "failed",
      STATE_SUCCEED: "succeed",
    };
  },
  methods: {
    async fetchRules() {
      try {
        const data = await getFileRules();
        if (!data.length) {
          this.initRules();
        } else {
          this.rules = data;
          this.rules.forEach((rule) => {
            this.$set(rule, "isEditTitle", false);
            this.$set(rule, "isNewRule", false);
          });
        }

        let index = this.rules.findIndex(
          (rule) => rule.id === this.currentRule?.id
        );
        index = index !== -1 ? index : 0;
        this.activeRule = index;
      } catch (error) {
        this.$refs.toast.add({
          severity: "error",
          summary: error.message,
          life: 3000,
        });
      }
    },
    async saveRules(newRules) {
      if (this.ruleState === this.STATE_SAVING || !newRules.length) return;

      try {
        this.ruleState = this.STATE_SAVING;
        if (this.isInputEmpty) {
          this.ruleState = this.STATE_FAILED;
          throw new Error("尚有欄位未填寫，無法儲存");
        } else {
          // 刪除 newRules 中不需要給後端的屬性
          newRules = newRules.map((rule) => {
            const { isEditTitle, isNewRule, ...rest } = rule;
            return rest;
          });
          await updateRules({ rules: newRules });
          this.$refs.toast.add({
            severity: "success",
            summary: "儲存成功",
            life: 3000,
          });
          this.ruleState = this.STATE_SUCCEED;
          this.rules.forEach((rule) => {
            rule.isNewRule = false;
          });
        }
      } catch (error) {
        this.$refs.toast.add({
          severity: "error",
          summary: error.message,
          life: 3000,
        });
        this.ruleState = this.STATE_FAILED;
      }
    },
    async useRule(rules, rule) {
      if (this.ruleState === this.STATE_USING) return;
      try {
        if (this.isInputEmpty) {
          this.ruleState = this.STATE_FAILED;
          throw new Error("尚有欄位未填寫，無法套用");
        } else {
          // 檢查規則是否有修改，若有則自動儲存
          if (this.ruleState === this.STATE_CHANGED) {
            await this.saveRules(rules);
            if (this.ruleState === this.STATE_FAILED) return;
          }
          this.ruleState = this.STATE_USING;
          const ruleId = rule.id;
          await useRule(ruleId);
          this.ruleState = this.STATE_SUCCEED;
          this.$refs.toast.add({
            severity: "success",
            summary: "套用建檔規則成功",
            life: 3000,
          });
          this.$emit("updateCurrentRule", {
            id: rule.id,
            title: rule.title,
          });
        }
      } catch (error) {
        this.ruleState = this.STATE_FAILED;
        this.$refs.toast.add({
          severity: "error",
          summary: error.message,
          life: 3000,
        });
      }
    },
    deleteRule(rule, index, event) {
      this.$refs.confirmPopup.visible = true;
      this.$refs.confirmPopup.target = event.currentTarget;
      this.$refs.confirmPopup.confirmation = {
        message: "是否確定刪除？",
        acceptLabel: "確定",
        rejectLabel: "取消",
        acceptClass: "font-bold",
        rejectClass:
          "border border-explorerPrimary text-explorerPrimary font-bold bg-white transition",
        accept: async () => {
          const { id: ruleId, isNewRule } = rule;
          try {
            // 若要刪除的規則已儲存，再 call api
            if (!isNewRule) {
              await removeRule(ruleId);
            }
            // 刪除規則
            const newRules = this.rules.filter((rule) => rule.id !== ruleId);
            if (!newRules.length) {
              this.initRules();
            } else {
              this.rules = newRules;
              if (index === 0) {
                this.activeRule = 0;
              } else {
                this.activeRule = index - 1;
              }
            }
            // 若刪除的規則是當前套用的規則，更新當前規則為 null
            if (this.currentRule && this.currentRule.id === ruleId) {
              this.$emit("updateCurrentRule", null);
            }

            this.rules.forEach((rule, index) => (rule.sort = index + 1));
            this.$refs.toast.add({
              severity: "success",
              summary: "刪除成功",
              life: 3000,
            });
          } catch (error) {
            this.$refs.toast.add({
              severity: "error",
              summary: `刪除失敗，${error.message}`,
              life: 3000,
            });
          }
        },
      };
    },
    initRules() {
      this.rules = [{ ...this.newRule, sort: 1, title: "建檔規則 1" }];
      this.$emit("updateCurrentRule", null);
    },
    editRuleTitle(index) {
      this.ruleState = this.STATE_CHANGED;
      this.rules[index].isEditTitle = true;
      this.$nextTick(() => {
        this.$refs.ruleTitleInput.forEach((input) => {
          input.focus();
          input.select();
        });
      });
    },
    saveRuleTitle(index) {
      if (!this.rules[index].title.trim()) {
        this.$refs.toast.add({
          severity: "error",
          summary: "規則名不可為空",
          life: 3000,
        });
      } else {
        this.rules[index].isEditTitle = false;
      }
    },
    addFolderOrFileRule(index, type) {
      this.ruleState = this.STATE_CHANGED;
      this.rules[index][type].push({
        sort: this.rules[index][type]?.length + 1,
        type: 0,
        value: "",
      });
      // file 多一個 hasDash 屬性
      if (type === "file") {
        this.$set(this.rules[index][type]?.at(-1), "hasDash", false);
      }
    },
    deleteFolderOrFileRule(item, index, event) {
      this.ruleState = this.STATE_CHANGED;
      this.$refs.confirmPopup.visible = true;
      this.$refs.confirmPopup.target = event.currentTarget;
      this.$refs.confirmPopup.confirmation = {
        message: "是否確定刪除？",
        acceptLabel: "確定",
        rejectLabel: "取消",
        acceptClass: "font-bold",
        rejectClass:
          "border border-explorerPrimary text-explorerPrimary font-bold bg-white transition",
        accept: () => {
          item.length > 1 && item.splice(index, 1);
          item.forEach((item, index) => (item.sort = index + 1));
          this.$refs.toast.add({
            severity: "success",
            summary: "刪除成功",
            life: 3000,
          });
        },
      };
    },
    addFileSettings() {
      this.rules.push(this.newRule);
      this.activeRule = this.rules.length - 1;
      this.ruleState = this.STATE_CHANGED;
    },
    checkRuleSaved(rules) {
      this.$emit("setConfirm", {
        type: "warning",
        message: "尚未儲存規則，是否儲存規則？",
        imgPath: new URL("@images/popup_state/warning.png", import.meta.url).href,
        leftBtn: {
          text: "關閉視窗",
          command: () => {
            this.confirmOption.isShow = false;
            this.$emit("toggleRuleSettings", false);
            this.ruleState = this.STATE_INITIAL;
          },
        },
        rightBtn: {
          text: "儲存規則",
          command: async () => {
            try {
              await this.saveRules(rules);
              this.confirmOption.isShow = false;
              if (this.ruleState === this.STATE_FAILED) return;
              this.$emit("toggleRuleSettings", false);
            } catch (error) {
              this.$refs.toast.add({
                severity: "error",
                summary: error.message,
                life: 3000,
              });
            }
          },
        },
        isShow: true,
      });
    },
    toChineseNumber(num) {
      // 將阿拉伯數字轉為中文
      const singles = [
        "零",
        "一",
        "二",
        "三",
        "四",
        "五",
        "六",
        "七",
        "八",
        "九",
      ];

      const tens = [
        "",
        "十",
        "二十",
        "三十",
        "四十",
        "五十",
        "六十",
        "七十",
        "八十",
        "九十",
      ];
      if (num < 10) {
        return singles[num];
      } else if (num < 20) {
        return num === 10 ? "十" : "十" + singles[num % 10];
      } else if (num < 100) {
        return (
          tens[Math.floor(num / 10)] + (num % 10 === 0 ? "" : singles[num % 10])
        );
      } else {
        return num;
      }
    },
  },
  computed: {
    computedSettingsVisible: {
      get() {
        return this.isShow;
      },
      set(value) {
        // 關閉 dialog 前檢查規則是否有儲存
        if (
          this.ruleState !== this.STATE_INITIAL &&
          this.ruleState !== this.STATE_SUCCEED
        ) {
          this.checkRuleSaved(this.rules);
        } else {
          this.$emit("toggleRuleSettings", value);
        }
      },
    },
    // 檢查是否有規則 input 為空
    isInputEmpty() {
      return this.rules.some(
        (rule) =>
          rule.folder.some((item) => !item.value.trim()) ||
          rule.file.some((item) => !item.value.trim())
      );
    },
    newRule() {
      return {
        id: nanoid(),
        sort: this.rules.at(-1)?.sort + 1 || 1,
        title: "建檔規則 " + (this.rules.length + 1),
        isEditTitle: false,
        isNewRule: true,
        folder: [
          {
            sort: 1,
            type: 0,
            value: "",
          },
        ],
        file: [
          {
            sort: 1,
            type: 0,
            value: "",
            hasDash: false,
          },
        ],
      };
    },
  },
  watch: {
    isShow: function () {
      if (this.isShow) {
        this.fetchRules();
      }
    },
  },
  mounted() {
    // 離開網頁前若有修改規則就跳出提示訊息
    window.addEventListener("beforeunload", (event) => {
      if (
        this.ruleState !== this.STATE_INITIAL &&
        this.ruleState !== this.STATE_SUCCEED
      ) {
        this.checkRuleSaved(this.rules);
        event.preventDefault();
        return false;
      } else {
        this.$emit("toggleRuleSettings", false);
      }
    });
  },
};
</script>

<style scoped>
.folder::before {
  content: "";
  display: inline-block;
  position: absolute;
  width: 24px;
  height: 2px;
  background: #98a2b3;
  top: 10px;
  left: -32px;
}
.tab-bar::-webkit-scrollbar {
  width: 5px !important;
  height: 5px !important;
  background-color: #fff !important;
}
.tab-bar::-webkit-scrollbar-track {
  border-radius: 10px !important;
}
.tab-bar::-webkit-scrollbar-thumb {
  border-radius: 4px !important;
  background-color: #e5e7eb !important;
}
.tab-bar::-webkit-scrollbar-thumb:hover {
  border-radius: 4px !important;
  background-color: #ccc !important;
}
.tab:hover {
  border-bottom: 3px solid #4f46e5;
}
.tab-active {
  border-bottom: 3px solid #4f46e5;
}
.rename::selection {
  background-color: #dcedfd;
  color: #1d2939;
}
</style>
