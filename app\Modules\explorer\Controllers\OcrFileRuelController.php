<?php

namespace App\Modules\explorer\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use App\Modules\explorer\Models\OcrFileRule;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Session;
use GuzzleHttp\Client;

class OcrFileRuelController extends Controller
{
    protected $company_id, $user_id, $file_url, $client;
    public function __construct()
    {
        $this->company_id = Session::get('CompanyId');
        $this->user_id = Session::get('employee_id');
        $this->file_url = config("app.services_file_api_url");
        $this->client = new Client();
    }

    public function fetchRules(Request $request)
    {
        try {
            $filRule = OcrFileRule::query()
                ->user()
                ->firstOrFail();
        } catch (ModelNotFoundException $ex) {
            // 尚未建立規則
            return response()->json([], 200);
        }

        $rules = $filRule->rules->map(function ($item) {
            return [
                'id' => $item['id'],
                'sort' => $item['sort'],
                'title' => $item['title'],
                'folder' => $item['folder'],
                'file' => $item['file'],
            ];
        });

        return response()->json($rules, 200);
    }

    public function updateRule(Request $request)
    {
        $rules = $request->input('rules');

        $validator = $this->validateUpdateRule($rules);
        if ($validator->fails())
            return response()->json(["message" => $validator->errors()->first()], 400);

        OcrFileRule::query()
            ->updateOrCreate(
                ['employee_id' => $this->user_id],
                [
                    'rules' => $rules,
                    'payload' => []
                ]
            );

        return response(200);
    }
    public function validateUpdateRule($form)
    {
        $rules = [
            '*.id' => 'required|distinct',
            '*.title' => 'required|string',
            '*.folder' => 'required|array',
            '*.file' => 'required|array',

            '*.folder.*.sort' => 'required|numeric',
            '*.folder.*.type' => 'required|boolean',
            '*.folder.*.value' => 'required|string',

            '*.file.*.sort' => 'required|numeric',
            '*.file.*.type' => 'required|boolean',
            '*.file.*.value' => 'required|string',
            '*.file.*.hasDash' => 'required|boolean',
        ];

        $msg = [
            '*.*.required' => '缺少必填項目!',
            '*.*.distinct' => '必填項目錯誤!',
            '*.*.string' => '必填項目錯誤!',
            '*.*.array' => '必填項目錯誤!',

            '*.*.*.*.required' => '缺少必填項目!',
            '*.*.*.*.numeric' => '必填項目錯誤!',
            '*.*.*.*.string' => '必填項目錯誤!',
            '*.*.*.*.boolean' => '必填項目錯誤!',
        ];
        $validator = \Validator::make($form, $rules, $msg); // $msg

        return $validator;
    }

    public function deleteRule($id)
    {
        try {
            $filRule = OcrFileRule::user()->firstOrFail();
        } catch (ModelNotFoundException $ex) {
            return response()->json(["message" => "規則已不存在!"], 400);
        }
        $rules = collect($filRule->rules);
        // array 的index
        $index = $rules->search(function ($item, $key) use ($id) {
            return $item['id'] == $id;
        });

        if ($index === false)
            return response()->json(["message" => "規則已不存在!"], 400);

        // 移除index 的element
        $rules->splice($index, 1);
        $filRule->setAttribute('rules', $rules)
            ->save();

        return response(200);
    }


    public function changeRule($id)
    {
        try {
            $filRule = OcrFileRule::user()->firstOrFail();
        } catch (ModelNotFoundException $ex) {
            return response()->json(["message" => "規則已不存在!"], 400);
        }
        $index = collect($filRule->rules)->search(function ($item, $key) use ($id) {
            return $item['id'] == $id;
        });

        if ($index === false)
            return response()->json(["message" => "規則已不存在!"], 400);


        $filRule->forceFill(['enable_id' => $id])
            ->save();

        return response(200);
    }
}
