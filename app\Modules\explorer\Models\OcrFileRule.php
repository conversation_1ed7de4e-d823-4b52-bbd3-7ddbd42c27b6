<?php

namespace App\Modules\explorer\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\Session;

class OcrFileRule extends Model
{
    use \Staudenmeir\EloquentJsonRelations\HasJsonRelationships;
    use SoftDeletes;
    protected $fillable = ['employee_id', 'enable_id', 'rules',  'payload'];
    protected $casts = ['payload' => 'collection', 'rules' => 'collection'];


    public function scopeUser($query)
    {
        return $query->where('employee_id', Session::get('employee_id'));
    }


}
