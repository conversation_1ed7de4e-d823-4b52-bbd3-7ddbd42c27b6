<template>
  <div
    class="flex flex-col lg:flex-row justify-between items-start mb-6 bg-white p-6 rounded-lg"
  >
    <form
      class="relative h-12 w-full md:w-96"
      @submit.prevent="
        $emit('onSearch', keyword);
        keyword = '';
      "
    >
      <input
        type="text"
        placeholder="搜尋檔案"
        style="border-color: #667085"
        class="w-full h-full px-3 py-2 border rounded-lg"
        v-model="keyword"
      />
      <button
        type="submit"
        class="absolute top-3 right-3 -translate-y-1/2 rounded-full hover:bg-gray-200 transition"
      >
        <!-- <i class="pi pi-search" /> -->
        <img src="@images/icon/search.svg" alt="search" class="w-6 p-1" />
      </button>
    </form>
    <!-- 按鈕（須有設定資料夾權限才會出現) -->
    <div
      v-if="$root.createPermission"
      class="flex flex-wrap mt-0 md:mt-6 lg:mt-0"
    >
      <button
        :disabled="!currentFolderData?.admin"
        class="mt-6 md:mt-0 border rounded-lg mr-6 h-12 px-6 py-2 transition"
        :class="currentFolderData?.admin ? 'tool-btn' : 'cursor-not-allowed'"
        :style="
          !currentFolderData?.admin && {
            borderColor: '#D0D5DD',
            color: '#D0D5DD',
          }
        "
        @click="$emit('onAdd')"
      >
        <div class="flex justify-center items-center mt-0">
          <img
            v-if="!currentFolderData?.admin"
            src="@images/icon/add_disable.svg"
            alt="setting"
            class="w-6 p-1"
          />
          <img
            v-else
            src="@images/icon/add.svg"
            alt="setting"
            class="w-6 p-1"
          />
          <span class="ml-2 font-bold">新增資料夾</span>
        </div>
      </button>
      <div>
        <button
          class="tool-btn mt-6 md:mt-0 border rounded-lg mr-6 h-12 px-6 py-2 transition"
          @click="$emit('toggleRuleSettings', true)"
        >
          <div class="flex justify-center items-center mt-0">
            <img src="@images/icon/settings.svg" alt="setting" class="w-6" />
            <span class="ml-2 font-bold">建檔規則</span>
          </div>
        </button>
        <div
          v-if="currentRule"
          class="text-explorerPrimary text-xs mt-1 flex item-center"
        >
          <img src="@images/icon/check.svg" alt="check-icon" />
          <span class="ml-0.5">已套用{{ currentRule?.title }}</span>
        </div>
        <div v-else style="color: #98a2b3" class="text-xs mt-1">未套用規則</div>
      </div>
      <button
        :disabled="!currentRule"
        class="mt-6 md:mt-0 border rounded-lg mr-6 w-36 h-12 font-bold transition"
        :class="currentRule && 'tool-btn'"
        :style="!currentRule && { borderColor: '#D0D5DD', color: '#D0D5DD' }"
      >
        <input
          v-if="currentRule"
          type="file"
          id="fileInput"
          class="hidden"
          multiple
          ref="fileInput"
          size="10000000"
          accept=".doc,.docx,.xls,.xlsx,.pdf"
          @change="handleUploadFile"
        />
        <label
          for="fileInput"
          class="w-full h-full flex justify-center items-center"
          :class="!currentRule ? 'cursor-not-allowed' : 'cursor-pointer'"
        >
          <img
            v-if="currentRule"
            src="@images/icon/upload.svg"
            alt="upload"
            class="w-6 p-0.5"
          />
          <img
            v-else
            src="@images/icon/upload_disable.svg"
            alt="upload"
            class="w-6 p-0.5"
          />
          <span class="ml-2">上傳檔案</span>
        </label>
      </button>
    </div>
  </div>
</template>

<script>
import { nanoid } from "nanoid";
import { uploadNotify, uploadFile } from "@/axios/explorerApi.js";

export default {
  name: "Toolbar",
  props: {
    currentRule: { required: true },
    currentFolderData: { required: true },
  },
  data() {
    return {
      keyword: "",
    };
  },
  methods: {
    async handleUploadFile(event) {
      if (!this.currentRule) return;
      const fileList = this.$refs.fileInput.files;
      const key = Date.now() + "_" + nanoid();
      const fileNames = Array.from(fileList).map((file) => file.name);

      try {
        this.$emit("updateIsLoading", true);
        await uploadNotify({ key, fileNames });
        await Promise.all(
          Array.from(fileList).map((file) => {
            const form = new FormData();
            form.append("file", file);
            form.append("fileName", file.name);
            form.append("key", key);
            return uploadFile(form).catch((error) =>
              Promise.reject(new Error(file.name + error.message))
            );
          })
        );

        this.$refs.toast.add({
          severity: "success",
          summary: "上傳成功，系統需處理建檔，完成後於小鈴鐺通知",
          life: 5000,
        });
      } catch (error) {
        this.$refs.toast.add({
          severity: "error",
          summary: error.message || "發生異常，上傳失敗，請再次上傳",
          life: 3000,
        });
      } finally {
        // 解決不能連續上傳同名檔案問題
        event.target.value = "";
        this.$emit("updateIsLoading", false);
      }
    },
  },
};
</script>

<style scoped>
.tool-btn {
  border-color: #1d2939;
  color: #1d2939;
}

.tool-btn:hover {
  background-color: #e4e7ec;
}
</style>
