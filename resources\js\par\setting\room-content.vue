<template>
    <div>
        <div class="text-center relative mb-10">
            <span
                @click="($parent.action = 0), ($parent.id = 0)"
                class="cursor-pointer absolute left-0 top-2"
            >
                <i class="fas fa-arrow-left"></i>
            </span>
            <p class="text-2xl font-semibold">
                {{ this.isAdd ? "新增設施" : "編輯設施" }}
            </p>
        </div>
        <div class="demandList">
            <div class="pb-8 border-b border-gray-200">
                <div class="block md:flex">
                    <p class="my-auto mr-16 whitespace-nowrap">
                        設施圖片<span class="text-red-500">*</span>
                    </p>
                    <div>
                        <div class="flex flex-wrap mb-2">
                            <div
                                v-for="(image, index) of form.images"
                                :key="index"
                                class="text-right"
                            >
                                <img
                                    class="w-52 h-40 mr-4"
                                    :class="
                                        index == form.images.length - 1
                                            ? 'mr-0'
                                            : ''
                                    "
                                    :src="form.images[index].file_show"
                                    width="200"
                                />
                                <i
                                    @click="form.images.splice(index, 1)"
                                    class="fa-solid fa-trash-can mr-4 cursor-pointer"
                                ></i>
                            </div>
                            <div v-if="form.images.length < 5">
                                <div
                                    class="border-2 rounded-md w-52 h-40 mr-4 text-center"
                                    :class="
                                        formInvalid.images
                                            ? 'border-solid border-red-300'
                                            : 'border-dashed border-gray-200'
                                    "
                                >
                                    <i
                                        style="line-height: 10rem"
                                        class="fa-solid fa-image fa-2xl text-gray-200"
                                    ></i>
                                </div>
                                <small
                                    v-if="formInvalid.images"
                                    class="text-red-500 block"
                                    >最少上傳1張圖片</small
                                >
                            </div>
                            <div
                                v-for="(n, index) in 4 - form.images.length < 0
                                    ? 0
                                    : 4 - form.images.length"
                                :key="'n' + index"
                                class="border-2 border-dashed rounded-md border-gray-200 w-52 h-40 mr-4 text-center"
                                :class="index == 4 ? 'mr-0' : ''"
                            >
                                <i
                                    style="line-height: 10rem"
                                    class="fa-solid fa-image fa-2xl text-gray-200"
                                ></i>
                            </div>
                        </div>
                        <Button
                            @click="$refs.fileInput.click()"
                            label="上傳圖片"
                            class="p-button-outlined p-button-secondary w-28"
                        />
                        <input
                            id="files"
                            type="file"
                            class="hidden"
                            ref="fileInput"
                            multiple
                            accept=".jpg,.jpeg,.heif,.hevc,.png,.HEIF,.HEVC"
                            @change="uploadFile($event.file)"
                        />
                    </div>
                </div>
            </div>
            <div class="py-8 border-b border-gray-200">
                <div class="block md:flex">
                    <p class="my-auto mr-16">
                        設施名稱<span class="text-red-500">*</span>
                    </p>
                    <div>
                        <InputText
                            class="w-72"
                            :class="{ 'p-invalid': formInvalid.name }"
                            v-model="form.name"
                            placeholder="輸入設施名稱"
                            @input="formInvalid.name = false"
                        />
                        <small
                            v-if="formInvalid.name"
                            class="text-red-500 block"
                            >請輸入設施名稱</small
                        >
                    </div>
                </div>
            </div>
            <div class="py-8 border-b border-gray-200">
                <div class="block md:flex">
                    <p class="my-auto mr-16">
                        設施人數<span class="text-red-500">*</span>
                    </p>
                    <div>
                        <InputNumber
                            v-model="form.people"
                            inputClass="w-60"
                            :class="{ 'p-invalid': formInvalid.people }"
                            mode="decimal"
                            incrementButtonClass="bg-white text-black border-gray-300 border-l-0 border-b-0"
                            decrementButtonClass="bg-white text-black border-gray-300 border-l-0 border-t-0"
                            showButtons
                            placeholder="輸入設施人數"
                            @input="formInvalid.people = false"
                            :min="0"
                        />
                        <small
                            v-if="formInvalid.type"
                            class="text-red-500 block"
                            >請輸入設施人數</small
                        >
                    </div>
                </div>
            </div>
            <div class="py-8 border-b border-gray-200">
                <div class="block md:flex">
                    <p class="my-auto mr-16">
                        設施地點<span class="text-red-500">*</span>
                    </p>
                    <div>
                        <InputText
                            class="w-72"
                            :class="{ 'p-invalid': formInvalid.place }"
                            v-model="form.place"
                            placeholder="輸入設施地點"
                            @input="formInvalid.place = false"
                        />
                        <small
                            v-if="formInvalid.place"
                            class="text-red-500 block"
                            >請輸入設施地點</small
                        >
                    </div>
                </div>
            </div>

            <!-- <div class="py-8 border-b border-gray-200">
        <div class="block md:flex">
          <p class="my-auto mr-16">
            所屬部門<span class="text-red-500">*</span>
          </p>
          <div  class="w-60">
            <Dropdown
              v-model="form.org_unit_id"
              :class="{ 'p-invalid': formInvalid.org_unit_id }"
              placeholder="選擇部門"
              :options="orgs"
              optionLabel="name"
              optionValue="id"
              @input="formInvalid.org_unit_id = false"
            />
            <small v-if="formInvalid.org_unit_id" class="text-red-500 block"
              >請選擇部門</small
            >
          </div>
        </div>
      </div> -->
            <div class="py-8 border-b border-gray-200">
                <div class="block md:flex">
                    <p class="my-auto mr-16">
                        設施說明<span class="text-red-500">*</span>
                    </p>
                    <div>
                        <Textarea
                            class="w-72"
                            v-model="form.remark"
                            :class="{ 'p-invalid': formInvalid.remark }"
                            rows="5"
                            cols="30"
                            placeholder="輸入設施說明"
                            @input="formInvalid.remark = false"
                        />
                        <small
                            v-if="formInvalid.remark"
                            class="text-red-500 block"
                            >請輸入設施說明</small
                        >
                    </div>
                </div>
            </div>
            <div class="py-8 w-96">
                <div class="block md:flex justify-between">
                    <p class="my-auto mr-16">設備支援</p>
                    <div>
                        <Button
                            @click="form.apparatus_type.push('')"
                            label="新增類別"
                            class="p-button-outlined p-button-secondary w-28"
                        />
                    </div>
                </div>
            </div>
            <div>
                <div
                    v-for="(app, app_index) in form.apparatus_type"
                    :key="app_index"
                    class="block md:flex my-2"
                >
                    <p class="my-auto mr-16">類別名稱</p>
                    <div>
                        <AutoComplete
                            v-model="form.apparatus_type[app_index]"
                            placeholder="輸入設施類別名稱"
                            :suggestions="filtered_apps"
                            @complete="autoComplete($event)"
                            @item-select="
                                form.apparatus_type[app_index] =
                                    form.apparatus_type[app_index].type
                            "
                            field="type"
                            value="id"
                        />
                    </div>
                    <img
                        class="w-6 h-6 cursor-pointer my-auto"
                        src="@images/icon/delete_enable.png"
                        @click="form.apparatus_type.splice(app_index, 1)"
                    />
                </div>
            </div>
            <div class="py-8">
                <div class="block md:flex">
                    <p class="my-auto mr-8">同步通知人員</p>
                    <div class="w-60">
                        <MultiSelect
                            class="w-72"
                            v-model="form.notified"
                            :filter="true"
                            placeholder="選擇人員"
                            :options="getEmployees"
                            optionLabel="name"
                            optionValue="id"
                        />
                    </div>
                </div>
            </div>
            <div class="w-full flex justify-between md:justify-end">
                <Button
                    @click="$parent.action = 0"
                    label="取消"
                    class="p-button-outlined p-button-secondary w-28 mr-5"
                />
                <Button
                    :disabled="$root.loading"
                    v-if="isAdd"
                    @click="save"
                    label="新增"
                    class="w-28"
                />
                <Button
                    :disabled="$root.loading"
                    v-else
                    @click="save"
                    label="儲存"
                    class="w-28"
                />
            </div>
        </div>
        <Toast ref="toast" position="top-center" />
        <div v-if="$root.loading">
            <div
                class="fixed top-1/2 left-1/2 transform -translate-y-1/2 -translate-x-1/2 z-50"
            >
                <ProgressSpinner />
            </div>
        </div>
    </div>
</template>
<script>
import axios from "axios";
import AutoComplete from "primevue/autocomplete";
import Button from "primevue/button";
import Dropdown from "primevue/dropdown";
import InputText from "primevue/inputtext";
import InputNumber from "primevue/inputnumber";
import Textarea from "primevue/textarea";
import Toast from "primevue/toast";
import MultiSelect from "primevue/multiselect";
import ProgressSpinner from "primevue/progressspinner";

export default {
    components: {
        AutoComplete,
        Button,
        Dropdown,
        InputText,
        InputNumber,
        Textarea,
        Toast,
        MultiSelect,
        ProgressSpinner,
    },
    data() {
        return {
            form: {
                id: 0,
                name: "",
                people: null,
                place: "",
                apparatus_type: [],
                notified: [],
                remark: "",
                images: [],
                upload: [],
            },
            formInvalid: {
                name: false,
                people: false,
                place: false,
                // apparatus_type: false,
                // notified: false,
                remark: false,
                images: false,
            },
            apps: [],
            filtered_apps: [],
            name: "xxx",
            orgs: null,
            isAdd: true,
            id: 0,
            getEmployees: [],
        };
    },
    watch: {
        // "form.images": function (newValue) {
        //   if (newValue.length !== 0) {
        //     this.formInvalid.images = false;
        //   } else {
        //     this.formInvalid.images = true;
        //   }
        // },
    },
    mounted() {
        this.getOrgs();
        this.fetchDetail();
        this.getEmployeesDropDown();
    },
    methods: {
        fetchDetail() {
            if (this.$parent.id == 0) return;
            this.id = this.$parent.id;

            axios
                .get(this.$parent.url + "/detail", {
                    params: {
                        id: this.id,
                    },
                })
                .then((response) => {
                    this.isAdd = false;
                    this.form = response.data;
                    this.form.id = this.id;
                })
                .catch((error) => {
                    console.error(error);
                });
        },
        validate() {
            let error = false;
            this.formInvalid.name = this.form.name === "";
            this.formInvalid.type = this.form.type === "";
            this.formInvalid.no = this.form.no === "";
            this.formInvalid.org_unit_id = this.form.org_unit_id === "";
            this.formInvalid.remark = this.form.remark === "";
            this.formInvalid.amount = this.form.amount == null;
            this.formInvalid.images = this.form.images.length == 0;
            Object.values(this.formInvalid).forEach((item) => {
                if (item == true) {
                    error = true;
                }
            });

            return error;
        },
        autoComplete(event) {
            setTimeout(() => {
                if (!event.query.trim().length) {
                    this.filtered_apps = [...this.apps];
                } else {
                    if (this.apps.length == 0) {
                        axios
                            .get("/api/par/apparatus-type")
                            .then((response) => {
                                this.apps = response.data;
                                this.filtered_apps = this.apps.filter((app) => {
                                    return app.type
                                        .toLowerCase()
                                        .includes(event.query.toLowerCase());
                                });
                            })
                            .catch((error) => {
                                console.error(error);
                            });
                    } else {
                        this.filtered_apps = this.apps.filter((app) => {
                            return app.type
                                .toLowerCase()
                                .includes(event.query.toLowerCase());
                        });
                    }
                }
            }, 250);
        },
        create() {
            //   if (this.validate() == true) {
            //     console.log("123");
            //     return;
            //   }
            //   console.log("456");
        },
        imageLoaded(e) {
            this.form.upload.push(e.target.result);
        },
        uploadFile(e) {
            let files = document.getElementById("files").files;

            let formData = new FormData();
            for (var i = 0; i < files.length; i++) {
                let file = files[i];
                formData.append("upload[" + i + "]", file);
            }
            const options = {
                method: "POST",
                headers: { "content-type": "multipart/form-data" },
                data: formData,
                url: "/api/par/image?type=postulate",
            };
            axios(options)
                .then((response) => {
                    //   if (response !== "") {
                    //       this.$refs.toast.add({
                    //         severity: "success",
                    //         summary: "上傳成功",
                    //         life: 3000,
                    //       });
                    //   } else {
                    //     this.$refs.toast.add({
                    //       severity: "error",
                    //       summary: "上傳失敗",
                    //       life: 3000,
                    //     });
                    //   }
                    let list = response.data;
                    list.forEach((x) => {
                        this.form.images.push(x);
                    });
                })
                .catch((error) => {
                    console.error(error);
                    this.$refs.toast.add({
                        severity: "error",
                        summary: "上傳失敗!",
                        life: 3000,
                    });
                });
        },
        save() {
            const options = {
                method: "POST",
                data: this.form,
                url: this.isAdd
                    ? this.$parent.url
                    : this.$parent.url + "/update",
            };
            this.$root.loading = true;
            axios(options)
                .then((response) => {
                    if (response.data.state) {
                        this.$refs.toast.add({
                            severity: "success",
                            summary: "成功",
                            life: 3000,
                        });
                        this.$parent.id = 0;
                        this.$parent.action = 0;
                    } else
                        this.$refs.toast.add({
                            severity: "error",
                            summary: response.data.error,
                            life: 3000,
                        });
                })
                .catch((error) => {
                    console.error(error);
                })
                .finally(() => {
                    this.$root.loading = false;
                });
        },
        addApparatus() {},
        getOrgs() {
            axios
                .get("/api/par/org-units", {})
                .then((response) => {
                    this.orgs = response.data;
                })
                .catch((error) => {
                    console.error(error);
                });
        },
        getEmployeesDropDown() {
            axios
                .get("/api/demand/employees")
                .then((response) => {
                    this.getEmployees = response.data ?? [];
                })
                .catch((error) => {
                    console.error(error);
                });
        },
    },
};
</script>
