<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateCodesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('codes', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->string('code_kind');
            $table->string('code_parent');
            $table->string('code_id');
            $table->unsignedBigInteger('sort_order');
            $table->string('nm_zh_tw')->nullable();
            $table->string('nm_zh_cn')->nullable();
            $table->string('nm_en_us')->nullable();
            $table->timestamps();
        });
        DB::table('codes')->insert([
            ['code_kind' => 'AA', 'code_parent' => '', 'code_id' => 'W0', 'sort_order' => '0', 'nm_zh_tw' => '日', 'nm_zh_cn' => '', 'nm_en_us' => 'Sunday'],
            ['code_kind' => 'AA', 'code_parent' => '', 'code_id' => 'W1', 'sort_order' => '1', 'nm_zh_tw' => '一', 'nm_zh_cn' => '', 'nm_en_us' => 'Monday'],
            ['code_kind' => 'AA', 'code_parent' => '', 'code_id' => 'W2', 'sort_order' => '2', 'nm_zh_tw' => '二', 'nm_zh_cn' => '', 'nm_en_us' => 'Tuesday'],
            ['code_kind' => 'AA', 'code_parent' => '', 'code_id' => 'W3', 'sort_order' => '3', 'nm_zh_tw' => '三', 'nm_zh_cn' => '', 'nm_en_us' => 'Wednesday'],
            ['code_kind' => 'AA', 'code_parent' => '', 'code_id' => 'W4', 'sort_order' => '4', 'nm_zh_tw' => '四', 'nm_zh_cn' => '', 'nm_en_us' => 'Thursday'],
            ['code_kind' => 'AA', 'code_parent' => '', 'code_id' => 'W5', 'sort_order' => '5', 'nm_zh_tw' => '五', 'nm_zh_cn' => '', 'nm_en_us' => 'Friday'],
            ['code_kind' => 'AA', 'code_parent' => '', 'code_id' => 'W6', 'sort_order' => '6', 'nm_zh_tw' => '六', 'nm_zh_cn' => '', 'nm_en_us' => 'Saturday'],

            ['code_kind' => 'AB', 'code_parent' => '', 'code_id' => 'money', 'sort_order' => '0', 'nm_zh_tw' => '輸入題(金額)', 'nm_zh_cn' => '', 'nm_en_us' => ''],
            ['code_kind' => 'AB', 'code_parent' => '', 'code_id' => 'input', 'sort_order' => '1', 'nm_zh_tw' => '輸入題(文數字)', 'nm_zh_cn' => '', 'nm_en_us' => ''],
            ['code_kind' => 'AB', 'code_parent' => '', 'code_id' => 'dropdown', 'sort_order' => '2', 'nm_zh_tw' => '下拉選項題', 'nm_zh_cn' => '', 'nm_en_us' => ''],
            ['code_kind' => 'AB', 'code_parent' => '', 'code_id' => 'single', 'sort_order' => '3', 'nm_zh_tw' => '單選題', 'nm_zh_cn' => '', 'nm_en_us' => ''],
            ['code_kind' => 'AB', 'code_parent' => '', 'code_id' => 'multi', 'sort_order' => '4', 'nm_zh_tw' => '複選題', 'nm_zh_cn' => '', 'nm_en_us' => ''],
            ['code_kind' => 'AB', 'code_parent' => '', 'code_id' => 'list', 'sort_order' => '5', 'nm_zh_tw' => '項目清單', 'nm_zh_cn' => '', 'nm_en_us' => ''],
            ['code_kind' => 'AB', 'code_parent' => '', 'code_id' => 'time', 'sort_order' => '6', 'nm_zh_tw' => '時間選擇', 'nm_zh_cn' => '', 'nm_en_us' => ''],
            ['code_kind' => 'AB', 'code_parent' => '', 'code_id' => 'date', 'sort_order' => '7', 'nm_zh_tw' => '日期選擇', 'nm_zh_cn' => '', 'nm_en_us' => ''],
            ['code_kind' => 'AB', 'code_parent' => '', 'code_id' => 'database', 'sort_order' => '8', 'nm_zh_tw' => '資料庫欄位', 'nm_zh_cn' => '', 'nm_en_us' => ''],
            ['code_kind' => 'AB', 'code_parent' => '', 'code_id' => 'document', 'sort_order' => '9', 'nm_zh_tw' => '附件', 'nm_zh_cn' => '', 'nm_en_us' => ''],

            ['code_kind' => 'AC', 'code_parent' => '', 'code_id' => '0', 'sort_order' => '0', 'nm_zh_tw' => '未審核', 'nm_zh_cn' => '', 'nm_en_us' => ''],
            ['code_kind' => 'AC', 'code_parent' => '', 'code_id' => '1', 'sort_order' => '1', 'nm_zh_tw' => '審核中', 'nm_zh_cn' => '', 'nm_en_us' => ''],
            ['code_kind' => 'AC', 'code_parent' => '', 'code_id' => '2', 'sort_order' => '2', 'nm_zh_tw' => '同意', 'nm_zh_cn' => '', 'nm_en_us' => ''],
            ['code_kind' => 'AC', 'code_parent' => '', 'code_id' => '3', 'sort_order' => '3', 'nm_zh_tw' => '駁回', 'nm_zh_cn' => '', 'nm_en_us' => ''],
            ['code_kind' => 'AC', 'code_parent' => '', 'code_id' => '4', 'sort_order' => '4', 'nm_zh_tw' => '再審中', 'nm_zh_cn' => '', 'nm_en_us' => ''],
            ['code_kind' => 'AC', 'code_parent' => '', 'code_id' => '5', 'sort_order' => '5', 'nm_zh_tw' => '會簽', 'nm_zh_cn' => '', 'nm_en_us' => ''],

            ['code_kind' => 'AD', 'code_parent' => '', 'code_id' => 'all', 'sort_order' => '0', 'nm_zh_tw' => '不分', 'nm_zh_cn' => '', 'nm_en_us' => ''],
            ['code_kind' => 'AD', 'code_parent' => '', 'code_id' => 'open', 'sort_order' => '1', 'nm_zh_tw' => '未結案', 'nm_zh_cn' => '', 'nm_en_us' => ''],
            ['code_kind' => 'AD', 'code_parent' => '', 'code_id' => 'closed', 'sort_order' => '2', 'nm_zh_tw' => '已結案', 'nm_zh_cn' => '', 'nm_en_us' => ''],

            ['code_kind' => 'AE', 'code_parent' => '', 'code_id' => 'column', 'sort_order' => '0', 'nm_zh_tw' => '欄位', 'nm_zh_cn' => '', 'nm_en_us' => ''],
            ['code_kind' => 'AE', 'code_parent' => '', 'code_id' => 'export', 'sort_order' => '1', 'nm_zh_tw' => '匯出', 'nm_zh_cn' => '', 'nm_en_us' => ''],

            ['code_kind' => 'AF', 'code_parent' => '', 'code_id' => 'name', 'sort_order' => '0', 'nm_zh_tw' => '項目', 'nm_zh_cn' => '', 'nm_en_us' => ''],
            ['code_kind' => 'AF', 'code_parent' => '', 'code_id' => 'count', 'sort_order' => '1', 'nm_zh_tw' => '數量', 'nm_zh_cn' => '', 'nm_en_us' => ''],
            ['code_kind' => 'AF', 'code_parent' => '', 'code_id' => 'unit', 'sort_order' => '2', 'nm_zh_tw' => '單位', 'nm_zh_cn' => '', 'nm_en_us' => ''],
            ['code_kind' => 'AF', 'code_parent' => '', 'code_id' => 'price', 'sort_order' => '3', 'nm_zh_tw' => '單價', 'nm_zh_cn' => '', 'nm_en_us' => ''],
            ['code_kind' => 'AF', 'code_parent' => '', 'code_id' => 'total', 'sort_order' => '4', 'nm_zh_tw' => '總價', 'nm_zh_cn' => '', 'nm_en_us' => ''],
            ['code_kind' => 'AF', 'code_parent' => '', 'code_id' => 'memo', 'sort_order' => '5', 'nm_zh_tw' => '備註', 'nm_zh_cn' => '', 'nm_en_us' => ''],

            ['code_kind' => 'AG', 'code_parent' => '', 'code_id' => 'err1', 'sort_order' => '0', 'nm_zh_tw' => '請重新匯入', 'nm_zh_cn' => '', 'nm_en_us' => ''],
            ['code_kind' => 'AG', 'code_parent' => '', 'code_id' => 'err2', 'sort_order' => '1', 'nm_zh_tw' => '檔案格式不正確', 'nm_zh_cn' => '', 'nm_en_us' => ''],
            ['code_kind' => 'AG', 'code_parent' => '', 'code_id' => 'err3', 'sort_order' => '2', 'nm_zh_tw' => '找不到項目清單名稱', 'nm_zh_cn' => '', 'nm_en_us' => ''],
            ['code_kind' => 'AG', 'code_parent' => '', 'code_id' => 'err4', 'sort_order' => '3', 'nm_zh_tw' => '找不到資料庫名稱', 'nm_zh_cn' => '', 'nm_en_us' => ''],

            ['code_kind' => 'AH', 'code_parent' => '', 'code_id' => 'notify1', 'sort_order' => '0', 'nm_zh_tw' => '您目前有name待審核，請立即前往審核', 'nm_zh_cn' => '', 'nm_en_us' => ''],
            ['code_kind' => 'AH', 'code_parent' => '', 'code_id' => 'notify2', 'sort_order' => '1', 'nm_zh_tw' => '您提出的name，已完成審核，請立即前往查看', 'nm_zh_cn' => '', 'nm_en_us' => ''],
            ['code_kind' => 'AH', 'code_parent' => '', 'code_id' => 'notify3', 'sort_order' => '2', 'nm_zh_tw' => '您提出的name，已被駁回，請立即前往查看', 'nm_zh_cn' => '', 'nm_en_us' => ''],

            ['code_kind' => 'AI', 'code_parent' => '', 'code_id' => 'signed', 'sort_order' => '0', 'nm_zh_tw' => '已審核', 'nm_zh_cn' => '', 'nm_en_us' => ''],
            ['code_kind' => 'AI', 'code_parent' => '', 'code_id' => 'counter', 'sort_order' => '1', 'nm_zh_tw' => '已會簽', 'nm_zh_cn' => '', 'nm_en_us' => ''],
            ['code_kind' => 'AI', 'code_parent' => '', 'code_id' => 'self', 'sort_order' => '2', 'nm_zh_tw' => '已指定', 'nm_zh_cn' => '', 'nm_en_us' => ''],

            ['code_kind' => 'AJ', 'code_parent' => '', 'code_id' => 'rank', 'sort_order' => '0', 'nm_zh_tw' => '職等', 'nm_zh_cn' => '', 'nm_en_us' => ''],
            ['code_kind' => 'AJ', 'code_parent' => '', 'code_id' => 'manager', 'sort_order' => '1', 'nm_zh_tw' => '主管', 'nm_zh_cn' => '', 'nm_en_us' => ''],

        ]);
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('codes');
    }
}
