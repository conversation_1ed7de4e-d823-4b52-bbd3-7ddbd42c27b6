<?php

namespace Tests\Feature;

use DemandSeeder;
use EmployeeSeeder;
use Illuminate\Foundation\Testing\DatabaseMigrations;
use Illuminate\Support\Facades\DB;
use Tests\TestCase;

class DemandCacheTest extends TestCase
{
    use DatabaseMigrations;

    protected $tmpCacheData = '[[{"id":1,"name":"11111","type":"money","value":11,"preset":1,"selected":{"id":0,"type":"money","fname":"輸入題(金額)"}},{"id":2,"name":"1111","type":"input","selected":{"id":1,"type":"input","fname":"輸入題(文數字)"}}]]';
    
    protected function setUp(): void
    {
        parent::setUp();

        $this->seed([
            DemandSeeder::class,
            EmployeeSeeder::class,
        ]);
    }

    protected function forceLogin(int $employeeId)
    {
        $this->get("/demand?id=$employeeId");
    }

    public function testGetDemandCache()
    {
        $this->forceLogin(583);
        $layoutId = 426;
        $response = $this->get("/api/demand/submit/cache/$layoutId");

        $response->assertStatus(200)->dump();
    }

    public function testSaveDemandCache()
    {
        $this->forceLogin(583);
        $layoutId = 426;
        $response = $this->put("/api/demand/submit/cache/$layoutId", [
            'data' => $this->tmpCacheData,
        ])->dump();

        $response->assertStatus(204);
        $this->testGetDemandCache();
    }

    public function testDeleteDemandCache()
    {
        $this->forceLogin(583);
        $layoutId = 426;
        $response = $this->delete("/api/demand/submit/cache/$layoutId")->dump();

        $response->assertStatus(204);
        $this->testSaveDemandCache();
        $response = $this->delete("/api/demand/submit/cache/$layoutId")->dump();
    }
}
