
## Fetch Reserve Postulate/Apparatus Dropdown [api/reserve/pa/dropdown]
所有設施/設備選單
### Fetch Reserve Postulate/Apparatus Dropdown [GET]

+ Request (application/json)
    + Attributes
        type : postulate/apparatus
+ Response 200 (application/json)
    + Attributes (Array)
        (object): 設備的資訊
            id : 12 (number, required) - 設備類別的id
            name : 測量儀 (string, required) - 設備名稱


## Fetch Reserve Apparatus List [api/reserve/apparatus]
查詢預約設備列表
### Fetch Reserve Apparatus List [GET]

+ Request (application/json)
    + Attributes
        id : null or 12 (number, required) - 全部設備或設備的id
        date : Wed Mar 15 2016 08:00:00 GMT+0800 (CST) (DateObject, required) - 查閱的某一天
+ Response 200 (application/json)
    + Attributes (Array) :
        (object) : 設備列表
            id : 12 (number, required) - 設備類別id
            image_url (Array) : /storage/par/image/.... (string, required) - 設備圖片
            name : 測量儀 (string, required) - 設備名稱
            total_amount : 2 (number, required) - 設備總數量
            org_unit : 工程部 (string, required) - 所屬部門
            remark : 說明內文 (string, required) - 設備說明
            reserves (Array) - 預約狀態顯示資訊
                (object) : 
                    id : 0 (number) 時間區間Id
                    duration : 08:00-08:30 (string) 時間區段
                    amount : 2 (number) - 當前時間段設備剩餘數量
                    detail (Array)  or emptyArray - 沒有內容回傳空陣列
                        (object) :
                            subscribers : 設二部 李家安 (string, required) - 預約人名稱
                            amount :  1 (number) - 預約數量
                            reason :  丈量用 (number, required) - 預約事由


## Reserve Apparatus [api/reserve/apparatus]
預約設備
### Reserve Apparatus [POST]

+ Request (application/json)
    + Attributes
        + form (object): 預約內容資訊
            pa_id : 12 (number, required) - 設備類別的id
            time (Array) - 預約時段的Ids
                1 (number, required) - 時段的Id
            date : Wed Mar 15 2016 08:00:00 GMT+0800 (CST) (DateObject, required) - 預約日期
            amount : 2 (number, required) - 預約數量
            reason : 事由內容 (string, required) - 預約事由
            remark : 備註內容 (string, required) - 備註
+ Response 200 (application/json)
    + Attributes (object) : 預約成功或失敗及失敗原因
            state : 1 or 0 (number) - 成功或失敗
            error (Array)
                12:00-12:30、12:30-13:00、13:00-13:30、13:30-14:00 (Array) - 無法預約時段

    
## Fetch Reserve Postulate List [api/reserve/postulate]
查詢預約設施列表
### Fetch Reserve Postulate List [GET]

+ Request (application/json)
    + Attributes
        id : null or 12 (number, required) - 全部設施或設施的id
        date : Wed Mar 15 2016 08:00:00 GMT+0800 (CST) (DateObject, required) - 查閱的某一天
+ Response 200 (application/json)
    + Attributes (Array) :
        (object) : 設備列表
            id : 12 (number, required) - 設施類別id
            image_url (Array) : /storage/par/image/.... (string, required) - 設施圖片
            name : 會議室 (string, required) - 設施名稱
            place : 10樓 (string, required) - 設施地點
            people : 10人 (string, required) - 容納人數
            remark : 說明內文 (string, required) - 設施說明
            apparatus_type (Array) - 設備支援類別相關資訊
                工程用 (string, required) - 設備分類的的名稱
            reserves (Array) - 預約狀態顯示資訊
                (object) : 
                    id : 0 (number) 時間區間Id
                    duration : 08:00-08:30 (string) 時間區段
                    detail (Array)  or emptyArray - 沒有內容回傳空陣列
                        (object) :
                            subscribers : 設二部 李家安 (string, required) - 預約人 名稱
                            reason :  開會 (number, required) - 預約事由

## type To Fetch Apparatus Dropdown [api/reserve/apparatus/dropdown]
即時獲取可預約設備選單(以設備類別)
### type To Fetch Apparatus Dropdown [GET]

+ Request (application/json)
    + Attributes
        + name : 工程用 (string, required) - 設備分類的的名稱
+ Response 200 (application/json)
    + Attributes (Array)  or null - 查無設備清單回傳null
        (object): 設備的資訊
            id : 12 (number, required) - 設備類別的id
            name : 測量儀 (string, required) - 設備名稱


## Reserve Postulate [api/reserve/postulate]
預約設施
### Reserve Postulate [POST]

+ Request (application/json)
    + Attributes
        + form (object): 預約內容資訊
            pa_id : 12 (number, required) - 設施類別的id
            time (Array) - 預約時段的Ids
                1 (number, required) - 時段的Id
            date : Wed Mar 15 2016 08:00:00 GMT+0800 (CST) (DateObject, required) - 預約日期
            reason : 事由內容 (string, required) - 預約事由
            remark : 備註內容 (string, required) - 備註
            apparatus (Array) - 需支援設備的Id
                (object): 
                    id : 12 (number, required) - 設備類別的id
                    name : 測量儀 (string, required) - 設備名稱
                    type : 工程用 (string, required) - 設備分類的的名稱
+ Response 200 (application/json)
    + Attributes (object) : 預約成功或失敗及失敗原因
            state : 1 or 0 (number) - 成功或失敗
            error (Array)
                12:00-12:30、12:30-13:00、13:00-13:30、13:30-14:00 (Array) - 無法預約時段
