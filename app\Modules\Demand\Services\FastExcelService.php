<?php

namespace App\Modules\Demand\Services;

use Illuminate\Support\Facades\Session;

class FastExcelService
{
    // protected $user_id;

    public function __construct()
    {
    }

    /**
     * @param $data 匯出資料
     * @param $docName 匯出檔名
     */
    public function export($data, $docName)
    {
        // 移除不合法字元（/ \ : * ? " < > |）
        // 回傳 download 會將 fileName 設定為 http header，但 header 不允許/\等符號。
        $safeName = preg_replace('/[\/\\\\\:\*\?\"\<\>\|]/', '_', $docName);

        return FastExcel($data)->download(date('Ymdhis') . $safeName . '匯出.xlsx');
    }
}
