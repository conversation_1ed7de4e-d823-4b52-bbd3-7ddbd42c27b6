<template>
    <div>
        <tool-list v-if="action == 0" />
        <tool-content v-if="action == 1" />
    </div>
</template>
<script>
import <PERSON><PERSON> from "primevue/button";
import toolList from "./tool-list.vue";
import toolContent from "./tool-content.vue";
export default {
    components: {
        Button,
        toolList,
        toolContent,
    },
    data() {
        return {
            action: 0,
            id:0,
        };
    },
    watch: {},
    mounted() {},
    methods: {},
};
</script>
<style></style>
