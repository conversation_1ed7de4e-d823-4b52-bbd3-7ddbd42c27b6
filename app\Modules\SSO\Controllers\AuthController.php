<?php

namespace App\Modules\SSO\Controllers;

use App\Events\DemandUpdated;
use App\Jobs\CreateACCDemandJob;
use App\Modules\Demand\Services\SubmitService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Session;
use App\Http\Controllers\Controller;
use App\Modules\Demand\Controllers\SubmitController;
use App\Modules\Demand\Models\Company;
use App\Modules\Demand\Models\Demand;
use App\Modules\Demand\Models\DemandLayout;
use App\Modules\Demand\Models\Employee;
use App\Modules\SSO\Models\SsoToken;
use Illuminate\Encryption\Encrypter;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class AuthController extends Controller
{
    protected $company_id;
    protected $user_id;

    public function __construct() {}

    private function check(Request $request)
    {
        if (!$request->has('token')) {
            throw new \Exception('token not found', 500);
        }
        $newEncrypter = new Encrypter(md5(Config('sso_key')), 'AES-256-CBC');
        $token = $newEncrypter->Decrypt($request->get('token'));
        $s = SsoToken::where('token', $token)->where('created_at', '>', Carbon::now()->addMinute(-10))->first();

        return $s;
    }

    private function setSession($ssoToken)
    {
        $e = Employee::find($ssoToken->employee_id);
        Session::put('employee_id', $e->id);
        Session::put('CompanyId', $e->company_id);
        $c = Company::find($e->company_id);
        $t = $c ? $c->payload->get('timezone') : 'Asia/Taipei';
        Session::put('timezone', $t);
    }

    public function SSOLogin(Request $request)
    {
        // $token=$request->get('token');
        try {
            $s = $this->check($request);
            $this->setSession($s);

            return redirect('/demand/signing');
        } catch (\Exception $th) {
            Log::error($th);
            return redirect('/');
        }
    }

    //  accept acc api & auth check
    // add 專案付款單
    public function acc_submit(Request $request)
    {
        try {
            $s = $this->check($request);
            CreateACCDemandJob::dispatch($s->employee_id,$request->all());

            return response(204);
        } catch (\Exception $th) {
            Log::error($th);
            return response('系統錯誤!', 500);
        }
    }
    public function acc_cancel_pay(Request $request)
    {
        try {
            $s = $this->check($request);
            if (!$request->has('id'))
                return response('', 500);

            $this->setSession($s);

            (new SubmitController)->delete($request);

            return  response(1, 200);
        } catch (\Exception $th) {
            Log::error($th);
            return response('server error', 500);
            // return $th;
        }
    }
}
