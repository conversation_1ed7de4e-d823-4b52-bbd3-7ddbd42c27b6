export const permissionMixin = {
    data() {
        return {
            isShowEditDialog: false,
            editOption: "",
            editDialogTitle: "",
            selectedMemberIds: [],
        };
    },
    methods: {
        updateSelectedMemberIds(newVal) {
            this.selectedMemberIds = newVal;
        },
        openEditDialog(data) {
            switch (data.name) {
                case "管理":
                    this.editDialogTitle = "管理權限";
                    break;
                case "瀏覽":
                    this.editDialogTitle = "瀏覽權限";
                    break;
                case "下載":
                    this.editDialogTitle = "下載權限";
                    break;
                default:
                    this.editDialogTitle = "可使用人員";
                    break;
            }
            this.editOption = data.type;
            this.selectedMemberIds = data.members?.map((member) => member.id);
            this.isShowEditDialog = true;
            this.computedPermissionSettingsVisible = false;
        },
        toggleEditDialog(isShow) {
            this.isShowEditDialog = isShow;
        },
        togglePermissionDialog(isShow) {
            this.computedPermissionSettingsVisible = isShow;
        },
    },
};
