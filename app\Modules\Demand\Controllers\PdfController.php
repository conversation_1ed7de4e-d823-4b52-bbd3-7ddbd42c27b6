<?php

namespace App\Modules\Demand\Controllers;


use <PERSON>enberg\Gotenberg;
use <PERSON>enberg\Stream;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Session;
use App\Http\Controllers\Controller;
use App\Modules\Demand\Models\CustomList;
use App\Modules\Demand\Models\DemandLayout;
use App\Modules\Demand\Models\DemandLog;
use App\Modules\Demand\Models\Demand;
use PhpOffice\PhpWord\IOFactory;
use PhpOffice\PhpWord\Settings;
use App\Modules\Demand\Models\Employee;
use App\Traits\FormatDate;
use App\Traits\GetColumnValue;
use Carbon\Carbon;
use Dompdf\Dompdf;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Style\Border;
use PhpOffice\PhpSpreadsheet\Style\Color;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Exception;

class PdfController extends Controller
{
    protected $timezone;

    public function __construct()
    {
        $this->timezone = Session::get('timezone');
    }

    use FormatDate, GetColumnValue;

    public function export(Request $request)
    {
        $id = $request->get('id');
        $type = $request->get('type');
        $finished = $request->get('finished');

        if (!$type)
            return 0;
        if($finished == "true") {
            $d = DemandLog::find($id);
        } else {
            $d = Demand::find($id);
        }

        if (!$d) {
            return 0;
        }

        $layout_original_id = $d->payload['layout_original_id'];
        $dl = DemandLayout::where('payload->original_id', $layout_original_id)->orderBy('id', 'desc')->first();
        $templete = $dl->payload->get('templete');
        if (empty($templete))
            // return null;
            return 0;

        // 有資料，但是是未拆分excel，word的格式，要先修改
        if (!isset($templete['word']) && !isset($templete['excel'])) {
            $ext = pathinfo($templete['base_name'], PATHINFO_EXTENSION);
            if ($ext === 'docx') {
                $templete = ['word' => $templete];
            } elseif ($ext === 'xlx' || $ext === 'xlsx') {
                $templete = ['excel' => $templete];
            }
        }

        // 需要哪種類型的匯出，若沒有返回
        $baseName = isset($templete[$type]) ? $templete[$type]['base_name'] : null;
        if (empty($baseName))
            return 0;

        $filePath = 'app/public/demand/upload/demandLayout/' . $dl->payload['original_id'] . '/' . $baseName;
        $exists = Storage::disk('public')->exists('demand/upload/demandLayout/' . $dl->payload['original_id']  . '/' . $baseName);
        // dd($filePath);
        // 副檔名
        $extension = pathinfo($baseName, PATHINFO_EXTENSION);
        if (!$exists) {
            return null;
        }
        else if ($extension == 'xls' || $extension == 'xlsx') {
            return $this->outputExcel($d, $filePath);
        } else {
            return $this->outputWord($d, $filePath);
        }
    }

    #region word

    private function changeToFullWidth($str)
    {
        // 半形& 套表word會壞掉
        // return str_replace('&',' ＆',$str);
        return $str;
    }
    public function outputWord($d, $filePath)
    {
        $templateProcessor = new \PhpOffice\PhpWord\TemplateProcessor(storage_path($filePath));

        \PhpOffice\PhpWord\Settings::setOutputEscapingEnabled(true);
        // word 內所有可替換的變數
        $getVar = $templateProcessor->getVariables();
        // if ($d) {
        if (!$d)
            return null;

        // 表單欄位
        $forms = $d->payload->get('forms');

        if($d->payload->get('demand_id')) {
            $this->formProcess($templateProcessor, $forms, $getVar, $d->payload->get('demand_id'));
        } else {
            $this->formProcess($templateProcessor, $forms, $getVar, $d->id);
        }

        // 表單小計
        $colsArray = [];
        foreach ($forms as $form) {
            array_push($colsArray, ...$form['columns']);
        }
        foreach ($forms[0]['columns'] as $col) {
            if (in_array('小計' . $col['name'], $getVar) && $col['type'] === 'money' || $col['type'] === 'total' || $col['type'] === 'number') {
                $colsSum = array_reduce($colsArray, function ($acc, $cur) use ($col) {
                    if ($cur['name'] === $col['name']) {
                        return $acc + $cur['value'];
                    }
                    return $acc;
                }, 0);
                $templateProcessor->setValue('小計' . $col['name'], number_format($colsSum, 2) . ($col['type'] === 'money' ? '元' : ''));
            }
        }


        //簽核流程
        $signRoles = $d->payload->get('sign_roles');
        //寫入申請人
        $employee = Employee::withTrashed()->with('orgs')->find($d->created_by);
        $templateProcessor->setValue('申請人員工編號', $employee->payload['employee_number']);
        if(isset($d->payload['applicant']['dep']))
            $templateProcessor->setValue('申請人部門', $d->payload['applicant']['dep']);
        if(isset($d->payload['applicant']['name']))
            $templateProcessor->setValue('申請人', $d->payload['applicant']['name']);
        if(isset($d->payload['applicant']['title']))
            $templateProcessor->setValue('申請人職稱', $d->payload['applicant']['title']);

        $signEmployees = Employee::withTrashed()->wherein('id', array_column($signRoles, 'role_id'))->get();
        foreach ($signRoles as $signRoleIndex => $signRole) {
            //如果不是當前角色最後一次簽核，就跳過，讓最後簽核時的資訊寫入就好，修改起因為 setValue() 不會覆蓋原本的值
            $lastIndex = $signRoleIndex;
            foreach ($signRoles as $index => $role) {
                // 检查每个对象是否包含目标角色
                if ($role['self_name'] == $signRole['self_name']) {
                    // 如果包含目标角色，则记录索引
                    $lastIndex = $index;
                }
            }

            if($lastIndex > $signRoleIndex) continue;

            //寫入簽核流程
            $key = $signRole['self_name'];
            $employee = $signEmployees->firstWhere('id', $signRole['role_id']);

            // $isRepresent =  $signRole['isRepresent'] ? '(代)' : '';
            $isRepresent = isset($signRole['isRepresent']) ? ($signRole['isRepresent'] ? '(代)' : '') : '';

            $templateProcessor->setValue($key, $employee ? $employee->payload->get('name') . $isRepresent : '');
            $templateProcessor->setValue($key . '簽核意見', (isset($signRole['remark']) ? $signRole['remark'] : ''));

            if (in_array($key . '簽核日期', $getVar) && isset($signRole['raw_time']))
                $templateProcessor->setValue($key . '簽核日期', $this->formatDate($signRole['raw_time'], 'date'));
            if (in_array($key . '簽核時間', $getVar) && isset($signRole['raw_time']))
                $templateProcessor->setValue($key . '簽核時間',  $this->formatDate($signRole['raw_time'], 'date_time'));
        }

        // 會簽(待定)
        // $counterRoles = array_filter($signRoles, function($value) {return array_key_exists('to_counter', $value);});
        // foreach ($counterRoles as $key => $value) {

        // }
        // $employee = Employee::with('orgs')->find($d->created_by);

        // 加安測試大陸帳號發現沒替換到，討論說統一繁體
        // if (Session::get('timezone') == 'Asia/Taipei')
        $e = '申請人';
        // if (Session::get('timezone') == 'Asia/Shanghai')
        //     $e = '申请人';
        $templateProcessor->setValue($e, $employee->payload->get('name'));

        if (in_array($e . '部門', $getVar)) {
            $fullname = null;
            isset($employee->orgs) && isset($employee->orgs->first()->payload) && $fullname = $employee->orgs->first()->payload->get('fullname');
            $templateProcessor->setValue($e . '部門', $fullname ?? '');
        }
        if (in_array($e . '職稱', $getVar))
            $templateProcessor->setValue($e . '職稱', $employee->payload->get('job_title'));


        if (in_array('需求單號', $getVar))
            $templateProcessor->setValue('需求單號', $d->no);
        if (in_array('申請日期', $getVar))
            $templateProcessor->setValue('申請日期', $this->formatDate($d->created_at, 'date'));
        if (in_array('申請時間', $getVar))
            $templateProcessor->setValue('申請時間', $this->formatDate($d->created_at, 'date_time'));
        // 部門，職稱，列號

        // 剩下的變數給空白或清除
        $cleanVar = $templateProcessor->getVariables();

        foreach ($cleanVar as $key => $value) {
            $templateProcessor->setValue($value, '');
        }

        $time = now()->format('Ymdhis');
        $templateProcessor->saveAs(storage_path('app/public/demand/temp/document' . $time . '.docx'));

        return $this->convertPDF(storage_path('app/public/demand/temp/document' . $time . '.docx'));

        #region
        // $domPdfPath = base_path('vendor/dompdf/dompdf');
        // \PhpOffice\PhpWord\Settings::setPdfRendererPath($domPdfPath);
        // \PhpOffice\PhpWord\Settings::setPdfRendererName('DomPDF');
        // $Content = \PhpOffice\PhpWord\IOFactory::load(storage_path('app/public/demand/temp/document' . $time . '.docx'));
        // $PDFWriter = \PhpOffice\PhpWord\IOFactory::createWriter($Content,'PDF');
        // $PDFWriter->save(public_path('doc-pdf.pdf'));
        // return response()->download(storage_path('app/public/demand/temp/document' . $time . '.docx'));

        // Settings::setPdfRendererName(Settings::PDF_RENDERER_DOMPDF);
        // Settings::setPdfRendererPath('.');

        // $phpWord = IOFactory::load(storage_path('app/public/demand/temp/document' . $time . '.docx'), 'Word2007');
        // $xmlWriter = IOFactory::createWriter($phpWord, 'PDF');
        // // //@ts-ignore
        // $html = $xmlWriter->getContent(); // @ts-ignore
        // $pdf = \PDF::loadHTML($html);
        // return $pdf->inline();
        // // return Pdf::loadHTML($html)->setPaper('a4', 'landscape')->setWarnings(false)->stream('myfile.pdf');
        // return $this->generateHtmlToPDF($html);
        // return $dompdf->stream('document' . now()->format('Ymdhis') . '.pdf');
        #endregion
    }
    public function formProcess($templateProcessor, $forms, $getVar, $demandId)
    {
        $demandCustomList = CustomList::where('demand_id',  $demandId)->get();
        if (count($forms) > 1) {
            $cloneForms = [];
            $listCollect = [];
            $customListCollect = [];
            foreach ($forms as $form_index => $form) {
                $columns = $form['columns'];
                $aa = [];
                $aa['index'] = $form_index+1;
                foreach ($columns as $column_index => $column) {
                    switch ($column['type']) {
                        case ('input'):
                        case ('number'):
                        case ('total'):
                        case ('database'):
                        case ('cascade'):
                        case ('dropdown'):
                        case ('single'):
                        case ('employee'):
                        case ('date'):
                            $aa[$column['name']] = htmlspecialchars($this->getValue($column));
                            break;
                        case ('money'):
                            $aa[$column['name']] = $this->getValue($column, $pdf = true);
                            break;

                        case ('list'):
                            if (!in_array($column['name'], $getVar)) break;
                            $list = $column['list'];

                            // 多比時 要複製清單表格，加index幫助定位
                            $name = $column['name'];
                            $index = $form_index;
                            $cell = [];
                            $cell[$name] = $name . $index;

                            $cell[$name . '_項目'] = '${' . $name . $index . '_項目}';
                            $cell[$name . '_數量'] = '${' . $name . $index . '_數量}';
                            $cell[$name . '_單位'] = '${' . $name . $index . '_單位}';
                            $cell[$name . '_單價'] = '${' . $name . $index . '_單價}';
                            $cell[$name . '_總計'] = '${' . $name . $index . '_總計}';
                            $cell[$name . '_備註'] = '${' . $name . $index . '_備註}';
                            // $cell = $this->cloneList($column['name'],  $form_index);
                            $aa =  array_merge($aa, $cell);
                            $listCollect = array_merge($listCollect, [$name . $index => $list]);
                            break;
                        case ('customList'):
                            if (!in_array($column['name'], $getVar)) break;
                            $customList = $demandCustomList->findList($form_index, $column_index);

                            // 多比時 要複製清單表格，加index幫助定位
                            $name = $column['name'];
                            $index = $form_index;
                            $cell = [];
                            $cell[$name] = $name . $index;
                            foreach ($customList->payload->get('form_setting') as $key => $value) {
                                $cell[$name . '_' . $value['name']] = '${' . $name . $index  . '_' . $value['name'] . '}';
                            }
                            $aa =  array_merge($aa, $cell);
                            $customListCollect = array_merge(
                                $customListCollect,
                                [
                                    $column['name'] . $form_index => [
                                        'list' =>   $customList->list,
                                        'form_setting' =>   $customList->payload->get('form_setting'),

                                    ]
                                ]
                            );
                            break;
                    }
                }
                array_push($cloneForms, $aa);
            }
            // 多筆的清單會被複製出來
            $templateProcessor->cloneBlock('block_name', 0, true, false, $cloneForms);

            // 把清單資料 塞進去
            if (count($listCollect) > 0)
                $this->listTableRawProcess($templateProcessor,  $listCollect);
            if (count($customListCollect) > 0)
                $this->customLListTableRawProcess($templateProcessor,  $customListCollect);
        }
        else {
            foreach ($forms as $form_index => $form) {
                $templateProcessor->setValue('index',  1);
                $columns = $form['columns'];
                foreach ($columns as $column_index => $column) {
                    switch ($column['type']) {
                        case ('input'):
                        case ('number'):
                        case ('total'):
                        case ('database'):
                        case ('cascade'):
                        case ('dropdown'):
                        case ('single'):
                        case ('employee'):
                        case ('date'):
                            $templateProcessor->setValue($column['name'],  $this->getValue($column));
                            break;
                        case ('money'):
                            $templateProcessor->setValue($column['name'], $this->getValue($column, $pdf = true));
                            break;
                        case ('list'):
                            if (!in_array($column['name'], $getVar)) break;
                            $list = $column['list'];
                            $table = $this->listProcess($templateProcessor, $list,  $column['name']);
                            $templateProcessor->cloneRowAndSetValues($column['name'] . '_項目', $table);
                            $templateProcessor->setValue($column['name'], $column['name']);
                            break;
                        case ('customList'):
                            if (!in_array($column['name'], $getVar)) break;
                            $customList = $demandCustomList->findList($form_index, $column_index);
                            $setting =  $customList->payload->get('form_setting');
                            $table = [];
                            foreach ($customList->list as $list_key => $list) {
                                $cell = [];
                                foreach ($list as $col_key => $col) {
                                    $cell[$column['name'] . '_' . $setting[$col_key]['name']] = $col ??  '';
                                }
                                array_push($table, $cell);
                            }

                            foreach ($setting as $idx => $set) {
                                // 如果使用者上傳檔案的欄位名稱和自訂表單設定欄位名稱相同才塞
                                if (in_array($column['name'] . '_' . $setting[$idx]['name'], $getVar)) {
                                    $templateProcessor->cloneRowAndSetValues($column['name'] . '_' . $setting[$idx]['name'], $table);
                                    break;
                                }
                            }

                            $templateProcessor->setValue($column['name'], $column['name']);
                            break;
                    }
                }
            }
        }
    }

    public function customLListTableRawProcess($templateProcessor, $customListCollect)
    {
        try {
            // 跑多個表單
            foreach ($customListCollect as $listName => $list_value) {
                $table = [];
                // 表單每一行
                foreach ($list_value['list'] as $listKey => $row) {
                    $raws = [];
                    // 表單column
                    foreach ($row as $key => $value) {
                        $raws[$listName . '_' . $list_value['form_setting'][$key]['name']] = $value ?? '';
                    }
                    // $raws[$listName . '_數量'] = isset($value['count']) ? $value['count'] : '';
                    // $raws[$listName . '_單位'] = isset($value['unit']) ? $value['unit'] : '';
                    // $raws[$listName . '_單價'] = isset($value['price']) ? number_format($value['price'], 2, '.', ',') . '元' : '';
                    // $raws[$listName . '_總計'] = isset($value['total']) ? number_format($value['total'], 2, '.', ',') . '元' : '';
                    // $raws[$listName . '_備註'] = isset($value['memo']) && !empty($value['memo']) ? $value['memo'] : '';
                    array_push($table, $raws);
                }
                $templateProcessor->cloneRowAndSetValues($listName . '_' . $list_value['form_setting'][0]['name'], $table);
            }
        } catch (\Exception $e) {
            info($table);
            info($e);
            echo "Something went wrong: ", $e->getMessage(), "\n";
            PHP_EOL;
        }
    }
    public function listTableRawProcess($templateProcessor, $listCollect)
    {
        try {
            foreach ($listCollect as $listName => $list) {
                $table = [];
                foreach ($list as $key => $value) {
                    $raws = [];
                    $raws[$listName . '_項目'] = isset($value['name']) ? $value['name'] : '';
                    $raws[$listName . '_數量'] = isset($value['count']) ? $value['count'] : '';
                    $raws[$listName . '_單位'] = isset($value['unit']) ? $value['unit'] : '';
                    $raws[$listName . '_單價'] = isset($value['price']) ? number_format($value['price'], 2, '.', ',') . '元' : '';
                    $raws[$listName . '_總計'] = isset($value['total']) ? number_format($value['total'], 2, '.', ',') . '元' : '';
                    $raws[$listName . '_備註'] = isset($value['memo']) && !empty($value['memo']) ? $value['memo'] : '';
                    array_push($table, $raws);
                }

                $templateProcessor->cloneRowAndSetValues($listName . '_項目', $table);
            }
        } catch (\Exception $e) {
            info($table);
            info($e);
            echo "Something went wrong: ", $e->getMessage(), "\n";
            PHP_EOL;
        }
    }

    public function listProcess($templateProcessor, $lists, $name)
    {
        $table = [];
        foreach ($lists as $key => $list) {
            $cell = [];
            $cell[$name . '_項目'] = isset($list['name']) ? $list['name'] : '';
            $cell[$name . '_數量'] = isset($list['count']) ? $list['count'] : '';
            $cell[$name . '_單位'] = isset($list['unit']) ? $list['unit'] : '';
            $cell[$name . '_單價'] = isset($list['price']) ? number_format($list['price'], 2, '.', ',') . '元' : '';
            $cell[$name . '_總計'] = isset($list['total']) ? number_format($list['total'], 2, '.', ',') . '元' : '';
            $cell[$name . '_備註'] = isset($list['memo']) && !empty($list['memo']) ? $list['memo'] : '';
            array_push($table, $cell);
        }

        return $table;
    }

    // public function convertPDF($path)
    // {
    //     try {
    //         //TODO: Get your ClientID and ClientSecret at https://dashboard.aspose.cloud (free registration is required).
    //         $ClientSecret = "aacfe5991a5473b4cdf1e3ad2568d7cc";
    //         $ClientId = "81f3349b-f044-48e1-92b7-7b0fb8b5bc47";
    //         $wordsApi = new \Aspose\Words\WordsApi($ClientId, $ClientSecret);
    //         $format = "pdf";
    //         $file = ($path);
    //         $request = new \Aspose\Words\Model\Requests\ConvertDocumentRequest($file, $format, null);
    //         $result = $wordsApi->ConvertDocument($request);
    //         $name = str_replace('docx', 'pdf', $path);
    //         copy($result->getPathName(), $name);
    //         return response()->file(
    //             $name
    //         );
    //         // print_r($result->getPathName());

    //     } catch (Exception $e) {
    //         echo "Something went wrong: ", $e->getMessage(), "\n";
    //         PHP_EOL;
    //     }
    // }

    public function convertPDF($path)
    {
        \Log::info('convert pdf begin... path: ' . $path);

        $response = Gotenberg::send(
            Gotenberg::libreOffice(config('app.pdf_serv_url'))
                ->convert(
                    Stream::path($path),
                )
        );
        \Log::info('convert pdf end...');
        return $response;
    }

    // public function generateHtmlToPDF($html)
    // {
    //     $pieces = explode("<style>", $html);
    //     if (Session::get('timezone') == 'Asia/Taipei')
    //         $font = 'wt011';
    //     if (Session::get('timezone') == 'Asia/Shanghai')
    //         $font = 'msyh';
    //     $fontFace = '
    //                     @font-face {
    //                     font-family: \'' . $font . '\';
    //                     font-weight: normal;
    //                     font-style: normal;
    //                     font-variant: normal;
    //                     src: url("' . public_path('fonts') . '/' . $font . '.ttf");
    //                     }';
    //     $html = $pieces[0] . '<style>' . $fontFace . $pieces[1];
    //     $options = new \Dompdf\Options();
    //     $options->setChroot(public_path());
    //     $options->setFontDir(public_path('fonts'));
    //     $options->setDefaultFont($font);
    //     $options->setIsRemoteEnabled(true);
    //     $dompdf = new Dompdf($options);
    //     $dompdf->loadhtml($html);
    //     $dompdf->setPaper('A4', 'landscape');
    //     // $dompdf->set_option('defaultFont', 'wt011'); // 預設字型(僅支援英文)
    //     $dompdf->render();
    //     // Attachment: 0 直接顯示, 1 強制下載
    //     $dompdf->stream('document' . now()->format('Ymdhis') . '.pdf', ['Attachment' => 1]);
    // }
    #endregion

    #region excel
    public function outputExcel($d, $filePath)
    {
        //  // Load the original Excel file
        $spreadsheet = \PhpOffice\PhpSpreadsheet\IOFactory::load(storage_path($filePath));
        $sheet = $spreadsheet->getSheet(0);
        $excelColumns = $sheet->toArray()[2];

        $forms = $d->payload->get('forms');
        $last = $this->excelForms($sheet, $excelColumns, $forms);

        $signRoles = $d->payload->get('sign_roles');
        $this->excelSignRoles($sheet, $signRoles, $last);


        $encoded_fname = rawurlencode(date('Ymdhis') . $d->payload->get('layout_name') . '匯出.xlsx');
        header('Content-Type:application/octet-stream');
        header('Content-Disposition: attachment; filename="' . $encoded_fname . '"; filename*=utf-8\'\'' . $encoded_fname);
        header('Pragma:no-cache');
        header('Expires:0');
        // Save a copy of the original file as a new file
        $writer = \PhpOffice\PhpSpreadsheet\IOFactory::createWriter($spreadsheet, 'Xlsx');
        return $writer->save("php://output");
    }

    public function excelForms($sheet, $excelColumns, $forms)
    {
        $columnp_pioritys = [];
        for ($x = ord('A'); $x <= ord('Z'); $x++) {
            array_push($columnp_pioritys, chr($x));
        }
        $columnp_pioritys = array_splice($columnp_pioritys, 0, count($excelColumns));
        $baseRow = 3;
        foreach ($forms as $formKey => $formValue) {
            $columns = collect($formValue['columns']);
            //X軸
            $k = $baseRow + $formKey;
            // excel 顯示的代碼
            foreach ($excelColumns as $columnKey => $columnValue) {
                if (!isset($columnValue)) {
                    $sheet->getStyle($columnp_pioritys[$columnKey] . $k)->getNumberFormat()
                        ->setFormatCode(\PhpOffice\PhpSpreadsheet\Style\NumberFormat::FORMAT_TEXT);
                    $sheet->setCellValue($columnp_pioritys[$columnKey] . $k, '');
                    continue;
                }
                // 同一欄多值
                $columnArr = explode('+', $columnValue);

                $txt = '';
                foreach ($columnArr as $key => $value) {
                    try {
                        $item = $columns->firstWhere('name', $value);
                        if (!isset($item))
                            continue;
                        if (gettype($item['value']) == "array") {
                            $txt .= $item['value']['name'];
                        } else {
                            $txt .= $this->getValue($item);
                        }
                    } catch (\Throwable $th) {
                        Log::error($value);
                        Log::error($item);
                    }
                }
                $sheet->getStyle($columnp_pioritys[$columnKey] . $k)->getNumberFormat()
                    ->setFormatCode(\PhpOffice\PhpSpreadsheet\Style\NumberFormat::FORMAT_TEXT);
                $sheet->setCellValue($columnp_pioritys[$columnKey] . $k, $txt);
            }
        }
        $sheet->getStyle('A2:' . end($columnp_pioritys) . ($baseRow + count($forms) - 1))->getBorders()->getAllBorders()->setBorderStyle(Border::BORDER_THIN)->setColor(new Color('ff000000'));

        return $baseRow + count($forms);
    }
    public function excelSignRoles($sheet, $signRoles, $lastRow)
    {
        $styleArray = [
            'alignment' => [
                'horizontal' => \PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER,
            ],
        ];

        $lastRow += 2;
        $k = $lastRow;
        $sheet->setCellValue('B' . $k, '審核進度')
            ->mergeCells('B' . $k . ':G' . $k)
            ->getStyle('B' . $k)
            ->applyFromArray($styleArray);
        $k++;
        $ids = collect($signRoles)->pluck('role_id');
        $employees = Employee::withTrashed()->select('id', 'payload->name as name')->whereIn('id', $ids)->get();
        foreach ($signRoles as $key => $value) {
            $sheet->setCellvalue('B' . $k, $value['self_name']);
            $sheet->setCellvalue('C' . $k, $employees->firstwhere('id', $value['role_id'])['name']);
            if(isset($value['remark'])) {
                $sheet->setCellvalue('D' . $k, $value['remark'])
                    ->mergeCells('D' . $k . ':F' . $k);
            }
            if(isset($value['timestamp'])) {
                $sheet->setCellvalue('G' . $k, $value['timestamp']);
            }
            $k++;
        }

        $sheet->getStyle('B' . $lastRow . ':G' . ($k - 1))->getBorders()->getAllBorders()->setBorderStyle(Border::BORDER_THIN)->setColor(new Color('ff000000'));
    }

    #endregion
}
