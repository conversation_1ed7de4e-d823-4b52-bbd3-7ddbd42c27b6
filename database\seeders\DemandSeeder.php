<?php

namespace Database\Seeders;

use App\Modules\Demand\Models\DemandLayout;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class DemandSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        // $connection = app()->make('db')->connection();
        DB::table('demand_layouts')->insert([
            'id' => 426,
            'group_id' => 23,
            'name' => '0831測試',
            'open' => true,
            'payload' => '{"sort": 0, "columns": [{"id": "shxH_0OlSpZL-LkY2tNTA", "name": "金額", "type": "money", "selected": {"id": 0, "type": "money", "fname": "輸入題(金額)"}}], "remarks": [], "version": 0, "templete": [], "original_id": 426, "setting_code": "13", "summaryColId": null, "import_from_others": false}',
            'sign_role' => '[{"id": 1, "type": 1, "child": false, "role_id": 569, "document": false, "list_must": [], "self_name": "審核關卡1", "audit_musts": [], "limit_dollar": false, "countersigned": false, "self_applicant": false, "fill_column_ids": [], "fill_column_list_ids": []}]',
            'can_multi' => false,
            'auto_finished' => false,
            'created_by' => 569,
            'updated_by' => 569,
        ]);
    }
}
