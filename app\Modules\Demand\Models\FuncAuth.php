<?php

namespace App\Modules\Demand\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
class FuncAuth extends Model
{
    //
    use SoftDeletes;
    use \Staudenmeir\EloquentJsonRelations\HasJsonRelationships;
    protected $fillable = [
        'id', 'company_id', 'payload'
    ];

    protected $casts = [
        'payload' => 'collection'
    ];

    public function users()
    {
        return $this->belongsToJson(Employee::class, 'payload->user_list');
    }

    public function parUsers()
    {
        return $this->belongsTo<PERSON>son(Employee::class, 'payload->par_list');
    }

    public function createWhite()
    {
        return $this->belongsToJson(Employee::class, 'payload->create_white');
    }
}
