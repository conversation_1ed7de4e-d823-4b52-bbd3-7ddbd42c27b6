<template>
    <div>
        <div v-if="$root.action == 0">
            <div
                style="height: calc(100vh - 23rem)"
                v-if="listsLoaded && lists.length == 0"
                class="flex items-center"
            >
                <div class="mx-auto">
                    <img
                        src="@images/page_state/empty.png"
                        alt=""
                    />
                    <p v-if="lists && !lists.length" class="text-center">
                        目前暫無任何預約
                    </p>
                    <p v-else-if="lists == undefined">獲取失敗</p>
                </div>
            </div>
            <div
                v-for="(list, index) in lists"
                :key="list.id"
                class="demandList block md:flex overflow-auto justify-between w-full mb-4"
            >
                <div class="w-full md:w-1/6">
                    <img width="200" :src="list.image_url" alt="" />
                </div>
                <div class="w-full md:w-1/6">
                    <h2 class="text-2xl font-semibold">{{ list.name }}</h2>
                </div>
                <div class="w-full md:w-1/6">
                    <span class="text-sm text-gray-400"> 預約日期 </span>
                    <br />
                    {{ list.date }}
                </div>
                <div v-if="list.amount !== null" class="w-full md:w-1/6">
                    <span class="text-sm text-gray-400"> 預約數量 </span>
                    <br />
                    {{ list.amount }}
                </div>
                <div v-else class="w-full md:w-1/6">
                    <span class="text-sm text-gray-400"> 設備支援 </span>
                    <br />
                    <div class="flex flex-wrap">
                        <div
                            v-for="(app, index) in list.apparatus"
                            :key="index"
                        >
                            {{ app
                            }}<span
                                v-if="!(index === list.apparatus.length - 1)"
                                >、</span
                            >
                        </div>
                    </div>
                </div>
                <div class="w-full md:w-1/6">
                    <span class="text-sm text-gray-400"> 預約時段 </span>
                    <br />
                    <div class="flex flex-wrap">
                        <span>{{ combineTime(list.time) }}</span>
                    </div>
                </div>
                <div class="w-full md:w-1/6">
                    <span class="text-sm text-gray-400"> 預約事由 </span>
                    <br />
                    {{ list.reason }}
                </div>
                <div class="w-full md:w-1/6">
                    <span class="text-sm text-gray-400"> 備註 </span>
                    <br />
                    <!-- {{ list.remark }} -->
                    <div v-html="list.remark"></div>
                </div>
                <div class="flex justify-between flex-row md:flex-col">
                    <Button
                        label="取消預約"
                        class="w-28 mb-2"
                        :class="
                            list.cancel_btn == 0
                                ? 'p-button-outlined p-button-secondary'
                                : ''
                        "
                        @click="
                            displayModal = true;
                            cancel_Id = list.id;
                            list_index = index;
                        "
                    />
                    <!-- 取消預約確認彈窗 -->
                    <Dialog
                        :visible.sync="displayModal"
                        :closable="false"
                        :containerStyle="{
                            width: '30vw',
                        }"
                        :modal="true"
                    >
                        <div class="pb-8">
                            <img
                                class="mx-auto"
                                src="@images/icon/notify.png"
                            />
                            <br />
                            <p class="text-center">是否確定取消預約</p>
                        </div>
                        <template #footer>
                            <div class="flex justify-evenly">
                                <Button
                                    @click="displayModal = false"
                                    label="取消"
                                    class="p-button-outlined p-button-secondary"
                                />
                                <Button
                                    label="確定"
                                    class=""
                                    @click="
                                        cancelReserve(
                                            cancel_Id,
                                            index,
                                            list.cancel_btn
                                        );
                                        displayModal = false;
                                    "
                                />
                            </div>
                        </template>
                    </Dialog>
                    <Button
                        v-if="list.cancel_btn == 1"
                        label="修改預約"
                        class="p-button-outlined p-button-secondary w-28"
                        @click="editReserve(list.id, index, list.type)"
                    />
                </div>
            </div>
        </div>
        <div v-if="$root.action == 1">
            <reserveApparatus
                v-if="$root.reserve == 0"
                :edit_data="edit_list"
            />
            <reservePostulate v-else :edit_data="edit_list" />
        </div>
        <Toast ref="toast" position="top-center" />
        <div
            v-if="$root.loading"
            class="fixed top-0 left-0 w-screen h-screen bg-gray-200 bg-opacity-50 z-40"
        >
            <div
                class="fixed top-1/2 left-1/2 transform -translate-y-1/2 -translate-x-1/2 z-50"
            >
                <ProgressSpinner />
            </div>
        </div>
    </div>
</template>

<script>
import axios from "axios";
import Button from "primevue/button";
import Toast from "primevue/toast";
import reservePostulate from "@/par/reserve/postulate";
import reserveApparatus from "@/par/reserve/apparatus";
import Dialog from "primevue/dialog";
import ProgressSpinner from "primevue/progressspinner";
export default {
    components: {
        Button,
        Toast,
        reservePostulate,
        reserveApparatus,
        Dialog,
        ProgressSpinner,
    },
    data() {
        return {
            lists: [],
            listsLoaded: false,
            url: "/api/par/reserve/own",
            list_index: 0,
            edit_list: {},
            displayModal: false,
            cancel_Id: 0,
        };
    },
    watch: {
        "$root.action": function (newValue) {
            if (newValue == 0) {
                this.fetchList();
                this.edit_list = {};
            }
        },
    },
    mounted() {
        this.fetchList();
    },
    methods: {
        async fetchList() {
            try {
                const response = await axios.get(this.url);
                this.lists = response.data;
                this.listsLoaded = true;
            } catch (error) {
                console.error(error);
            }
        },

        checkAll() {
            this.checks = [];
            if (this.checkedAll) {
                for (var i in this.lists.data) {
                    this.checks.push(this.lists.data[i].id);
                }
            }
        },
        cancelReserve(id, index, type) {
            this.$root.loading = true;
            axios
                .post("/api/par/reserve/cancel", {
                    id: id,
                    cancel_type: type,
                    list: this.lists[this.list_index],
                })
                .then((response) => {
                    if (response.data.state) {
                        this.$refs.toast.add({
                            severity: "success",
                            summary: "取消預約成功",
                            life: 3000,
                        });
                        this.lists.splice(index, 1);
                        this.fetchList();
                    } else
                        this.$refs.toast.add({
                            severity: "error",
                            summary: response.data.error,
                            life: 3000,
                        });
                })
                .catch((error) => {
                    console.error(error);
                })
                .finally(() => {
                    this.$root.loading = false;
                });
        },
        async editReserve(id, index, type) {
            await axios
                .get("/api/par/reserve/edit", {
                    params: {
                        id: id,
                        type: type,
                    },
                })
                .then((response) => {
                    this.edit_list = response.data;
                })
                .catch((error) => {
                    console.error(error);
                });

            this.$root.action = 1;
            this.list_index = index;
            type == "apparatus"
                ? (this.$root.reserve = 0)
                : (this.$root.reserve = 1);
            this.$root.reserve_id = id;
        },
        combineTime(time) {
            return time[0].slice(0, 6) + time[time.length - 1].slice(6);
        },
    },
};
</script>
