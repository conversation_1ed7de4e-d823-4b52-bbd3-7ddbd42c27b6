<template>
    <div ref="outBox">
        <div
            style="height: calc(100vh - 160px)"
            class="w-full bg-white rounded-xl shadow pt-6 overflow-y-auto relative"
        >
            <div class="flex px-6">
                <Button
                    @click="$parent.action = 1"
                    label="新增"
                    class="w-28 mr-4"
                />
                <div class="w-1/4 p-inputgroup">
                    <InputText
                        v-model="key"
                        class="w-72"
                        placeholder="搜尋設施"
                    />
                    <Button icon="pi pi-search" @click="fetchList(1, 10)" />
                </div>
            </div>
            <div
                ref="infoBox"
                class="overflow-auto whitespace-nowrap block mt-8"
            >
                <table class="min-w-full">
                    <thead>
                        <tr
                            class="text-left text-gray-400 border-b border-gray-200"
                        >
                            <th class="py-2 leading-3 pl-6">
                                <Checkbox
                                    v-model="checkedAll"
                                    @change="checkAll"
                                    :binary="true"
                                />
                            </th>
                            <th class="py-2">設施圖片</th>
                            <th class="py-2">設施名稱</th>
                            <th class="py-2">預約狀態</th>
                            <th class="py-2">當前使用人</th>
                            <th class="py-2">開放狀態</th>
                            <th class="py-2 pr-6">&emsp;&emsp;&emsp;</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr
                            v-for="(list, index) of lists.data"
                            :key="list.id"
                            class="text-left bg-white even:bg-gray-100"
                        >
                            <td class="py-2 leading-3 pl-6">
                                <Checkbox v-model="checks" :value="list.id" />
                            </td>
                            <td class="py-2">
                                <img width="100" :src="list.image_url" alt="" />
                            </td>
                            <td class="py-2">{{ list.name }}</td>
                            <td class="py-2">
                                <div class="flex">
                                    <p
                                        class="rounded-full py-1.5 border text-sm px-2"
                                        :class="
                                            list.state == '預約中'
                                                ? 'bg-indigo-50 text-indigo-400 border-indigo-400'
                                                : 'bg-green-50 text-green-400 border-green-300'
                                        "
                                    >
                                        {{ list.state }}
                                    </p>
                                </div>
                            </td>
                            <td class="py-2">
                                <div
                                    v-for="(sub, sub_index) of list.subscribers"
                                    :key="sub.reserveId"
                                    class="flex flex-col relative"
                                >
                                    <p
                                        @click="
                                            displaySub = true;
                                            list_index = index;
                                            list_subscribers_index = sub_index;
                                        "
                                        class="underline cursor-pointer"
                                    >
                                        {{ sub.org_unit + " " + sub.name }}
                                    </p>
                                </div>
                            </td>
                            <td>
                                <InputSwitch
                                    @change="
                                        switch_delete = 1;
                                        list.is_open
                                            ? openPa(list.id, list.is_open)
                                            : (displayDelete = true);
                                        list_index = index;
                                    "
                                    v-model="list.is_open"
                                />
                            </td>
                            <td class="py-2 pr-6">
                                <div class="flex justify-between">
                                    <button>
                                        <img
                                            src="@images/icon/edit_enable.png"
                                            alt="edit"
                                            @click="edit(list.id)"
                                        />
                                    </button>
                                    <button
                                        :disabled="list.subscribers.length !== 0"
                                        :class="{ 'cursor-not-allowed': list.subscribers.length !== 0 }"
                                        @click="
                                            list.subscribers.length == 0
                                                ? (displayDelete = true)
                                                : (displayDelete = false);
                                            list_index = index;
                                            switch_delete = 0;
                                        "
                                    >
                                        <img
                                            v-if="list.subscribers.length == 0"
                                            src="@images/icon/delete_enable.png"
                                            alt="delete"
                                        />
                                        <img
                                            v-else
                                            src="@images/icon/delete_disable.png"
                                            alt="delete_disable"
                                        />
                                    </button>
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
            <div :style="white_space"></div>
            <Paginator
                class="sticky bottom-0 left-0"
                :data="lists"
                @page="fetchList"
            />
            <div class="w-full sticky bottom-0 z-40">
                <transition name="slide">
                    <div
                        v-if="checks.length !== 0"
                        class="flex p-5 justify-end bg-white shadow"
                    >
                        <div class="flex">
                            <p class="my-auto">已選取{{ checks.length }}個</p>
                            <p
                                @click="(checks = []) && (checkedAll = false)"
                                class="my-auto mx-4 underline cursor-pointer"
                            >
                                取消選取
                            </p>
                            <Button
                                class="w-28"
                                label="全部刪除"
                                @click="multipleRemove"
                            />
                        </div>
                    </div>
                </transition>
            </div>
        </div>
        <Toast ref="toast" position="top-center" />
        <Dialog
            header=" "
            :visible.sync="displaySub"
            :containerStyle="{
                width: '20vw',
            }"
            :dismissableMask="true"
            :closable="true"
            :modal="true"
        >
            <!-- 當前使用人燈箱 -->
            <div v-if="displaySub">
                <p class="py-2">
                    預約人&emsp;&emsp;|&emsp;{{
                        lists.data[list_index].subscribers[
                            list_subscribers_index
                        ].org_unit +
                        " " +
                        lists.data[list_index].subscribers[
                            list_subscribers_index
                        ].name
                    }}
                </p>
                <p class="py-2">
                    預約日期&emsp;|&emsp;{{
                        lists.data[list_index].subscribers[
                            list_subscribers_index
                        ].date
                    }}
                </p>
                <p class="py-2">
                    預約時段&emsp;|&emsp;
                    <span>
                        <span
                            >{{
                                combineTime(
                                    lists.data[list_index].subscribers[
                                        list_subscribers_index
                                    ].time
                                )
                            }}
                        </span>
                    </span>
                </p>
                <p class="py-2">
                    預約事由&emsp;|&emsp;{{
                        lists.data[list_index].subscribers[
                            list_subscribers_index
                        ].reason
                    }}
                </p>
                <p class="py-2">
                    請勾選需要IT支援的項目&emsp;|&emsp;
                    <span>
                        <span
                            v-for="(app, index) in lists.data[list_index]
                                .subscribers[list_subscribers_index]
                                .apparatus_type"
                            :key="index"
                            >{{ app
                            }}<i
                                v-if="
                                    index !==
                                    lists.data[list_index].subscribers[
                                        list_subscribers_index
                                    ].apparatus_type.length -
                                        1
                                "
                                >、</i
                            ></span
                        >
                    </span>
                </p>
                <p class="py-2">
                    備註&emsp;&emsp;&emsp;|&emsp;{{
                        lists.data[list_index].subscribers[
                            list_subscribers_index
                        ].remark
                    }}
                </p>
                <!-- 提前釋出功能 -->
                <div
                    class="buttons mt-4 w-full flex justify-between md:justify-end card"
                >
                    <Button
                        class="cursor-pointer"
                        @click="
                            release_reserveId =
                                lists.data[list_index].subscribers[
                                    list_subscribers_index
                                ].reserveId;
                            confirm1($event);
                        "
                        label="提前釋出"
                    />
                    <!--     displaySubOptions = true; -->
                </div>
            </div>
            <template #footer> <div></div></template>
        </Dialog>
        <ConfirmPopup
            ref="confirmPopup"
            style="z-index: 2000; transform: translate(-80px)"
        ></ConfirmPopup>
        <!-- <Dialog
            :visible.sync="displaySubOptions"
            :containerStyle="{
                width: '20vw',
            }"
            :dismissableMask="true"
            :closable="false"
            :modal="true"
        >
            <div class="pb-8">
                <img
                    class="mx-auto"
                    src="/storage/demand/image/popup_state/warning.png"
                />
                <br />
                <p class="text-center">是否確定提前釋出?</p>
            </div>
            <template #footer>
                <div class="flex justify-evenly">
                    <Button
                        @click="displaySubOptions = false"
                        label="取消"
                        class="p-button-outlined p-button-secondary w-28"
                    />
                    <Button @click="release()" label="確定" class="w-28" />
                </div>
            </template>
        </Dialog> -->
        <Dialog
            :visible.sync="displayDelete"
            :containerStyle="{
                width: '25vw',
            }"
            :dismissableMask="true"
            :closable="false"
            :modal="true"
        >
            <div class="pb-8">
                <img class="mx-auto" src="@images/icon/notify.png" />
                <br />
                <p v-if="switch_delete == 0" class="text-center">
                    是否刪除此設施?
                </p>
                <div v-else>
                    <p class="text-center">是否關閉此設施?</p>
                    <p class="text-center" style="color: red">
                        *如關閉設施，系統將自動取消後續預約
                    </p>
                </div>
            </div>
            <template #footer>
                <div class="flex justify-evenly">
                    <Button
                        @click="
                            switch_delete !== 0 &&
                            lists.data[list_index].is_open
                                ? (lists.data[list_index].is_open = false)
                                : (lists.data[list_index].is_open = true);
                            displayDelete = false;
                        "
                        label="取消"
                        class="p-button-outlined p-button-secondary w-28"
                    />
                    <Button
                        @click="
                            switch_delete == 0
                                ? remove()
                                : openPa(
                                      lists.data[list_index].id,
                                      lists.data[list_index].is_open
                                  )
                        "
                        label="確定"
                        class="w-28"
                    />
                </div>
            </template>
        </Dialog>
    </div>
</template>
<script>
import axios from "axios";
import Button from "primevue/button";
import Checkbox from "primevue/checkbox";
import InputText from "primevue/inputtext";
import Paginator from "@/demand/common/Paginator";
import Sidebar from "primevue/sidebar";
import Dialog from "primevue/dialog";
import RadioButton from "primevue/radiobutton";
import InputSwitch from "primevue/inputswitch";
import Toast from "primevue/toast";
import ConfirmPopup from "primevue/confirmpopup";

export default {
    components: {
        Button,
        Checkbox,
        InputText,
        Paginator,
        Sidebar,
        Dialog,
        RadioButton,
        InputSwitch,
        Toast,
        ConfirmPopup,
    },
    data() {
        return {
            lists: [],
            checks: [],
            checkedAll: false,
            visibleBottom: false,
            key: "",
            displaySub: false,
            displaySubOptions: false,
            displayDelete: false,
            list_index: null,
            list_subscribers_index: null,
            release_reserveId: null,
            white_space: {},
            switch_delete: 0,
        };
    },
    mounted() {
        this.fetchList(1, 10);
    },
    updated() {
        this.computedWhite();
    },
    methods: {
        fetchList(page, per) {
            this.checks = [];
            this.checkedAll = false;
            axios
                .get(this.$parent.url, {
                    params: {
                        page: page,
                        per: per,
                        key: this.key,
                    },
                })
                .then((response) => {
                    this.lists = response.data;
                    this.computedWhite();
                })
                .catch((error) => {
                    console.error(error);
                });
        },
        checkAll() {
            this.checks = [];
            if (this.checkedAll) {
                for (var i in this.lists.data) {
                    this.checks.push(this.lists.data[i].id);
                }
            }
        },

        edit(id) {
            this.$parent.id = id;
            this.$parent.action = 1;
        },
        release() {
            axios
                .post("/api/par/reserve/cancel", {
                    id: this.release_reserveId,
                    cancel_type: 0,
                })
                .then((response) => {
                    if (response.data) {
                        this.$refs.toast.add({
                            severity: "success",
                            summary: "提前釋出成功",
                            life: 3000,
                        });
                        this.fetchList(1, 10);
                    } else
                        this.$refs.toast.add({
                            severity: "error",
                            summary: response.data.error,
                            life: 3000,
                        });
                })
                .catch((error) => {
                    console.error(error);
                })
                .finally(() => {
                    this.displaySubOptions = false;
                });
        },
        showDetail(sub) {
            console.log(sub);
        },
        remove(ids) {
            if (this.lists.data[this.list_index].subscribers.length == 0) {
                axios
                    .post(this.$parent.url + "/delete", {
                        id:
                            this.checks.length !== 0
                                ? ids
                                : this.lists.data[this.list_index].id,
                    })
                    .then((response) => {
                        this.fetchList();
                        this.$refs.toast.add({
                            severity: "success",
                            summary: "刪除成功",
                            life: 3000,
                        });
                    })
                    .catch((error) => {
                        console.error(error);
                        this.$refs.toast.add({
                            severity: "error",
                            summary: "刪除失敗",
                            life: 3000,
                        });
                    })
                    .finally(() => {
                        this.displayDelete = false;
                    });
            }
        },

        multipleRemove() {
            this.remove(this.checks);
        },
        computedWhite() {
            var computed = 204 + this.$refs.infoBox.offsetHeight;
            var out = this.$refs.outBox.offsetHeight;
            var calc = out - computed + "px";
            this.$set(this.white_space, "height", calc);
        },
        openPa(id, isOpen) {
            axios
                .put("/api/par/switch", {
                    id: id,
                    type: "postulate",
                    open: isOpen,
                })
                .then((response) => {
                    if (response.data) {
                        if (isOpen) {
                            this.$refs.toast.add({
                                severity: "success",
                                summary: "設施已開啟",
                                life: 3000,
                            });
                        } else {
                            this.$refs.toast.add({
                                severity: "success",
                                summary: "設施已關閉",
                                life: 3000,
                            });
                        }
                    } else {
                        this.$refs.toast.add({
                            severity: "error",
                            summary: "修改失敗",
                            life: 3000,
                        });
                    }
                })
                .catch((error) => {
                    console.error(error);
                    if (error.response) {
                        this.$refs.toast.add({
                            severity: "error",
                            summary: "修改失敗!",
                            life: 3000,
                        });
                    }
                })
                .finally(() => {
                    this.displayDelete = false;
                });
        },
        // 提前釋出
        confirm1(event) {
            this.$refs.confirmPopup.visible = true;
            this.$refs.confirmPopup.target = event.currentTarget;
            this.$refs.confirmPopup.confirmation = {
                message: "是否確定提前釋出",
                acceptLabel: "確定",
                rejectLabel: "取消",
                accept: () => {
                    this.release();
                    this.displaySub = false;
                },
                reject: () => {
                    this.displaySub = false;
                },
            };
        },
        combineTime(time) {
            return time[0].slice(0, 6) + time[time.length - 1].slice(6);
        },
    },
};
</script>
<style>
.slide-leave-active,
.slide-enter-active {
    transition: all 0.9s ease;
}
.slide-enter-from {
    transform: translateY(-100%);
}
.slide-leave-to {
    transform: translateY(100%);
}
</style>
