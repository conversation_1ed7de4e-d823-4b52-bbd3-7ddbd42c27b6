<?php

namespace App\Console\Commands;

use App\Jobs\ReserveNotify;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;
use App\Modules\par\Models\Postulate;
use App\Modules\par\Services\PublicService;
use App\Modules\par\Events\notificationEvent;
use App\Modules\Demand\Models\Notification;
use Illuminate\Support\Facades\Config;

class NotificationComman extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'notify:cmd';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'send email notification';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        // 當下跟一小時前各跑一次
        Log::info('notify start');
        ReserveNotify::dispatch();
        Log::info('notify end');
        return 0;
    }
}
