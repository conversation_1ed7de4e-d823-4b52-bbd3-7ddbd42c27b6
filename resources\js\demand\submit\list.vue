<template>
  <div>
    <!-- 進度列表 -->
    <div v-if="action == 0">
      <div class="w-52 grid p-fluid">
          <div class="col-12 md:col-4">
              <div class="p-inputgroup">
                  <span class="p-inputgroup-addon cursor-pointer" @click="getLists(1, 10)">
                    <i class="pi pi-search"></i>
                  </span>
                  <InputText
                    class="lg:w-44 xl:w-48"
                    placeholder="搜尋關鍵字"
                    type="text"
                    v-model="keyword"
                    @keydown.enter="getLists(1, 10)"
                  />
              </div>
          </div>
      </div>
      <br />
      <div class="thead">
        <div class="flex w-1/6 my-auto">
          <!-- <input
                        v-if="signOff"
                        :value="data.id"
                        class="mr-0 md:mr-4 my-auto"
                        type="checkbox"
                        v-model="checked"
                    /> -->
          <p class="text-sm text-gray-400">需求名稱</p>
        </div>
        <div
          :class="col_index === columns.length - 1 ? 'w-16' : (column == '部門'||column == '摘要') ? 'w-1/6 hidden sm:block' : 'w-1/6'"
          v-for="(column, col_index) in columns"
          :key="col_index"
        >
          <span class="text-sm text-gray-400"> {{ column }}</span>
        </div>
      </div>
      <div v-if="(dataLists && !dataLists.length) || dataLists == undefined">
        <DataStatus :data="dataLists" :status=status :col="getNumberOfCol()" />
      </div>
      <div v-else v-for="(list, listIndex) in dataLists" :key="list.id">
        <DemandRequestList
          :data="list"
          :index="listIndex"
          :layer="'outside'"
          @click="getListsRow(listIndex)"
        />
      </div>
    </div>

    <!-- 進度內頁 -->
    <div v-if="action == 1" style="max-width: 1624px" class="mx-auto">
      <DemandRequestList
        :data="list"
        :layer="'inner'"
        :editCustomForm="false"
        @back="(val) => (action = val)"
      />
      <div class="buttons w-full flex justify-between md:justify-end pt-6">
        <Button
          v-if="list.status == 2"
          @click="reviewAgainBtn()"
          label="指定再審"
          class="p-button-outlined p-button-secondary w-28 mr-2"
        />
        <Button
          v-if="list.status == 7"
          @click="editForm(list.layout_id)"
          label="修改申請"
          class="p-button-outlined p-button-secondary w-28 mr-2"
        />
        <Button
          v-if="list.status == 2"
          @click="closeCaseBtn()"
          label="結案"
          class="w-28"
        />
        <Button
          v-if="list.status != 2"
          @click="cancelBtn()"
          label="取消申請"
          class="p-button-outlined p-button-secondary w-28 mr-2"
        />
      </div>

      <Dialog
        header="指定再審"
        :visible.sync="displayModal"
        :dismissableMask="true"
        :containerStyle="{
          width: '60%',
        }"
        :modal="true"
      >
        <div class="mr-4 my-auto font-bold text-gray-400">
          <div class="pb-8">
            <span>指定節點 </span>
            <br />
            <Dropdown
              class="lg:w-44 xl:w-48 rounded-md shadow"
              placeholder="選擇指定節點"
              :options="getRoles"
              optionLabel="name"
              optionValue="id"
              data-key="id"
              v-model="reviewToNodeId"
            >
              <template #option="slotProps">
                <div>
                  <span>{{ slotProps.option.rank }}</span>
                  <span>{{ slotProps.option.name }}</span>
                </div>
              </template>
            </Dropdown>
          </div>
          <div class="pb-8">
            <span>再審事由</span>
            <br />
            <InputText
              class="w-3/4"
              placeholder="輸入再審事由"
              v-model="reviewRemark"
            ></InputText>
          </div>
        </div>

        <template #footer>
          <div class="flex justify-center">
            <Button
              @click="displayModal = false"
              label="取消"
              class="p-button-outlined p-button-secondary w-28 mr-5"
            />
            <Button @click="reviewReturnBtn()" label="退回" class="w-28" />
          </div>
        </template>
      </Dialog>
      <ConfirmDialog ref="confirmDialog" />
    </div>
    <div v-if="action == 2">
      <Submit
        :layout_id="list.layout_id"
        :demand_id="list.id"
        :list_forms="default_forms"
        :default_forms="list.forms"
        :default_setting="default_setting"
      />
    </div>
    <ScrollTop :threshold="1" icon="pi pi-arrow-up" />
    <Toast ref="toast" position="top-center" />
  </div>
</template>
<script>
import axios from "axios";
import DemandRequestList from "@/demand/common/demand-request-list";
import Dropdown from "primevue/dropdown";
import Button from "primevue/button";
import InputText from "primevue/inputtext";
import ScrollTop from "primevue/scrolltop";
import Toast from "primevue/toast";
import ConfirmDialog from "primevue/confirmdialog";
import Dialog from "primevue/dialog";
import DataStatus from "@/demand/common/data-status";
import Submit from "@/demand/submit/submit.vue";
export default {
  components: {
    DemandRequestList,
    Dropdown,
    InputText,
    ScrollTop,
    Toast,
    ConfirmDialog,
    Dialog,
    Button,
    DataStatus,
    Submit,
  },
  data() {
    return {
      action: 0,
      dataLists: undefined,
      list: {},
      listIndex: 0,
      apiURL: "/api/demand/submit/lists",
      columns: ["單號", "申請日期", "摘要", "申請狀態"],
      displayModal: false,
      reviewToNodeId: 0, //再審節點id
      reviewRemark: "", //再審事由
      getRoles: [],
      default_forms: [],
      default_setting: null,
      form_list: null,
      keyword: "",
      status: 0,
    };
  },
  watch: {
    //修改申請返回將暫存需求單刪除
    action: function (newValue) {
      if (newValue == 0) {
        this.default_forms = [];
      }
    },
  },
  mounted() {
    this.getLists();
  },
  methods: {
    editForm(layout_id) {
      this.form_list = JSON.stringify(this.list.forms);
      if (this.default_forms.length == 0) {
        axios
          .get("/api/demand/submit/original-layout", {
            params: { layout_id: layout_id },
          })
          .then((response) => {
            //取得原單layout並帶入對應的值
            this.default_setting = response.data;
            this.list.forms.forEach(() => {
              this.default_forms.push(
                JSON.parse(JSON.stringify(response.data.columns))
              );
            });

            // loop form
            this.default_forms.forEach((d_form, d_formIndex) => {
              // loop column
              d_form.forEach((d_col, d_index) => {
                var col = this.value_forms[d_formIndex][d_index];
                if (
                  col.type == "document" ||
                  col.type == "list" ||
                  col.type == "customList"
                ) {
                  if (col.type == "list") {
                    this.$set(d_col, "list", col.list);
                  } else if (col.type == "document") {
                    this.$set(d_col, "files", col.files);
                  } else if (col.type == "customList") {
                    this.$set(d_col, "custom_list", col.custom_list);
                    this.$set(d_col, "form_setting", col.form_setting);
                    this.$set(d_col, "can_insert", col.can_insert);
                  }
                } else {
                  if (Object.keys(col).includes("value")) {
                    if (col.type == "time" || col.type == "date") {
                      this.$set(d_col, "value", new Date(col.dateTime));
                      this.$set(d_col, "dateTime", col.dateTime);
                    } else {
                      this.$set(d_col, "value", col.value);
                    }
                  } else {
                    if (col.type !== "time" && col.type !== "date") {
                      this.$set(d_col, "value", null);
                    }
                  }
                }
              });
            });
          })
          .catch((error) => {
            console.error(error);
          })
          .finally(() => {
            this.action = 2;
          });
      } else {
        this.action = 2;
      }
    },
    getLists() {
      this.status = 1;
      this.dataLists = undefined;
      axios
        .get(this.apiURL, {
          params: {
            keyword: this.keyword,
          },
        })
        .then((response) => {
          this.status = 2;
          this.dataLists = response.data ?? [];
        })
        .catch((error) => {
          this.status = 2;
          console.error(error);
          this.dataLists = undefined;
        });
    },
    getListsRow(index) {
      this.listIndex = index;
      this.action = 1;
      this.list = this.dataLists[this.listIndex];
      if (Object.keys(this.list).length) {
        this.getRolesDropdownAPI();
      }
    },
    getRolesDropdownAPI() {
      axios
        .get(this.apiURL + "/sign/roles/dropdown", {
          params: { id: this.list.id },
        })
        .then((response) => {
          this.getRoles = response.data ?? [];
        })
        .catch((error) => {
          console.error(error);
        });
    },
    cancelBtn() {
      //DB會直接硬刪除
      this.$refs.confirmDialog.visible = true;
      this.$refs.confirmDialog.confirmation = {
        message: "確定要取消申請嗎?",
        acceptLabel: "確定",
        rejectLabel: "取消",
        accept: () => {
          axios
            .delete(this.apiURL + "/" + this.list.id)
            .then((response) => {
              if (response.data) {
                this.$refs.toast.add({
                  severity: "success",
                  summary: "取消成功",
                  life: 3000,
                });
                this.action = 0;
                this.getLists();
              } else {
                this.$refs.toast.add({
                  severity: "error",
                  summary: "取消失敗",
                  life: 3000,
                });
              }
            })

            .catch((error) => {
              console.error(error);
              this.$refs.toast.add({
                severity: "error",
                summary: "取消失敗!",
                life: 3000,
              });
            });
        },
      };
    },
    reviewAgainBtn() {
      this.displayModal = true;
    },
    reviewReturnBtn() {
      let param = {
        id: this.list.id,
        node_id: this.reviewToNodeId,
        remark: this.reviewRemark,
      };
      axios
        .post(this.apiURL + "/review/again", param)
        .then((response) => {
          if (response.data) {
            this.getListsRow(this.listIndex);
            this.displayModal = false;
            this.$refs.toast.add({
              severity: "success",
              summary: "指定再審設定成功",
              life: 3000,
            });
            this.action = 0;
            this.getLists();
          } else {
            this.$refs.toast.add({
              severity: "error",
              summary: "指定再審設定失敗",
              life: 3000,
            });
          }
        })
        .catch((error) => {
          console.error(error);
          this.$refs.toast.add({
            severity: "error",
            summary: "再審失敗!請重新整理",
            life: 3000,
          });
        });
    },
    closeCaseBtn() {
      this.closeCaseAPI();
      //結案成功後會有彈窗倒至紀錄(沒做)
    },
    closeCaseAPI() {
      axios
        .post(this.apiURL + "/check/close", { id: this.list.id })
        .then((response) => {
          if (response.data) {
            this.action = 0;
            this.dataLists.splice(this.listIndex, 1);
            this.$refs.toast.add({
              severity: "success",
              summary: "操作成功",
              life: 3000,
            });
          } else {
            this.$refs.toast.add({
              severity: "error",
              summary: "操作失敗",
              life: 3000,
            });
          }
        })
        .catch((error) => {
          console.error(error);
          this.$refs.toast.add({
            severity: "error",
            summary: "結案失敗!",
            life: 3000,
          });
        });
    },
    openList(col) {
      this.menuForm = col.list;
      this.displayModal1 = true;
    },
    getNumberOfCol() {
      return window.innerWidth<640 ? 3 : 4
    }
  },
  computed: {
    value_forms() {
      return this.list.forms.map((form) => {
        return form.columns;
      });
    },
  },
};
</script>
