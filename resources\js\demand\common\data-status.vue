<template>
    <div 
        v-if="status==1" 
        class="top-0 w-full bg-white rounded-xl shadow border border-white flex flex-col mb-5"
        style="padding-top: 33px; padding-bottom: 33px; padding-left: 24px; padding-right: 24px;"
    > 
        <div class="relative flex justify-between">
            <div class="bg-load"></div>
            <div v-if="input" class="w-7 pr-4 bg-white z-10"></div>
            <div v-for="i in col" class="flex flex-grow z-10">
                <div
                    class="h-4 bg-transparent"
                    :class="`w-1/2`"
                >
                </div>
                <div class="flex-grow bg-white "></div>
            </div>
            <div class="z-10 w-16 h-4 py-1.5 sm:text-sm text-xs text-center my-auto"></div>
        </div>
    </div>
    <div v-else-if="data && !data.length" style="height: calc(100vh - 23rem)" class="flex items-center">
        <div class="mx-auto">
            <template v-if="data == undefined">
                <p class="text-center">獲取失敗</p>    
            </template>
            <template v-else>
                <img :src="changeState(0)" alt="" />
                <p class="text-center">{{ changeState(1) }}</p>
            </template>
        </div>
    </div>
</template>
<script>
export default {
    // status: 0: not search yet; 1: searching; 2: searched;
    props: ["data", "status", "col", "input"],
    data() {
        return {
            imgUrl: "",
            wording: "",
        };
    },
    methods: {
        changeState(type) {
            switch (this.status) {
                case 0:
                    return type
                        ? (this.wording = "選擇您要查詢的日期")
                        : (this.imgUrl =
                            new URL("@images/page_state/search.png", import.meta.url).href);
                case 2:
                    return type
                        ? (this.wording = "無任何查詢結果，請再次查詢")
                        : (this.imgUrl =
                            new URL("@images/page_state/empty.png", import.meta.url).href);
                case undefined:
                    return type
                        ? (this.wording = "目前暫無需求申請")
                        : (this.imgUrl =
                            new URL("@images/page_state/empty.png", import.meta.url).href);
            }
        },
    },
};
</script>

<style>
@keyframes loadingAnimation {
    from {
        background-position: -100vw 0px;
    }
    to {
        background-position: 100vw 0px;
    }
}


.bg-load {
    background-size: 100vw 100%;
    background-image: linear-gradient(120deg, #E8E8E8 20%, #F3F3F3 28%, #E8E8E8 43%);
    animation-timing-function: linear;
    animation-iteration-count: infinite;
    animation-duration: 2.5s;
    animation-name: loadingAnimation;
    animation-fill-mode: forwards;
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
}
</style>