<template>
  <ul
    class="absolute top-1/2 left-1/2 border border-gray-200 rounded-lg w-32 text-xs bg-white select-none"
    :style="{
      top: contextmenuPosition.y,
      left: contextmenuPosition.x,
      zIndex: 9999,
    }"
  >
    <li
      class="border-b last:border-b-0 border-gray-200 hover:bg-blue-100 transition first:rounded-t-lg last:rounded-b-lg"
      v-for="item in menu"
      :key="item.name"
    >
      <button
        :disabled="!item.hasPermission"
        class="flex items-center justify-start w-full p-2"
        :class="
          item.hasPermission
            ? 'text-explorerPrimary'
            : 'text-gray-300 cursor-not-allowed'
        "
        @click="item.command"
      >
        <img
          :src="item.hasPermission ? item.iconPath : item.disableIconPath"
          class="w-4"
        />
        <span class="ml-2">{{ item.name }}</span>
      </button>
    </li>
  </ul>
</template>

<script>
export default {
  name: "CustomContextMenu",
  props: {
    contextmenuPosition: { type: Object, required: true },
    menu: { type: Array, required: true },
  },
};
</script>
