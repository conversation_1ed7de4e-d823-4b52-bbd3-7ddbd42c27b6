<template>
    <div>
        <div v-if="action == 0">
            <div class="w-52 grid p-fluid">
                <div class="col-12 md:col-4">
                    <div class="p-inputgroup">
                        <span
                            class="p-inputgroup-addon cursor-pointer"
                            @click="getLists(1, 10)"
                        >
                            <i class="pi pi-search"></i>
                        </span>
                        <InputText
                            class="mr-2 lg:w-44 xl:w-48 mb-2"
                            placeholder="搜尋關鍵字"
                            type="text"
                            v-model="keyword"
                            @keydown.enter="getLists(1, 10)"
                        />
                    </div>
                </div>
            </div>
            <br />
            <div class="thead">
                <input
                    v-model="checkedAll"
                    @change="changeCheckedAll()"
                    class="mr-0 md:mr-4 mr-2"
                    type="checkbox"
                />
                <div class="w-1/6">
                    <span class="text-sm text-gray-400"
                        ><p class="break-all">需求名稱</p>
                    </span>
                </div>

                <div
                    :class="
                        col_index === columns.length - 1
                            ? 'w-16'
                            : column == '部門' || column == '摘要'
                            ? 'w-1/6 hidden sm:block'
                            : 'w-1/6'
                    "
                    v-for="(column, col_index) in columns"
                    :key="col_index"
                >
                    <span class="text-sm text-gray-400"> {{ column }}</span>
                </div>
            </div>
            <div
                v-if="
                    (dataLists.data && !dataLists.data.length) ||
                    dataLists.data == undefined
                "
            >
                <DataStatus
                    :data="dataLists"
                    :status="status"
                    :col="getNumberOfCol()"
                    :input="true"
                />
            </div>
            <!-- list -->
            <div
                v-else
                v-for="(list, listIndex) in dataLists.data"
                :key="list.id"
            >
                <DemandRequestList
                    :data="list"
                    :layer="'outside'"
                    :signOff="1"
                    @click="getListsRow(list.id, listIndex)"
                    :checked="checkedAll"
                    :value="checked"
                    v-model="checked"
                />
                <!-- list end  -->
            </div>
            <Paginator v-if="status != 1" :data="dataLists" @page="getLists" />
            <Sidebar
                :visible.sync="visibleBottom"
                :baseZIndex="1000"
                :modal="false"
                :showCloseIcon="false"
                position="bottom"
                class="h-28"
            >
                <div class="flex justify-end w-full py-1">
                    <p class="text-sm my-auto">已選取{{ checked.length }}個</p>
                    <p
                        @click="
                            checked = [];
                            visibleBottom = false;
                        "
                        class="mx-12 text-sm text-blue-600 underline my-auto cursor-pointer"
                    >
                        取消選取
                    </p>
                    <Button @click="allAgreeModal()" class="px-7 h-9">
                        全部同意
                    </Button>
                </div>
            </Sidebar>
            <ConfirmDialog ref="confirmDialog" />
        </div>
        <!-- inner -->
        <div v-if="action == 1">
            <DemandRequestList
                :key="listsRow.id"
                :data="listsRow"
                :layer="'inner'"
                :sign="1"
                :signOff="1"
                :editCustomForm="true"
                :mode="'signing'"
                @back="(val) => (action = val)"
                @counterRoles="(val) => (counterRoles = val)"
                @files="(val) => (files = val)"
                @remark="(val) => (remark = val)"
                @invalid_remark="(val) => (invalid_remark = val)"
                @openList="openList"
            />
            <div
                class="bg-white w-full shadow rounded-b-xl p-6 mx-auto"
                style="max-width: 1624px"
            >
                <label class="text-sm text-gray-400">回覆</label>
                <br />
                <InputText
                    class="px-4 py-3 rounded-md shadow form-content w-full"
                    :class="{ 'p-invalid': invalid_remark }"
                    @input="invalid_remark = false"
                    v-model="remark"
                    placeholder="輸入您的回覆"
                    type="text"
                />
                <br />
                <div v-if="listsRow.document" class="mt-4">
                    <Button
                        @click="$refs.fileInput.click()"
                        label="上傳附件"
                        class="p-button-outlined p-button-secondary w-28"
                    />
                    <input
                        type="file"
                        class="hidden"
                        ref="fileInput"
                        accept=".jpg,.pdf,.jpeg,.heif,.hevc,.png,.HEIF,.HEVC"
                        multiple="multiple"
                        @change="uploadFile($event, files)"
                    />
                    <div class="overflow-auto block lg:inline-block">
                        <div
                            v-for="(file, index) in files"
                            :key="index"
                            class="inline-block"
                        >
                            <span class="flex mx-2">
                                <div class="w-20 truncate">
                                    <a
                                        :href="file.URL"
                                        :title="file.name"
                                        target="_blank"
                                        >{{ file.name }}</a
                                    >
                                </div>
                                <span
                                    @click="deleteTemp(file.base_name, index)"
                                    title="刪除附件"
                                >
                                    <i
                                        class="fas fa-times-circle cursor-pointer"
                                    ></i>
                                </span>
                            </span>
                        </div>
                    </div>
                    <p class="text-xs text-gray-400 mt-3">
                        檔案大小10MB以內，JPG、PDF檔
                    </p>
                </div>
                <div class="flex flex-wrap justify-between">
                    <div class="mt-4">
                        <Button
                            @click="openCounterModel()"
                            v-if="listsRow.countersigned"
                            class="p-button-outlined p-button-secondary"
                        >
                            會簽
                        </Button>
                        <Dialog
                            :visible.sync="displayModal"
                            :dismissableMask="true"
                            :closable="false"
                            :modal="true"
                            :containerStyle="{
                                maxWidth: '450px',
                                width: '100%',
                            }"
                            class="dialogHeight"
                        >
                            <template #header>
                                <div class="w-full flex justify-between">
                                    <h3 class="text-2xl font-semibold">會簽</h3>
                                    <Button
                                        @click="addCounterNode()"
                                        class="p-button-text p-button-plain h-10 underline p-0"
                                    >
                                        新增審核人
                                    </Button>
                                </div>
                            </template>
                            <div class="my-auto font-bold text-gray-400">
                                <div class="pb-8">
                                    <div
                                        v-for="(
                                            counterRole, counterIndex
                                        ) in counterRoles"
                                        :key="counterRole.id"
                                    >
                                        <span
                                            >審核人{{ counterIndex + 1 }}</span
                                        >
                                        <br />
                                        <Dropdown
                                            class="xl:w-44 2xl:w-48 rounded-md shadow"
                                            placeholder="選擇審核人"
                                            :options="getEmployees"
                                            :filter="true"
                                            optionLabel="name"
                                            optionValue="id"
                                            data-key="id"
                                            v-model="counterRoles[counterIndex]"
                                        >
                                            <template #option="slotProps">
                                                <div>
                                                    <span>{{
                                                        slotProps.option
                                                            .org_name
                                                    }}</span>
                                                    <span>{{
                                                        slotProps.option.name
                                                    }}</span>
                                                </div>
                                            </template>
                                        </Dropdown>
                                        <span
                                            class="pl-2"
                                            v-if="counterIndex >= 1"
                                            @click="
                                                deleteCounterNode(counterIndex)
                                            "
                                        >
                                            <i class="far fa-trash-alt"></i>
                                        </span>
                                        <br />
                                        <br />
                                    </div>
                                </div>

                                <div class="pb-8">
                                    <span>會簽事由</span>
                                    <br />
                                    <InputText
                                        class="w-full"
                                        placeholder="輸入會簽事由"
                                        v-model="remark"
                                    ></InputText>
                                </div>
                            </div>

                            <template #footer>
                                <div class="flex justify-end">
                                    <Button
                                        @click="displayModal = false"
                                        label="取消"
                                        class="p-button-outlined p-button-secondary w-28 mr-5"
                                    />
                                    <Button
                                        @click="submitCounterBtn()"
                                        label="會簽"
                                        class="w-28"
                                        :disabled="isCounterBtnDisabled"
                                    />
                                </div>
                            </template>
                        </Dialog>
                    </div>
                    <div
                        class="mt-4 flex flex-wrap gap-5 justify-between md:justify-end"
                    >
                        <Dialog
                            :visible.sync="displayModal1"
                            :dismissableMask="true"
                            :closable="false"
                            :modal="true"
                            header="退回"
                            :containerStyle="{ width: '30vw' }"
                            class="dialogHeight"
                        >
                            <div class="my-auto font-bold text-gray-400">
                                <div class="pb-8">
                                    <span>退回節點</span>
                                    <br />
                                    <Dropdown
                                        class="xl:w-44 2xl:w-48 rounded-md shadow"
                                        :class="{
                                            'p-invalid': back.nodeInvalid,
                                        }"
                                        :options="back.empDropdown"
                                        :filter="true"
                                        optionLabel="name"
                                        optionValue="id"
                                        data-key="id"
                                        v-model="back.roleId"
                                        placeholder="選擇節點"
                                        @change="back.nodeInvalid = false"
                                    >
                                        <template #option="slotProps">
                                            <div>
                                                <span>{{
                                                    slotProps.option.self_name
                                                }}</span>
                                                <span>{{
                                                    slotProps.option.name
                                                }}</span>
                                            </div>
                                        </template>
                                    </Dropdown>
                                    <br />
                                    <br />
                                </div>

                                <div class="pb-8">
                                    <span>退回事由</span>
                                    <br />
                                    <InputText
                                        class="w-full"
                                        :class="{
                                            'p-invalid': back.remarkInvalid,
                                        }"
                                        @input="back.remarkInvalid = false"
                                        placeholder="輸入退回事由"
                                        v-model="remark"
                                    ></InputText>
                                </div>
                            </div>

                            <template #footer>
                                <div class="flex justify-end">
                                    <Button
                                        @click="
                                            displayModal1 = false;
                                            back = {
                                                empDropdown: [],
                                                nodeInvalid: false,
                                                remarkInvalid: false,
                                                remark: '',
                                                roleId: null,
                                            };
                                        "
                                        label="取消"
                                        class="p-button-outlined p-button-secondary w-28 mr-5"
                                    />
                                    <Button
                                        @click="
                                            displayModal1 = false;
                                            goBack(6);
                                        "
                                        label="退回"
                                        class="w-28"
                                        :disabled="btnDisabled == 1"
                                    />
                                </div>
                            </template>
                        </Dialog>
                        <Dialog
                            :visible.sync="displayModalTurnDown"
                            :dismissableMask="true"
                            :closable="false"
                            :modal="true"
                            header="駁回"
                            :containerStyle="{ width: '30vw' }"
                            class="dialogHeightTurnDown"
                        >
                            <div>確定取消本單?</div>
                            <template #footer>
                                <div class="flex justify-end">
                                    <Button
                                        @click="displayModalTurnDown = false"
                                        label="取消"
                                        class="p-button-outlined p-button-secondary w-28 mr-5"
                                    />
                                    <Button
                                        @click="
                                            displayModalTurnDown = false;
                                            submit(3);
                                        "
                                        label="確定"
                                        class="w-28"
                                    />
                                </div>
                            </template>
                        </Dialog>
                        <Button
                            v-if="listsRow.child"
                            @click="action = 2"
                            class="p-button-text p-button-plain h-10 w-28 underline inline-block"
                        >
                            申請子單
                        </Button>
                        <Button
                            :disabled="btnDisabled == 1 || isBackSubmitting"
                            v-if="listsRow.canFallback == 2"
                            @click="
                                displayModal1 = true;
                                backDropdown();
                            "
                            class="p-button-outlined p-button-secondary w-28 flex justify-center"
                        >
                            <i
                                v-if="isBackSubmitting"
                                class="pi pi-spin pi-spinner mr-2"
                            ></i>
                            <span>退回</span>
                        </Button>
                        <Button
                            :disabled="btnDisabled == 1 || isRejectSubmitting"
                            v-if="
                                listsRow.canFallback == 1 ||
                                listsRow.canFallback == 2
                            "
                            @click="displayModalTurnDown = true"
                            class="p-button-outlined p-button-secondary w-28 flex justify-center"
                        >
                            <template>
                                <i
                                    v-if="isRejectSubmitting"
                                    class="pi pi-spin pi-spinner mr-2"
                                ></i>
                                <span>駁回</span>
                            </template>
                        </Button>
                        <Button
                            :disabled="btnDisabled == 1 || isAgreeSubmitting"
                            @click="submit(2)"
                            class="w-28 flex justify-center"
                        >
                            <template>
                                <i
                                    v-if="isAgreeSubmitting"
                                    class="pi pi-spin pi-spinner mr-2"
                                ></i>
                                <span>同意</span>
                            </template>
                        </Button>
                    </div>
                </div>
            </div>
            <Dialog
                :visible.sync="displayModal2"
                :closable="false"
                :containerStyle="{ width: '30vw' }"
                :modal="true"
            >
                <div class="pb-8">
                    <img
                        class="mx-auto"
                        src="@images/popup_state/warning.png"
                    />
                    <br />
                    <p class="text-center">
                        {{
                            displayModal2 && "value" in modal2Info
                                ? modal2Info.value.prompt
                                : ""
                        }}
                    </p>
                </div>
                <template #footer>
                    <div class="flex justify-center">
                        <Button
                            @click="
                                displayModal2 = false;
                                modal2Info = null;
                            "
                            label="我知道了"
                        />
                    </div>
                </template>
            </Dialog>
            <ConfirmDialog ref="confirmDialog" />
        </div>
        <div v-if="action == 2">
            <Submit :parent_no="listsRow.no" />
        </div>
        <Toast ref="toast" position="top-center" />
    </div>
</template>
<script>
import axios from "axios";
import InputText from "primevue/inputtext";
import Button from "primevue/button";
import ConfirmDialog from "primevue/confirmdialog";
import DemandRequestList from "@/demand/common/demand-request-list";
import Submit from "@/demand/submit/submit.vue";
import Toast from "primevue/toast";
import Dialog from "primevue/dialog";
import Dropdown from "primevue/dropdown";
import Paginator from "@/demand/common/Paginator";
import DataStatus from "@/demand/common/data-status";
import Sidebar from "primevue/sidebar";
import SplitButton from "primevue/splitbutton";
// import  listStatusMixin  from "@/mixins/listStatusMixin";

export default {
    components: {
        InputText,
        Button,
        ConfirmDialog,
        DemandRequestList,
        Toast,
        Submit,
        Dialog,
        Dropdown,
        Paginator,
        DataStatus,
        Sidebar,
        SplitButton,
    },
    data() {
        return {
            action: 0,
            keyword: null,
            apiURL: "/api/demand/reviewed/lists",
            columns: ["單號", "申請日期", "申請人", "部門", "摘要", "申請狀態"],
            dataLists: [],
            checkedAll: false,
            checked: [],
            listIndex: 0,
            //內頁
            remark: "", //輸入您的回覆
            listsRow: {},
            //會簽
            displayModal: false,
            displayModal1: false,
            displayModal2: false,
            displayModalTurnDown: false,
            modal2Info: null,
            counterRemark: "",
            getEmployees: [],
            counterRoles: [0], //被會簽的json簽核節點們
            counterId: 0,
            files: [],
            visibleBottom: false,
            sign_forms: [],
            invalid_remark: false,
            items: [
                {
                    label: "退回",
                    command: () => {
                        this.displayModal1 = true;
                        this.backDropdown();
                    },
                },
            ],
            back: {
                empDropdown: [],
                nodeInvalid: false,
                remarkInvalid: false,
                // remark: "",
                roleId: null,
            },
            btnDisabled: 0,
            isCounterBtnDisabled: false,
            status: 0,
            isAgreeSubmitting: false,
            isBackSubmitting: false,
            isRejectSubmitting: false,
        };
    },
    //   mixins: [listStatusMixin],
    mounted() {
        this.getLists(1);
    },
    watch: {
        action: function (newValue) {
            if (newValue == 0) {
                this.btnDisabled = 0;
            }
        },
    },
    methods: {
        getLists(page, per) {
            this.status = 1;
            axios
                .get(this.apiURL, {
                    params: {
                        page: page,
                        per: per,
                        keyword: this.keyword,
                    },
                })
                .then((response) => {
                    this.status = 2;
                    this.dataLists = response.data ?? [];
                })
                .catch((error) => {
                    this.status = 2;
                    console.error(error);
                    this.dataLists = undefined;
                });
        },
        getListsRow(id, index) {
            axios
                .get(this.apiURL + "/detail", {
                    params: {
                        id: id,
                    },
                })
                .then((response) => {
                    if (response.data) {
                        // 確保乾淨所以清空
                        this.listsRow = {};
                        var detail = response.data;
                        detail.forms.map((form) => {
                            return form.columns.map((col) => {
                                if (col.type == "date" || col.type == "time") {
                                    col.value = col.value
                                        ? new Date(col.value)
                                        : null;
                                }
                            });
                        });

                        this.listsRow = Object.assign(
                            detail,
                            this.dataLists.data[index]
                        );
                        this.listIndex = index;
                        //   this.listsRow = this.dataLists.data[index];
                        this.action = 1;
                    }
                })
                .catch((error) => {
                    this.listsRow = {};
                    console.error(error);
                });
        },
        changeCheckedAll() {
            this.checked = [];
            if (this.checkedAll) {
                for (var i in this.dataLists.data) {
                    this.checked.push(this.dataLists.data[i].id);
                }
                this.visibleBottom = true;
            } else {
                this.visibleBottom = false;
            }
        },
        allAgreeModal() {
            this.$refs.confirmDialog.visible = true;
            this.$refs.confirmDialog.confirmation = {
                message: "您是否同意全部申請?",
                acceptLabel: "同意",
                rejectLabel: "取消",
                accept: () => {
                    this.allAgree();
                },
                reject: () => {
                    this.$refs.confirmDialog.visible = false;
                },
            };
        },
        allAgree() {
            if (!this.checkCanAllAgree().status) {
                this.$refs.toast.add({
                    severity: "error",
                    summary: this.checkCanAllAgree().message,
                    life: 5000,
                });
                return;
            }

            axios
                .post(this.apiURL + "/all-agree", {
                    ids: this.checked,
                })
                .then((response) => {
                    if (response.data) {
                        this.$refs.toast.add({
                            severity: "success",
                            summary: "審核成功",
                            life: 3000,
                        });
                        this.getLists(1);
                        this.visibleBottom = false;
                    } else {
                        this.$refs.toast.add({
                            severity: "error",
                            summary: "審核失敗",
                            life: 3000,
                        });
                    }
                });
        },
        checkCanAllAgree() {
            let canAllAgree = true;
            let checkedIds = new Set(this.checked);
            let unableToAgreeNo = "";

            for (let i = 0; i < this.dataLists.data.length; i++) {
                const list = this.dataLists.data[i];
                if (!checkedIds.has(list.id)) {
                    continue;
                }

                canAllAgree = !list.sign_roles.some((role) => {
                    if (role.must && role.fill_column_ids.length > 0) {
                        unableToAgreeNo = list.no;
                        return true;
                    }
                    return false;
                });

                if (!canAllAgree) {
                    break;
                }
            }

            return {
                status: canAllAgree,
                message:
                    "單號" +
                    unableToAgreeNo +
                    "　　　　　　　　" +
                    "需要審核人填寫欄位，無法全部同意",
            };
        },
        submit(status) {
            if (status === 3) {
                this.isRejectSubmitting = true;
            } else {
                this.isAgreeSubmitting = true;
            }
            this.btnDisabled = 1;
            var regu = "^[ ]+$";
            var re = new RegExp(regu);
            let error = false;
            let missingInput = false;
            let missingList = false;
            if (status == 3 && (this.remark == "" || re.test(this.remark))) {
                this.invalid_remark = true;
                this.$refs.toast.add({
                    severity: "error",
                    summary: "駁回需填寫事由",
                    life: 3000,
                });
                error = true;
                this.btnDisabled = 0;
            }

            //審核人填寫欄位判斷
            let sign_forms = [];
            if (status == 2) {
                let signer =
                    this.listsRow.sign_roles[
                        this.listsRow.sign_roles.length - 1
                    ];
                let fill_columns = this.listsRow.forms[0].columns.filter(
                    (form) => {
                        if ("fill_column_ids" in signer) {
                            return signer?.fill_column_ids.includes(form.id);
                        }
                    }
                );

                this.listsRow.forms.forEach((form, form_index) => {
                    form.columns.forEach((col) => {
                        // 檢查審核人必填欄位是否完整
                        if (fill_columns.length !== 0) {
                            fill_columns.forEach((f_c) => {
                                if (col.id == f_c.id) {
                                    if (col.must && col.audit_must) {
                                        if (
                                            col.value === null ||
                                            col.value === "" ||
                                            col.value?.length == 0 ||
                                            !("value" in col)
                                        ) {
                                            error = true;
                                            missingInput = true;
                                            this.btnDisabled = 0;
                                            this.$set(
                                                this.listsRow,
                                                "invalidInput",
                                                true
                                            );
                                        }
                                    }
                                }
                            });
                        }

                        // 檢查自訂表單審核人有無填寫完整
                        if (col.type == "customList") {
                            if ("fill_column_list_ids" in signer) {
                                col.form_setting.forEach(
                                    (setting, set_index) => {
                                        if (
                                            !signer.fill_column_list_ids.includes(
                                                setting.id
                                            )
                                        )
                                            return;
                                        if (!setting.is_must) return;

                                        col.custom_list.forEach(
                                            (list, index) => {
                                                if (
                                                    col.custom_list[index][
                                                        set_index
                                                    ] == null ||
                                                    col.custom_list[index][
                                                        set_index
                                                    ] === ""
                                                ) {
                                                    setting.invalid = true;
                                                    error = true;
                                                    missingList = true;
                                                    this.btnDisabled = 0;
                                                }
                                            }
                                        );
                                    }
                                );
                            }
                        }

                        // 審核人有填寫的話，將資料塞入要回傳的form裡面
                        if ("update" in col || col.type == "customList") {
                            if (
                                sign_forms.find(
                                    (sign_form) => sign_form.index == form_index
                                )
                            ) {
                                sign_forms
                                    .find(
                                        (sign_form) =>
                                            sign_form.index == form_index
                                    )
                                    .columns.push(col);
                            } else {
                                const sign_form = {
                                    index: form_index,
                                    columns: [col],
                                };
                                sign_forms.push(sign_form);
                            }
                        }
                    });
                });
            }

            if (missingInput) {
                this.$refs.toast.add({
                    severity: "error",
                    summary: "*字號為必填項目!",
                    life: 3000,
                });
                return;
            }
            if (missingList) {
                this.$refs.toast.add({
                    severity: "error",
                    summary: "*表單欄位為必填項目!",
                    life: 3000,
                });
                return;
            }

            // 過濾自訂表單
            this.filterOutUnwantedRowFromCustomLists();

            // 清除不必要的欄位
            this.sign_forms = JSON.parse(JSON.stringify(sign_forms));
            this.sign_forms.forEach((f) => {
                f.columns.forEach((col) => {
                    const col_value = [
                        "id",
                        "value",
                        "type",
                        col.type == "customList" ? "custom_list" : null,
                    ];
                    Object.keys(col).forEach((key) => {
                        if (!col_value.includes(key)) {
                            delete col[key];
                        }
                    });
                });
            });

            if (error == false) {
                // 送出同意or駁回
                let param = {
                    id: this.listsRow.id,
                    forms: this.sign_forms,
                    status: status,
                    remark: this.remark == "" ? "無" : this.remark,
                    files: this.files, //附件檔案
                };
                axios
                    .post(this.apiURL, param)
                    .then((response) => {
                        if (response.data) {
                            this.action = 0;
                            if (status == 3) {
                                this.$refs.toast.add({
                                    severity: "success",
                                    summary: "駁回成功",
                                    life: 3000,
                                });
                            } else {
                                this.$refs.toast.add({
                                    severity: "success",
                                    summary: "審核成功",
                                    life: 3000,
                                });
                            }
                            this.dataLists.data.splice(this.listIndex, 1);
                            this.dataLists.total -= 1;
                        } else {
                            this.$refs.toast.add({
                                severity: "error",
                                summary: "審核失敗",
                                life: 3000,
                            });
                        }
                    })
                    .catch((error) => {
                        console.error(error);
                        this.$refs.toast.add({
                            severity: "error",
                            summary:
                                "審核失敗，伺服器異常，請重新嘗試或洽詢管理員",
                            life: 3000,
                        });
                    })
                    .finally(() => {
                        this.counterRemark = "";
                        this.remark = "";
                        this.files = [];
                        this.btnDisabled = 0;
                        this.sign_forms = [];
                        this.isAgreeSubmitting = false;
                        this.isRejectSubmitting = false;
                    });
            }
        },
        getEmployeesDropDown() {
            axios
                .get("/api/demand/employees")
                .then((response) => {
                    this.getEmployees = response.data ?? [];
                })
                .catch((error) => {
                    console.error(error);
                });
        },
        openCounterModel() {
            this.displayModal = true;
            this.getEmployeesDropDown();
        },
        addCounterNode() {
            console.log("addCounterNode");
            this.counterRoles.push(0);
        },
        deleteCounterNode(index) {
            console.log("deleteCounterNode");
            this.counterRoles.splice(index, 1);
        },
        submitCounterBtn() {
            let roles = [];
            let employeesList = this.getEmployees;
            this.counterRoles.forEach(function (id, index) {
                let employee = employeesList.filter((emp) => emp.id == id);
                if (employee.length > 0) {
                    let role = {
                        role_id: employee[0].id,
                        self_name: employee[0].org_name,
                        rank: employee[0].org_id,
                        to_counter: 1,
                        apply_status: 0,
                        child: false,
                        document: false,
                        countersigned: false,
                    };
                    roles.push(role);
                }
            });
            if (roles.length >= 1) {
                this.callCounterAPI(roles);
            }
        },
        callCounterAPI(roles) {
            this.isCounterBtnDisabled = true;
            let param = {
                id: this.listsRow.id,
                counterRoles: roles,
                counterRemark: this.remark == "" ? "無" : this.remark,
            };

            axios
                .post(this.apiURL + "/countersigned", param)
                .then((response) => {
                    if (response.data) {
                        //  console.log("會簽成功");
                        this.action = 0;
                        //移除list index
                        this.dataLists.data.splice(this.listIndex, 1);
                        this.$refs.toast.add({
                            severity: "success",
                            summary: "審核成功",
                            life: 3000,
                        });
                        this.displayModal = false;
                    } else {
                        this.$refs.toast.add({
                            severity: "error",
                            summary: "審核失敗",
                            life: 3000,
                        });
                    }
                })
                .finally(() => {
                    this.counterRoles = [0];
                    this.counterRemark = "";
                    this.remark = "";
                    this.isCounterBtnDisabled = false;
                });
        },
        backDropdown() {
            const set = new Set();
            let sign_roles = this.listsRow.sign_roles;
            let this_user_node =
                this.listsRow.sign_roles[sign_roles.length - 1];
            //尋找經辦第一關的位置
            let manager_first_role_index = sign_roles.findIndex(
                (role) => role.type == 1
            );
            //取得經辦/審核第一關的人
            let first_roles = sign_roles.filter((role) => {
                return (
                    role.type == this_user_node.type &&
                    role.role_id ==
                        sign_roles[
                            this_user_node.type == 1
                                ? manager_first_role_index
                                : 0
                        ].role_id &&
                    role.apply_status !== 5
                );
            });
            //尋找第一關id最後的節點位置
            let first_role_index =
                Math.max(...first_roles.map((role) => role.id)) - 1;
            //取得經辦/審核到最後的區間關卡
            this.back.empDropdown = sign_roles.slice(
                first_role_index,
                sign_roles.length - 1
            );
            this.back.empDropdown = this.back.empDropdown.filter(
                (role) =>
                    role.apply_status !== 6 &&
                    role.role_id !== this_user_node.role_id
            );
            //帶入前一關節點id
            this.back.roleId =
                this.back.empDropdown.length == 0
                    ? null
                    : this.back.empDropdown[this.back.empDropdown.length - 1]
                          .id;
            this.back.empDropdown.unshift({
                id: 0,
                self_name: "申請人",
                name: this.listsRow.createdBy,
            });
        },
        goBack(status) {
            this.isBackSubmitting = true;
            this.btnDisabled = 1;
            if (this.remark == "" || this.back.roleId == null) {
                this.remark == ""
                    ? (this.back.remarkInvalid = true)
                    : (this.back.remarkInvalid = false);
                this.back.roleId == null
                    ? (this.back.nodeInvalid = true)
                    : (this.back.nodeInvalid = false);

                this.$refs.toast.add({
                    severity: "warn",
                    summary: "請填入節點與事由",
                    life: 3000,
                });
                this.btnDisabled = 0;
                this.isBackSubmitting = false;
                return false;
            }
            let param = {
                id: this.listsRow.id,
                status: status,
                role_id: this.back.roleId,
                remark: this.remark == "" ? "無" : this.remark,
            };
            axios
                .post(this.apiURL + "/back", param)
                .then((response) => {
                    if (response.data) {
                        this.action = 0;
                        this.$refs.toast.add({
                            severity: "success",
                            summary: "退回成功",
                            life: 3000,
                        });
                        this.dataLists.data.splice(this.listIndex, 1);
                        this.dataLists.total -= 1;
                    } else {
                        this.$refs.toast.add({
                            severity: "error",
                            summary: "退回失敗",
                            life: 3000,
                        });
                    }
                })
                .catch((error) => {
                    this.$refs.toast.add({
                        severity: "error",
                        summary: "退回失敗!",
                        life: 3000,
                    });
                    console.error(error);
                })
                .finally(() => {
                    this.displayModal1 = false;
                    this.btnDisabled = 0;
                    this.remark = "";
                    this.isBackSubmitting = false;
                });
        },
        uploadFile(e, currentFiles) {
            const files = e.target.files;
            let accumulatedFilesSize = 0;
            const cumulativeFilesSize = currentFiles.reduce(
                (acc, cur) => acc + cur.size,
                0
            );
            const validFileName = [
                "jpg",
                "jpeg",
                "png",
                "pdf",
                "heif",
                "xlx",
                "xlsx",
                "JPG",
                "JPEG",
                "PNG",
                "PDF",
                "HEIF",
                "XLX",
                "XLSX",
            ];
            const form = new FormData();

            // 沒選到檔案時取消
            if (!files.length) {
                return;
            }

            for (var i = 0; i < files.length; i++) {
                accumulatedFilesSize += files[i].size;
                // 檔案大於10MB時取消
                if (cumulativeFilesSize + accumulatedFilesSize > 10485760) {
                    this.$refs.toast.add({
                        severity: "warn",
                        summary: "檔案大於10MB",
                        life: 3000,
                    });
                    e.target.value = "";
                    return;
                }

                // 不適用的檔案類型
                if (
                    !validFileName.includes(
                        files[i].name.split(".").slice(-1)[0]
                    )
                ) {
                    this.$refs.toast.add({
                        severity: "warn",
                        summary: "檔案類型錯誤",
                        life: 3000,
                    });
                    e.target.value = "";
                    return;
                }

                // 選到得檔案與已上傳檔案同名
                if (currentFiles.some((el) => el.name == files[i].name)) {
                    this.$refs.toast.add({
                        severity: "warn",
                        summary: "同名檔案已上傳",
                        life: 3000,
                    });
                    e.target.value = "";
                    return;
                }

                form.append("files" + i, files[i]);
                form.append("fileName" + i, files[i].name);
            }

            const options = {
                method: "POST",
                headers: { "content-type": "multipart/form-data" },
                data: form,
                url: "/api/demand/upload/temp",
            };

            axios(options)
                .then((response) => {
                    if (response !== "") {
                        for (let i = 0; i < response.data.length; i++) {
                            currentFiles.push(response.data[i]);
                        }
                        this.$refs.toast.add({
                            severity: "success",
                            summary: "上傳成功",
                            life: 3000,
                        });
                    } else {
                        this.$refs.toast.add({
                            severity: "error",
                            summary: "上傳失敗",
                            life: 3000,
                        });
                    }
                })
                .catch((error) => {
                    console.error(error);
                    this.$refs.toast.add({
                        severity: "error",
                        summary: "上傳失敗!",
                        life: 3000,
                    });
                });
        },
        deleteTemp(baseName, index) {
            axios
                .post("/api/demand/delete/temp", { base_name: baseName })
                .then((response) => {
                    this.files.splice(index, 1);
                    this.$refs.toast.add({
                        severity: "success",
                        summary: "刪除成功",
                        life: 3000,
                    });
                    this.$refs.fileInput.value = "";
                })
                .catch((error) => {
                    console.error(error);
                    this.$refs.toast.add({
                        severity: "error",
                        summary: "刪除失敗",
                        life: 3000,
                    });
                });
        },
        filterOutUnwantedRowFromCustomLists() {
            this.listsRow.forms = this.listsRow.forms.map((form) => {
                form.columns = form.columns.map((col) => {
                    if (
                        col.type == "customList" &&
                        col.form_setting?.some((el) => el.filterCriteria)
                    ) {
                        // 如果 col 的所有 mode 2 的 id 都已出現在 sign_roles 上了, 代表 mode 2 的都填完了 => 開濾
                        if (
                            col.form_setting.every((setting) => {
                                if (
                                    setting.mode == 2 &&
                                    setting.filterCriteria
                                ) {
                                    return this.listsRow.sign_roles
                                        .map(
                                            (role) => role.fill_column_list_ids
                                        )
                                        .flat()
                                        .includes(setting.id);
                                }
                                return true;
                            })
                        ) {
                            col.custom_list = col.custom_list.filter((row) => {
                                return row.some((el, index) => {
                                    if (
                                        col.form_setting[index].filterCriteria
                                    ) {
                                        return el;
                                    }
                                });
                            });
                            col.update = 1;
                        }
                    }
                    return col;
                });
                return form;
            });
        },
        openList(item) {
            this.modal2Info = item;
            if (
                "prompt" in item.value &&
                item.value.prompt !== undefined &&
                item.value.prompt !== "" &&
                item.value.prompt !== null
            ) {
                this.displayModal2 = true;
            }
        },
        getNumberOfCol() {
            return window.innerWidth < 640 ? 4 : 6;
        },
    },
};
</script>
<style>
.p-sidebar-xs {
    height: 6rem;
}
.p-splitbutton-defaultbutton[data-v-32782628] {
    background-color: transparent !important;
    color: #64748b !important;
    border: 1px solid !important;
}
.p-splitbutton-menubutton[data-v-32782628] {
    background-color: transparent !important;
    color: #64748b !important;
    border: 1px solid !important;
}
.dialogHeight .p-dialog-content {
    height: 28rem;
}
.dialogHeightTurnDown .p-dialog-content {
    height: 4rem;
}
</style>
