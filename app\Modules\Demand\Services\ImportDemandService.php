<?php

namespace App\Modules\Demand\Services;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use App\Modules\Demand\Models\ImportDemand;

class ImportDemandService
{
    public function createOrUpdate(Request $request): int
    {
        $new_id = $request->input('layout_id');
        $old_id = $request->input('old_layout_id') ?? 0;
        $list_import = $request->input('list_import');

        if (is_nan($new_id) || empty($new_id)) return 0;

        $result = ImportDemand::updateOrCreate(
            ['layout_id' =>  $new_id],
            ['list_import' => $list_import]
        );

        $query = 'UPDATE import_demands
                    SET list_import = sub.list
                    FROM (
                        SELECT
                            id,
                            jsonb_agg(
                                CASE
                                    WHEN value->>\'id\' = \'' . $old_id . '\'
                                    THEN jsonb_set(value, \'{id}\', \'' . $new_id . '\')
                                    ELSE value
                                END
                            ) AS list
                        FROM import_demands id,
                             LATERAL jsonb_array_elements(id.list_import) WITH ORDINALITY jsonb_array_elements(value, ordinality)
                        WHERE id.deleted_at IS NULL
                          AND list_import @> \'[{"id": ' . $old_id . '}]\'::jsonb
                        GROUP BY id
                    ) AS sub
                    WHERE import_demands.id = sub.id
                      AND list_import @> \'[{"id": ' . $old_id . '}]\'::jsonb';

        DB::statement($query);

        return $result ? 1 : 0;
    }
}
