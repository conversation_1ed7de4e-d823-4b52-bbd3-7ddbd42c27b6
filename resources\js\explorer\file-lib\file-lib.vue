<template>
  <div
    style="min-height: calc(100vh - 148px)"
    class="w-full h-full select-none flex flex-col"
    @contextmenu.prevent=""
  >
    <Toast ref="toast" position="top-center" :baseZIndex="9999" :closable="false" />

    <ConfirmPopup ref="confirmPopup" style="z-index: 9999"></ConfirmPopup>

    <Confirm :option="confirmOption" @onToggle="toggleConfirm" />

    <div
      v-show="isLoading"
      class="loader fixed z-50 top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2"
    />

    <RuleSettings
      :isShow="isShowRuleSettings"
      :currentRule="currentRule"
      :confirmOption="confirmOption"
      @updateCurrentRule="updateCurrentRule"
      @toggleRuleSettings="toggleRuleSettings"
      @setConfirm="setConfirm"
    />

    <div class="flex flex-col w-full h-full flex-grow">
      <Toolbar
        :currentRule="currentRule"
        :currentFolderData="currentFolderData"
        @toggleRuleSettings="toggleRuleSettings"
        @onSearch="handleSearch"
        @onAdd="handleAddFolder"
        @updateIsLoading="updateIsLoading"
      />

      <FileView
        v-if="currentData"
        ref="fileView"
        :rootId="rootId"
        :layout="layout"
        :currentData="currentData"
        :currentPathIds="currentPathIds"
        :currentPathNames="currentPathNames"
        :prevPathIds="prevPathIds"
        :nextPathIds="nextPathIds"
        :selectedItem="selectedItem"
        :newName="newName"
        :dirTree="dirTree"
        :isSearched="isSearched"
        :isThumbnail="isThumbnail"
        :isLoading="isLoading"
        :currentFolderData="currentFolderData"
        :confirmOption="confirmOption"
        @onSortName="handleSortByName"
        @onSortDate="handleSortByDate"
        @onRename="handleRename"
        @onChangeName="handleChangingName"
        @dataInit="dataInit"
        @updateSelectedItem="updateSelectedItem"
        @updateNewName="updateNewName"
        @setConfirm="setConfirm"
      />
    </div>
  </div>
</template>

<script>
import Toast from "primevue/toast";
import ConfirmPopup from "primevue/confirmpopup";
import Dialog from "primevue/dialog";
import RuleSettings from "./rule-settings.vue";
import Confirm from "./confirm.vue";
import Toolbar from "./toolbar.vue";
import FileView from "./file-view.vue";
import { explorerMixin } from "@/mixins/explorerMixin";
import { confirmMixin } from "@/mixins/confirmMixin";
import {
  getRootData,
  getExplorerData,
  getDirectory,
  renameItem,
  addFolder,
  searchFile,
} from "@/axios/explorerApi.js";

export default {
  name: "FileLib",
  components: {
    Toast,
    ConfirmPopup,
    Dialog,
    RuleSettings,
    Confirm,
    Toolbar,
    FileView,
  },
  mixins: [explorerMixin, confirmMixin],
  data() {
    return {
      layout: "grid",
      selectedItem: null,
      isShowRuleSettings: false,
      isSearched: false,
      isThumbnail: false,
      isFetchingData: false,
      isFetchingDir: false,
      isLoading: false,
      newName: "",
      newFolders: [],
      directory: [],
      dirTree: [],
      rootId: 0,
      currentData: [],
      currentFolderData: {},
      currentPathIds: [],
      prevPathIds: [],
      nextPathIds: [],
      // 建檔規則
      currentRule: null,
    };
  },
  methods: {
    async fetchRootData() {
      try {
        this.isLoading = true;
        const { root, data } = await getRootData();
        this.rootId = root.id;
        this.currentFolderData = root;
        this.currentData = data;
        this.addAttributes();
        this.currentPathIds = [this.rootId];
      } catch (error) {
        this.$refs.toast.add({
          severity: "error",
          summary: error.message,
          life: 3000,
        });
      } finally {
        this.isLoading = false;
      }
    },
    async fetchDataByFolderId(id) {
      if (!id || this.isFetchingData) return;
      try {
        this.isLoading = true;
        this.isFetchingData = true;
        const data = await getExplorerData(id);
        this.currentData = data;
        this.addAttributes();
        this.updateCurrentPathIds(id);
        this.changeDirActive();
      } catch (error) {
        this.$refs.toast.add({
          severity: "error",
          summary: error.message,
          life: 3000,
        });
      } finally {
        this.isLoading = false;
        this.isFetchingData = false;
      }
    },
    async dataInit() {
      if (this.isFetchingData || this.isFetchingDir) return;
      await this.fetchDirectory();
      if (!this.checkFolderPermission(this.rootId)) return;
      await this.fetchDataByFolderId(this.rootId);
      this.nextPathIds.length = 0;
      this.isSearched = false;
    },
    addAttributes() {
      // 給資料加上 iconPath 和 isChangingName 屬性
      this.currentData?.forEach((item) => {
        if (item.type === "folder") {
          item.iconPath = new URL("@images/icon/folder.png", import.meta.url).href;
        } else if (item.type.includes("pdf")) {
          item.iconPath = new URL("@images/icon/pdf.png", import.meta.url).href;
        } else if (item.type.includes("xls")) {
          item.iconPath = new URL("@images/icon/excel.png", import.meta.url).href;
        } else if (item.type.includes("doc")) {
          item.iconPath = new URL("@images/icon/word.png", import.meta.url).href;
        }
        this.$set(item, "isChangingName", false);
      });
    },
    checkFolderPermission(id) {
      if (!id) return;
      // 從目錄獲取目標資料夾的資料
      this.currentFolderData = this.directory?.find(
        (folder) => folder.id === id
      );

      // 判斷使用者是否有權限打開資料夾
      if (!this.currentFolderData?.admin && !this.currentFolderData?.read) {
        this.setConfirm({
          type: "wrong",
          message: "無瀏覽權限，無法開啟",
          imgPath: new URL("@images/popup_state/wrong.png", import.meta.url).href,
          centerBtn: {
            text: "我知道了",
            command: () => (this.confirmOption.isShow = false),
          },
          isShow: true,
        });

        // 無權限，將當前所在資料夾改回來
        this.currentFolderData = this.directory?.find(
          (folder) => folder.id === this.currentPathIds.at(-1)
        );

        return false;
      }
      return true;
    },
    handleSortByName(type) {
      this.currentData?.sort((a, b) => {
        const nameA = a.name.toUpperCase();
        const nameB = b.name.toUpperCase();
        if (type === "desc") {
          return nameB.localeCompare(nameA, "en", { numeric: true });
        }
        return nameA.localeCompare(nameB, "en", { numeric: true });
      });
    },
    handleSortByDate(type) {
      this.currentData?.sort((a, b) => {
        const timeA = new Date(a.timeCreated).getTime();
        const timeB = new Date(b.timeCreated).getTime();
        if (type === "desc") return timeB - timeA;
        return timeA - timeB;
      });
    },
    async fetchDirectory() {
      if (this.isFetchingDir) return;
      try {
        this.isLoading = true;
        this.isFetchingDir = true;
        const data = await getDirectory();
        this.directory = data;
        // 給目錄資料加上 isExpand isActive 屬性
        this.directory?.forEach((item) => {
          this.$set(item, "isExpand", false);
          this.$set(item, "isActive", false);
        });

        // 將目錄資料格式化成樹狀結構
        this.dirTree = this.formatToTree(this.directory);
      } catch (error) {
        this.$refs.toast.add({
          severity: "error",
          summary: error.message,
          life: 3000,
        });
      } finally {
        this.isLoading = false;
        this.isFetchingDir = false;
      }
    },
    changeDirActive() {
      // 找出目錄中的當前資料夾並設定樣式
      const dir = this.directory?.find(
        (dir) => dir.id === this.currentFolderData?.id
      );
      this.resetDirActive(this.dirTree);
      dir.isActive = true;
    },
    resetDirActive(dir) {
      if (!dir) return;
      dir?.forEach((item) => {
        item.isActive = false;
        if (item.children) {
          this.resetDirActive(item.children);
        }
      });
    },
    updateCurrentRule(newRule) {
      if (newRule === this.currentRule) return;
      this.currentRule = newRule;
    },
    toggleRuleSettings(isShow) {
      this.isShowRuleSettings = isShow;
    },
    updateSelectedItem(newItem) {
      if (this.selectedItem === newItem) return;
      this.selectedItem = newItem;
    },
    updateNewName(newVal) {
      if (this.newName === newVal) return;
      this.newName = newVal;
    },
    updatePrevPathIds(id) {
      if (id === this.currentPathIds.at(-1)) return;
      this.prevPathIds.push(this.currentPathIds.at(-1));
    },
    updateCurrentPathIds(id) {
      this.currentPathIds = [
        ...this.directory?.filter((item) => item.id === id)[0]?.path,
        id,
      ];
    },
    getNewFolderName() {
      // 設定新增資料夾的名稱序號
      const newFolderNumbers = this.currentData
        .filter((item) => item.name.includes("新資料夾"))
        .map((item) =>
          item.name === "新資料夾"
            ? 0
            : parseInt(item.name.replace(/\D/g, ""), 10)
        )
        .sort((a, b) => a - b);

      let newFolderName = "新資料夾";
      for (let index = 0; index < newFolderNumbers.length; index++) {
        if (newFolderNumbers[index] !== index) {
          newFolderName = index ? `新資料夾(${index})` : "新資料夾";
          break;
        } else {
          newFolderName = `新資料夾(${newFolderNumbers.length})`;
        }
      }
      return newFolderName;
    },
    handleAddFolder() {
      const newFolderName = this.getNewFolderName();
      const parentId = this.currentFolderData.id;
      const path = this.currentPathIds;

      const newFolder = {
        type: "folder",
        name: newFolderName,
        parentId,
        path,
        // timeCreated: this.currentDate,
        size: 0,
        iconPath: new URL("@images/icon/folder.png", import.meta.url).href,
        isChangingName: false,
      };

      this.newFolders.push({ name: newFolderName, parentId, path });
      this.currentData?.push(newFolder);

      this.selectedItem = this.currentData?.at(-1);
      this.handleChangingName();
    },
    handleChangingName() {
      this.currentData?.forEach((data) => (data.isChangingName = false));
      this.newName = this.selectedItem.name;
      this.selectedItem.isChangingName = true;
      this.$nextTick(() => {
        const input = this.$refs?.fileView?.$refs?.newNameInput[0];
        if (input) {
          input.focus();
          input.select();
        }
      });
    },
    async handleRename() {
      if (!this.currentFolderData.admin) return;
      try {
        if (!this.newName.trim()) {
          throw new Error("名稱不可為空");
        } else if (
          // 新舊名相同
          !this.newFolders.length &&
          this.selectedItem.name === this.newName
        ) {
          this.selectedItem.isChangingName = false;
          return;
        }

        this.isLoading = true;

        // 判斷要檢查是否重名的資料是 新資料夾 ? 舊資料夾 ? 或檔案
        const itemsToCheck = this.newFolders.length
          ? this.currentData
              .filter((item) => item.type === "folder")
              .slice(0, -1)
          : this.selectedItem.type === "folder"
          ? this.currentData.filter(
              (item) =>
                item.type === "folder" && item.id !== this.selectedItem.id
            )
          : this.currentData.filter(
              (item) =>
                item.type === this.selectedItem.type &&
                item.id !== this.selectedItem.id
            );

        // 判斷當前資料夾內是否有重名的資料夾或檔案
        const isNameExist = itemsToCheck.some(
          (item) =>
            item.name.toLowerCase() === this.newName.trim().toLowerCase()
        );

        if (isNameExist) {
          throw new Error(`名稱"${this.newName}"已存在`);
          // 新建的資料夾
        } else if (this.newFolders.length && !this.selectedItem?.id) {
          this.newFolders.at(-1).name = this.newName;
          this.selectedItem.name = this.newName;
          await this.postNewFolder();
          this.selectedItem.isChangingName = false;
          return;
          // 已存在的資料夾或檔案
        } else {
          this.selectedItem.name = this.newName;
          const { id, name } = this.selectedItem;
          await renameItem(id, { name });
          await this.fetchDirectory();
        }
      } catch (error) {
        this.setConfirm({
          type: "wrong",
          message: error.message,
          imgPath: new URL("@images/popup_state/wrong.png", import.meta.url).href,
          centerBtn: {
            text: "我知道了",
            command: () => (this.confirmOption.isShow = false),
          },
          isShow: true,
        });
      }
      this.isLoading = false;
      this.selectedItem.isChangingName = false;
      await this.fetchDataByFolderId(this.currentFolderData?.id);
    },
    postNewFolder: _.debounce(async function () {
      if (!this.newFolders.length || this.selectedItem.isChangingName) return;
      try {
        await addFolder({ newFolders: this.newFolders });
        await this.fetchDirectory();
      } catch (error) {
        this.$refs.toast.add({
          severity: "error",
          summary: error.message,
          life: 3000,
        });
      } finally {
        await this.fetchDataByFolderId(this.currentFolderData?.id);
        this.newFolders = [];
        this.isLoading = false;
      }
    }, 800),
    async handleSearch(keyword) {
      keyword = keyword.trim();
      if (!keyword) return;

      try {
        this.isLoading = true;
        this.currentData = [];
        const data = await searchFile({ keyword });
        this.currentData = data;
        if (this.currentData?.length) {
          this.addAttributes();
          this.layout = "grid";
          this.isThumbnail = true;
        }
      } catch (error) {
        this.$refs.toast.add({
          severity: "error",
          summary: error.message,
          life: 3000,
        });
      } finally {
        this.isLoading = false;
        this.isSearched = true;
      }
    },
    updateLayout(newVal) {
      if (this.layout === newVal) return;
      this.layout = newVal;
    },
    updateIsThumbnail(newVal) {
      if (this.isThumbnail === newVal) return;
      this.isThumbnail = newVal;
    },
    updateIsLoading(newVal) {
      if (this.isLoading === newVal) return;
      this.isLoading = newVal;
    },
  },
  provide() {
    return {
      fetchDataByFolderId: this.fetchDataByFolderId,
      checkFolderPermission: this.checkFolderPermission,
      dataInit: this.dataInit,
      fetchDirectory: this.fetchDirectory,
      changeDirActive: this.changeDirActive,
      updateLayout: this.updateLayout,
      updateIsThumbnail: this.updateIsThumbnail,
      updatePrevPathIds: this.updatePrevPathIds,
    };
  },
  computed: {
    // currentDate() {
    //   const today = new Date();
    //   const year = today.getFullYear();
    //   const month = String(today.getMonth() + 1).padStart(2, "0");
    //   const day = String(today.getDate()).padStart(2, "0");
    //   const hour = String(today.getHours()).padStart(2, "0");
    //   const min = String(today.getMinutes()).padStart(2, "0");
    //   return `${year}/${month}/${day} ${hour}:${min}`;
    // },
    currentPathNames() {
      let result = [];
      for (let i = 0; i < this.currentPathIds.length; i++) {
        const id = this.currentPathIds[i];
        const item = this.directory?.find((item) => item.id === id);
        if (item) {
          result.push(item.name);
        }
      }
      result.shift();
      return result;
    },
  },
  created() {
    this.fetchRootData();
    this.fetchDirectory();
  },
  mounted() {
    if (this.$root.ruleId) {
      this.currentRule = {
        id: this.$root.ruleId,
        title: this.$root.ruleName,
      };
    }
  },
};
</script>
<style>
@media only screen and (min-width: 768px) {
  /* 設定 Dialog 滾動條的樣式 */
  .p-dialog-content::-webkit-scrollbar {
    width: 9px !important;
    height: 9px !important;
    background-color: #fff !important;
  }

  /* 滾動條的軌道樣式 */
  .p-dialog-content::-webkit-scrollbar-track {
    border-radius: 10px !important;
  }

  /* 滾動條的滑塊樣式 */
  .p-dialog-content::-webkit-scrollbar-thumb {
    border-radius: 4px !important;
    background-color: #00000080 !important;
    border-right: solid 2px #fff !important;
  }
}

.p-confirm-popup {
  box-shadow: none !important;
  border: 1px solid #d0d5dd !important;
}

.p-confirm-popup::before,
.p-confirm-popup::after {
  content: none;
}

.p-button-label {
  font-weight: bold !important;
}

.p-confirm-popup-reject:hover {
  background: #e5e7eb !important;
  color: #1d2939 !important;
  border: 1px solid #1d2939 !important;
}

.loader {
  width: 60px;
  height: 60px;
  border: 5px solid #4f46e5;
  border-bottom-color: transparent;
  border-radius: 50%;
  display: inline-block;
  box-sizing: border-box;
  animation: rotation 1s linear infinite;
}

@keyframes rotation {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
</style>
