<?php

namespace App\Modules\Demand\Models;

use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Model;

class ListLayout extends Model
{
    //
    use SoftDeletes;
    protected $fillable = [
        'id',
        'demand_layout_id',
        'payload',
        'created_by',
        'updated_by'
    ];

    protected $casts = [
        'payload' => 'collection'
    ];

    public function layout()
    {
        return $this->belongsTo(DemandLayout::class,'demand_layout_id');
    }

}
