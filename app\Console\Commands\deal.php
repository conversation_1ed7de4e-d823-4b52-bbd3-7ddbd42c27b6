<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Modules\Demand\Models\Demand as ModelsDemand;
use App\Modules\Demand\Models\DemandLog as ModelsDemandLog;
use App\Modules\Demand\Models\Employee as ModelsEmployee;
use App\OrgUnit;

class deal extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'command:deal';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        // Get the demand and demandlog records
        // $demands = ModelsDemand::withTrashed()->get();
        $demandLogs = ModelsDemandLog::all();

        // Iterate through each demand record
        // foreach ($demands as $demand) {

        //     if (isset($demand->payload['applicant'])) {
        //         continue;
        //     }

        //     // Get the employee record
        //     $employee = ModelsEmployee::withTrashed()->find($demand->created_by);
        //     if (!$employee) {
        //         continue;
        //     }
        //     dd($employee);

        //     // Get the org unit record
        //     $orgUnit = OrgUnit::find($employee->org_unit_id);

        //     // Update the payload field with the applicant details
        //     $payload = json_decode($demand->payload, true);
        //     $payload['applicant'] = [
        //         'dep' => $orgUnit->payload['fullname'],
        //         'name' => $employee->payload['name'],
        //         'title' => $employee->payload['job_title']
        //     ];
        //     $demand->payload = json_encode($payload);
        //     // $demand->save();
        // }
        // dd(878);
        // Iterate through each demandlog record
        foreach ($demandLogs as $demandLog) {
            // Get the employee record
            if (isset($demandLog->payload['applicant'])) {
                continue;
            }

            // // Get the employee record
            $employee = ModelsEmployee::with(['orgs' => fn ($it) => $it->withTrashed()])->withTrashed()->find($demandLog->created_by);
            if (!$employee) {
                continue;
            }

            // Get the org unit record
            $orgUnit = ModelsEmployee::with('orgs')->withTrashed()->first();
            // Update the payload field with the applicant details

            $payload = $demandLog->payload;
            $payload['applicant'] = [
                'dep' => $employee->orgs->first()->payload['fullname'],
                'name' => $employee->payload['name'],
                'title' => $employee->payload['job_title']
            ];

            $demandLog->update(['payload' => $payload]);

            // dd($demandLog);

            // $demandLog->save();
        }
        return 0;
    }
}
