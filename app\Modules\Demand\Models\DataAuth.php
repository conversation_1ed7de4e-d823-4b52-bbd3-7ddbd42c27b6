<?php

namespace App\Modules\Demand\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
class DataAuth extends Model
{
    use SoftDeletes;
    //
    use \Staudenmeir\EloquentJsonRelations\HasJsonRelationships;
    protected $primaryKey = 'layout_id';
    protected $fillable = [
        'layout_id', 'payload', 'metadata'
    ];

    protected $casts = [
        'payload' => 'collection',
        'metadata' => 'collection'
    ];

    public function users()
    {
        return $this->belongsToJson(Employee::class, 'payload->user_list');
    }
}
