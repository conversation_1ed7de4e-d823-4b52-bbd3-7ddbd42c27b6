<template>
    <div>
        <room-list v-if="action == 0" />
        <room-content v-if="action == 1" />
    </div>
</template>
<script>
import axios from "axios";
import Button from "primevue/button";
import roomList from "./room-list.vue";
import roomContent from "./room-content.vue";
export default {
    components: {
        Button,
        roomList,
        roomContent,
    },
    data() {
        return {
            action: 0,
            id:0,
            url: "/api/par/postulate",
        };
    },
    watch: {},
    mounted() {},
    methods: {},
};
</script>
<style></style>
