<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Modules\par\Services\ReserveService;
use Illuminate\Support\Facades\Mail;
use App\Mail\notification as MailNotification; // 確保 MailNotification 類存在且可自動載入
use Carbon\Carbon;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Log;
use Exception;

class TestIcsEmail extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:test-ics-email {--email= : The recipient email address for the test.}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Sends a test email with an ICS attachment to the specified email address.';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        try {
            $this->info('開始發送 ICS 測試郵件...');

            $testEmail = $this->option('email');
            if (!$testEmail) {
                // 您可以在 .env 或 config/mail.php 中設定一個預設測試郵箱
                $defaultTestEmail = Config::get('mail.test_recipient_email', '<EMAIL>');
                $testEmail = $this->ask('請輸入接收測試郵件的郵箱地址：', $defaultTestEmail);
            }

            if (!filter_var($testEmail, FILTER_VALIDATE_EMAIL)) {
                $this->error('提供的郵箱地址無效。');
                Log::warning('TestIcsEmail: 無效的郵箱地址嘗試: ' . $testEmail);
                return 1;
            }

            $this->line("收件郵箱設定為: {$testEmail}");

            // 檢查 ReserveService 是否存在
            if (!class_exists(ReserveService::class)) {
                $this->error('错误：ReserveService 類別未找到。請確認命名空間和路徑是否正確。');
                Log::error('TestIcsEmail: ReserveService 類別未找到。');
                return 1;
            }
            $reserveService = new ReserveService();

            // 測試事件數據
            $eventName = '系統日曆整合測試事件';
            $eventStartDate = Carbon::now()->addDays(3)->setHour(10)->setMinute(0)->setSecond(0); // 明天上午10點
            $eventEndDate = (clone $eventStartDate)->addHours(1); // 事件持續1小時
            $eventReason = '這是一個自動化測試，用於驗證 ICS 日曆邀請的生成和郵件發送功能。';
            $eventRemark = "如果您收到此郵件，表示 ICS 功能基本正常。\n事件時間：" . $eventStartDate->format('Y-m-d H:i:s') . " 到 " . $eventEndDate->format('Y-m-d H:i:s');


            $this->line("準備為事件 '{$eventName}' 生成 ICS 文件...");

            // 生成 ICS
            // 根據 ReserveController, newIcs($name, $date, $reason, $remark) - cancelReserve line 730
            // 或 newIcs($name, $date, $remark) - reservePostulate line 432
            // 我們將使用包含 reason 的四參數版本，假設它是較完整的版本
            $ics = $reserveService->newIcs($eventName, $eventStartDate, $eventReason, $eventRemark);

            if (!$ics) {
                $this->error('ICS 文件生成失敗。請檢查 ReserveService::newIcs 方法的實現和日誌。');
                Log::error('TestIcsEmail: ICS 文件生成返回 null 或 false。');
                return 1;
            }
            $this->info('ICS 文件已成功生成。');


            $eventStartDate->setTimezone('Asia/Taipei');
            $eventEndDate->setTimezone('Asia/Taipei');
            // 準備郵件內容 (模擬 ReserveController 中的 $emailMsg 結構)
            $emailMsg = [
                [
                    'user' => '系統測試帳號',
                    'name' => $eventName,
                    'date' => $eventStartDate->format('Y/m/d'),
                    'time' => [
                        $eventStartDate->format('H:i') . '-' . $eventStartDate->format('H:i'),
                        $eventEndDate->format('H:i') . '-' . $eventEndDate->format('H:i')
                    ],
                    'reason' => $eventReason,
                    'remark' => nl2br(htmlspecialchars($eventRemark, ENT_QUOTES, 'UTF-8')),
                    'notified' => ['測試相關設備1', '測試相關設備2'], // 模擬的相關設備資訊
                    // 'isRevised' => '設施', // 如果是修改事件
                    // 'isCanceled' => '設施', // 如果是取消事件
                ]
            ];

            $mailSubject = (Config::get('mail.sys_title') ?: '系統通知') . ' - ICS 功能測試郵件';

            // 準備傳遞給 MailNotification 的數據
            $data = [
                'subject' => $mailSubject,
                'msg' => $emailMsg,
                'email' => [$testEmail], // MailNotification 可能會使用此 email 鍵來確定其他收件人或資訊
                'ics' => $ics
            ];

            $this->line("正在將郵件加入隊列，發送到 {$testEmail}...");

            // 檢查 MailNotification 是否存在
            if (!class_exists(MailNotification::class)) {
                $this->error('错误：MailNotification 類別未找到。請確認其已定義並可自動載入。');
                Log::error('TestIcsEmail: MailNotification 類別未找到。');
                return 1;
            }

            // 發送郵件
            Mail::to($testEmail)->queue(new MailNotification($data));
            // 如果郵件隊列未設定或想立即發送，可以考慮使用 Mail::send()
            // Mail::send(new MailNotification($data));


            $this->info("ICS 測試郵件已成功加入隊列，將發送到 {$testEmail}。");
            $this->comment("請檢查您的郵件隊列 worker 是否正在運行，並稍後檢查郵箱 {$testEmail}。");
            Log::info("TestIcsEmail: ICS 測試郵件已成功為 {$testEmail} 加入隊列。");

            return Command::SUCCESS; // 等同於 return 0;

        } catch (Exception $e) {
            $this->error('發送 ICS 測試郵件時發生錯誤： ' . $e->getMessage());
            $this->error('詳細錯誤請查看日誌文件。');
            Log::error('TestIcsEmail: 執行過程中捕獲到例外 - ' . $e->getMessage() . "\n" . $e->getTraceAsString());
            return Command::FAILURE; // 等同於 return 1;
        }
    }

}
