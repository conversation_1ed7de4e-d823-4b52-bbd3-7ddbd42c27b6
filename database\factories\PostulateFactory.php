<?php

namespace Database\Factories;

/** @var \Illuminate\Database\Eloquent\Factory $factory */

use App\Modules\par\Models\Postulate;
use Faker\Generator as Faker;

$factory->define(Postulate::class, function (Faker $faker) {


    $notified = [];
    for ($i = 1; $i <= random_int(1, 4); $i++) {
        $notified[] = $faker->numberBetween(7, 200);
    }
    return [
        'company_id' => 1,
        'name' => $faker->unique()->word,
        'payload' => [
            "people" => $faker->numberBetween(1, 150),
            "place" => $faker->word,
            "remark" => $faker->text(50),
            // "picture" => '',
            "apparatus_type" => $faker->words(random_int(1, 9), false),
            "notified" => $notified
        ],
        'metadata' => [],
        'created_by' => 1,
        'updated_by' => 1,
    ];
});
