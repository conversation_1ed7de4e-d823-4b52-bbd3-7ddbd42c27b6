<template>
    <div>
        <div class="flex flex-wrap w-full pb-4">
            <div class="pr-6">
                <label class="text-sm text-gray-400">{{
                    $root.reserve == 0 ? "設備" : "設施"
                }}</label>
                <br />
                <Dropdown
                    class="lg:w-44 xl:w-48 rounded-md shadow"
                    optionLabel="name"
                    optionValue="id"
                    data-key="id"
                    placeholder=""
                    :filter="true"
                    :options="dropdown_options"
                    @input="
                        $emit('selectedId', $event);
                        invalidSearch();
                    "
                    v-model="selected_id"
                />
            </div>
            <div class="pr-6">
                <label class="text-sm text-gray-400">日期</label>
                <br />
                <Calendar
                    class="lg:w-44 xl:w-48 rounded-md shadow"
                    placeholder="選擇查詢日期"
                    dateFormat="yy-mm-dd"
                    :manualInput="false"
                    :minDate="todayMidnight"
                    @input="
                        $emit('selectedDate', $event);
                        invalidSearch();
                    "
                    :value="selected_date"
                />
            </div>
            <div class="pr-6">
                <label class="text-sm text-gray-400">預約起始時間</label>
                <br />
                <Dropdown
                    class="lg:w-44 xl:w-48 rounded-md shadow"
                    optionLabel="time"
                    optionValue="id"
                    data-key="id"
                    placeholder="請選擇開始時間"
                    :options="begin_time"
                    @input="updateSelectedTime('updateSelectedBegin', $event)"
                    :value="selected_begin"
                    optionDisabled="disabled"
                />
            </div>
            <div class="pr-6">
                <label class="text-sm text-gray-400">預約結束時間</label>
                <br />
                <Dropdown
                    class="lg:w-44 xl:w-48 rounded-md shadow"
                    optionLabel="time"
                    optionValue="id"
                    data-key="id"
                    placeholder="請選擇結束時間"
                    :options="end_time"
                    @input="updateSelectedTime('updateSelectedEnd', $event)"
                    :value="selected_end"
                    optionDisabled="disabled"
                />
            </div>
            <div>
                <label></label>
                <br />
                <Button
                    @click="$emit('search')"
                    label="查詢"
                    class="w-28"
                    :disabled="if_search"
                />
            </div>
        </div>
    </div>
</template>
<script>
import axios from "axios";
import Button from "primevue/button";
import Dropdown from "primevue/dropdown";
import Calendar from "primevue/calendar";
export default {
    components: {
        Button,
        Dropdown,
        Calendar,
    },
    mounted() {
        this.fetchList();
    },
    props: [
        "searchInput",
        "selected_date",
        "begin_time",
        "end_time",
        "selected_begin",
        "selected_end",
        "if_search",
        "todayMidnight",
    ],
    data() {
        return {
            dropdown_options: [],
            selected_id:
                this.searchInput.app_id == null ? 0 : this.searchInput.app_id,
        };
    },
    methods: {
        fetchList() {
            axios
                .get("/api/par/reserve/pa/dropdown", {
                    params: {
                        type:
                            this.$root.reserve == 0 ? "apparatus" : "postulate",
                    },
                })
                .then((response) => {
                    this.dropdown_options = response.data;
                    this.dropdown_options.splice(0, 0, {
                        id: 0,
                        name: this.$root.reserve == 0 ? "所有設備" : "所有設施",
                    });
                })
                .catch((error) => {
                    console.error(error);
                });
        },
        invalidSearch() {
            if (
                this.selected_id != null &&
                this.selected_date != null &&
                this.selected_begin != null &&
                this.selected_end != null
            ) {
                this.$emit("disableButton", false);
            }
        },
        async updateSelectedTime(selectedTime, $event) {
            await this.$emit(selectedTime, $event);
            this.invalidSearch();
        },
    },
};
</script>
