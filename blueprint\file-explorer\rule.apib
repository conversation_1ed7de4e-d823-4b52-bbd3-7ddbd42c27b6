FORMAT: 1A
# rule-api
建檔規則相關的API

## rule [api/explorer/rule/:id]

### use a rule [PUT]
套用新規則

+ Response 200 (application/json)
    + Attributes
        + status: 200 (number) - HTTP 狀態碼
        + message: "OK" (string) - 成功訊息

+ Response 400 (application/json)
    + Attributes
        + status: 400 (number) - HTTP 狀態碼
        + message: "error message" (string) - 錯誤訊息


### delete a rule [DELETE]
刪除建檔規則

+ Response 200 (application/json)
    + Attributes
        + status: 200 (number) - HTTP 狀態碼
        + message: "OK" (string) 成功訊息

+ Response 400 (application/json)
    + Attributes
        + status: 400 (number) - HTTP 狀態碼
        + message: "error message" (string) - 錯誤訊息


## rule settings [api/explorer/rules]

### get all rules [GET]
獲取所有建檔規則

+ Response 200 (application/json)
    + Attributes
        + status: 200 (number) - HTTP 狀態碼
        + message: "OK" (string) - 成功訊息
        + data: (array)
            + (object)
                + id: 1 (string, required) - 規則id
                + title: "建檔規則 1" (string, required) - 規則名稱
                + folder: (array)
                    + (object)
                        + sort: 1 (number, required) - 順序
                        + type: 0 (number, required) - 命名方式 (0:直接命名 1:掃描命名)
                        + value: "財務部" (string, required) - 取名文字
                + file: (array)
                    + (object)
                        + sort: 1 (number, required) - 順序
                        + type: 0 (number, required) - 命名方式 (0:直接命名 1:掃描命名)
                        + value: "廠商名稱:" (string, required) - 取名文字
                        + hasDash: false (boolean, required) - 檔名間是否要以-隔開

+ Response 400 (application/json)
    + Attributes
        + status: 400 (number) - HTTP 狀態碼
        + message: "error message" (string) - 錯誤訊息


### update rules [PUT]
更新建檔規則

+ Request (application/json)
    + Attributes
        + rules: (array)
            + (object)
                + title: "建檔規則 1" (string, required) - 規則名稱
                + folder: (array)
                    + (object)
                        + sort: 1 (number, required) - 順序
                        + type: 0 (number, required) - 命名方式 (0:直接命名 1:掃描命名)
                        + value: "財務部" (string, required) - 取名文字
                + file: (array)
                    + (object)
                        + sort: 1 (number, required) - 順序
                        + type: 0 (number, required) - 命名方式 (0:直接命名 1:掃描命名)
                        + value: "廠商名稱:" (string, required) - 取名文字
                        + hasDash: false (boolean, required) - 檔名間是否要以-隔開

+ Response 200 (application/json)
    + Attributes
        + status: 200 (number) - HTTP 狀態碼
        + message: "OK" (string) 成功訊息

+ Response 400 (application/json)
    + Attributes
        + status: 400 (number) - HTTP 狀態碼
        + message: "error message" (string) - 錯誤訊息
