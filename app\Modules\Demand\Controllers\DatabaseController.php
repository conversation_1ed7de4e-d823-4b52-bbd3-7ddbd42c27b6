<?php

namespace App\Modules\Demand\Controllers;


use Illuminate\Http\Request;
use Illuminate\Support\Facades\Session;
use App\Http\Controllers\Controller;
use App\Modules\Demand\Models\Database;


class DatabaseController extends Controller
{
    protected $company_id;
    protected $user_id;

    public function __construct()
    {
        $this->company_id = Session::get('CompanyId');
        $this->user_id = Session::get('employee_id');
    }

    public function add(Request $request)
    {
        $DbModel = Database::create(
            [
                'name' => $request->get('name'),
                'payload' => (object)[
                    'columns' => [
                        (object)[
                            'data' => [''],
                            'name' => '',
                            'id' => 1
                        ]
                    ],
                    'company_id' => $this->company_id
                ],
                'created_by' => $this->user_id,
                'updated_by' => $this->user_id
            ]
        );
        return $DbModel->id;
    }

    public function fetchDbSetting()
    {
        $databases = Database::where(
            'payload->company_id',
            $this->company_id
        )->select('payload->columns as columns', 'id', 'name')
            ->orderBy('id', 'asc')
            ->get();

        return $databases->each(function ($db) {
            $db->columns = collect(json_decode($db['columns']));
        });
    }

    public function delete(Request $request)
    {
        $id = $request['id'];
        if ($id) {

            $database = Database::where('id', $id)->first();
            $database->delete();
            return 1;
        }
        return 0;
    }

    public function update(Request $request)
    {
        if ($request->get('id') && $request->get('name')) {

            $db = Database::where('id', $request->get('id'))->first();
            $db->setAttribute('name', $request->get('name'))
                ->setAttribute('updated_by', $this->user_id);

            if ($request->get('columns')) {
                $db->setAttribute('payload->columns', json_decode($request->get('columns')));
            }
            $db->save();
            return 1;
        }
        return 0;
    }
}
