<template>
    <div>
        <div v-if="action == 0">
            <div class="flex flex-wrap w-full pb-4">
                <div class="pr-6">
                    <label class="text-sm text-gray-400">申請類型</label>
                    <br />
                    <Dropdown
                        class="lg:w-44 xl:w-48 rounded-md shadow"
                        v-model="layoutId"
                        :filter="true"
                        :options="layouts"
                        optionLabel="name"
                        optionValue="id"
                        data-key="id"
                        placeholder="所有申請"
                    />
                </div>

                <div class="pr-6">
                    <label class="text-sm text-gray-400">申請結果</label>
                    <br />
                    <Dropdown
                        class="lg:w-44 xl:w-48 rounded-md shadow"
                        :options="[
                            { value: -1, name: '不分' },
                            { value: 2, name: '同意' },
                            { value: 3, name: '駁回' },
                        ]"
                        optionLabel="name"
                        optionValue="value"
                        v-model="result"
                    />
                </div>

                <div class="pr-6">
                    <label class="text-sm text-gray-400">起始日期</label>
                    <br />
                    <Calendar
                        class="lg:w-44 xl:w-48 rounded-md shadow"
                        placeholder="選擇查詢起始日期"
                        dateFormat="yy-mm-dd"
                        :manualInput="false"
                        v-model="start"
                        :value="start"
                    />
                </div>

                <div class="pr-6">
                    <label class="text-sm text-gray-400">結束日期</label>
                    <br />
                    <Calendar
                        class="lg:w-44 xl:w-48 rounded-md shadow"
                        placeholder="選擇查詢結束日期"
                        dateFormat="yy-mm-dd"
                        :manualInput="false"
                        v-model="end"
                    />
                </div>

                <div class="mt-8 pr-8">
                    <RadioButton
                        id="month"
                        class="mb-1"
                        value="month"
                        v-model="month"
                        @click="getMonthDate()"
                    />
                    <label for="month" class="text-gray-400 cursor-pointer ml-1"
                        >最近一個月</label
                    >
                </div>
                <div class="pr-8 mt-6">
                    <div class="w-52 grid p-fluid">
                        <div class="col-12 md:col-4">
                            <div class="p-inputgroup">
                                <span class="p-inputgroup-addon">
                                    <i class="pi pi-search"></i>
                                </span>
                                <InputText
                                    class="mr-2 lg:w-44 xl:w-48 mb-2"
                                    placeholder="搜尋關鍵字"
                                    type="text"
                                    v-model="key_word"
                                />
                            </div>
                        </div>
                    </div>
                </div>
                <div class="pr-8">
                    <br />
                    <Button
                        :disabled="start == null && end == null"
                        @click="
                            sPer = 10;
                            fetchHistoryAPI(1, sPer);
                        "
                        label="查詢"
                        class="w-28"
                    />
                </div>
            </div>
            <br />
            <div class="thead">
                <div class="flex w-1/6 my-auto">
                    <!-- <input
                        v-if="signOff"
                        :value="data.id"
                        class="mr-0 md:mr-4 my-auto"
                        type="checkbox"
                        v-model="checked"
                    /> -->
                    <p class="text-sm text-gray-400">需求名稱</p>
                </div>
                <div
                    :class="
                        col_index === columns.length - 1
                            ? 'w-16'
                            : column == '部門' || column == '摘要'
                            ? 'w-1/6 hidden sm:block'
                            : 'w-1/6'
                    "
                    v-for="(column, col_index) in columns"
                    :key="col_index"
                >
                    <span class="text-sm text-gray-400"> {{ column }}</span>
                </div>
            </div>
            <div class="relative">
                <template
                    v-if="
                        (dataLists.data && !dataLists.data.length) ||
                        dataLists.data == undefined
                    "
                >
                    <DataStatus
                        :data="dataLists"
                        :status="status"
                        :col="getNumberOfCol()"
                    />
                </template>
                <div
                    v-else
                    v-for="(list, listIndex) in dataLists.data"
                    :key="list.id"
                >
                    <DemandRequestList
                        :data="list"
                        :layer="'outside'"
                        :signOff="0"
                        @click="getListsRow(listIndex)"
                        :checked="false"
                    />
                </div>
            </div>
        </div>
        <div v-if="action == 1">
            <DemandRequestList
                :data="list"
                :layer="'inner'"
                :signOff="2"
                :editCustomForm="false"
                @back="(val) => (action = val)"
            />
        </div>
        <Paginator
            v-if="dataLists.data?.length && action == 0 && status != 1"
            :sPer="sPer"
            :data="dataLists"
            @page="fetchHistoryAPI"
        />
        <ScrollTop :threshold="1" icon="pi pi-arrow-up" />
    </div>
</template>
<script>
import axios from "axios";
import Button from "primevue/button";
import Dropdown from "primevue/dropdown";
import Calendar from "primevue/calendar";
import RadioButton from "primevue/radiobutton";
import DemandRequestList from "@/demand/common/demand-request-list";
import DataStatus from "@/demand/common/data-status";
import ScrollTop from "primevue/scrolltop";
import InputText from "primevue/inputtext";
import Paginator from "@/demand/common/Paginator";
export default {
    components: {
        Button,
        Dropdown,
        Calendar,
        RadioButton,
        DemandRequestList,
        DataStatus,
        ScrollTop,
        InputText,
        Paginator,
    },
    data() {
        return {
            action: 0,
            layouts: [],
            layoutId: 0,
            result: -1,
            columns: ["單號", "申請日期", "摘要", "申請狀態"],
            month: null,
            start: null,
            end: null,
            dataLists: [],
            current_page: 1,
            fullDataLists: [],

            // 以下是內頁
            list: {},
            status: 0,
            key_word: null,
            sPer: 10,
        };
    },
    mounted() {
        this.fetchTypeDropdown();
        // this.getLists(0, 10)
    },
    methods: {
        fetchHistoryAPI(page, per) {
            this.status = 1;
            this.dataLists.data = undefined;
            axios
                .get("/api/demand/submit/history", {
                    params: {
                        start: this.start,
                        end: this.end,
                        id: this.layoutId,
                        result: this.result,
                        key_word: this.key_word,
                        page: page,
                        per: per,
                        no: this.no,
                    },
                })
                .then((response) => {
                    this.status = 2;
                    this.dataLists = response.data ?? [];
                })
                .catch((error) => {
                    this.status = 2;
                    console.error(error);
                    this.dataLists = undefined;
                });
        },
        fetchTypeDropdown() {
            axios
                .get("/api/demand/submit/demand/type/dropdown")
                .then((response) => {
                    this.layouts = response.data ?? [];
                })
                .catch((error) => {
                    console.error(error);
                });
        },
        getMonthDate() {
            // let date = new Date();
            // let month = date.setMonth(date.getMonth() - 1);
            // let monthAgo = new Date(month);

            const lastMonth = new Date();
            // 上月
            lastMonth.setMonth(lastMonth.getMonth() - 1);
            // 0點0分0秒0毫秒
            lastMonth.setHours(0, 0, 0, 0);

            this.start = lastMonth;
            this.end = new Date();
        },
        getListsRow(index) {
            this.action = 1;
            this.list = this.dataLists.data[index];
            //.....
        },
        getNumberOfCol() {
            return window.innerWidth < 640 ? 3 : 4;
        },
    },
};
</script>
