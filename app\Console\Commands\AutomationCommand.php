<?php

namespace App\Console\Commands;

use App\Jobs\DeleteDemandLayouts;
use App\Jobs\DeleteTempImage;
use App\Jobs\MoveReserveToLog;
use Illuminate\Support\Facades\Log;
use Illuminate\Console\Command;

class AutomationCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'automation:cmd';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'command to run scheduled jobs';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        Log::info('daily start');
        DeleteDemandLayouts::dispatch();
        // DeleteTempImage::dispatch();
        // MoveReserveToLog::dispatch();
        Log::info('daily end');
        return 0;
    }
}
