<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

class AddCodesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        DB::table('codes')->insert([
            ['code_kind' => 'AB', 'code_parent' => '', 'code_id' => 'customList', 'sort_order' => '14', 'nm_zh_tw' => '自訂表單', 'nm_zh_cn' => '', 'nm_en_us' => ''],
        ]);
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {

    }
}
