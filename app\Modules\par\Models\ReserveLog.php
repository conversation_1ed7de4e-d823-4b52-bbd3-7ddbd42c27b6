<?php

namespace App\Modules\par\Models;

use App\Modules\Demand\Models\Employee;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\Session;

class ReserveLog extends Model
{
    use SoftDeletes;
    protected $fillable = ['company_id', 'employee_id', 'pa_id', 'type', 'payload', 'metadata', 'created_by', 'updated_by'];
    protected $casts = ['payload' => 'collection', 'metadata' => 'collection'];

    public function scopeCompany($query)
    {
        return $query->where('company_id', Session::get('CompanyId'));
    }

    public function employee()
    {
        return $this->belongsTo(Employee::class, 'employee_id');
    }

    public function postulate()
    {
        return $this->belongsTo(Postulate::class, 'pa_id', 'id');
    }
}
