<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddValueToCodeTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        DB::table('codes')->insert([
            ['code_kind' => 'AH', 'code_parent' => '', 'code_id' => 'notify4', 'sort_order' => '3', 'nm_zh_tw' => '於time建檔完成，成功success個、失敗failed個，點擊查看結果', 'nm_zh_cn' => '', 'nm_en_us' => '']
        ]);
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        DB::table('codes')
        ->where('code_kind' , 'AH')
        ->where( 'code_id' , 'notify4')
        ->delete();
    }
}
