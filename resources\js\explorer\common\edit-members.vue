<template>
  <Dialog
    :visible.sync="computedEditDialogVisible"
    :modal="true"
    :containerStyle="{ maxWidth: '90%', width: '488px' }"
    position="right"
    dismissableMask
    class="justify-center"
  >
    <template #header>
      <div class="flex items-center px-2">
        <button
          v-if="editType === 'folder'"
          class="w-8 h-8 mr-3 rounded-full hover:bg-gray-200 transition"
          @click="handleGoBack"
        >
          <img src="@images/icon/back.svg" class="w-6 p-0.5 mx-auto" />
        </button>
        <h1 class="text-3xl font-bold text-explorerPrimary">
          {{ title }}
        </h1>
      </div>
    </template>
    <div class="flex flex-col mt-6 px-2">
      <div class="text-sm pb-24 relative" style="color: #98a2b3">人員</div>
      <MultiSelect
        :options="employees"
        :optionLabel="(employee) => `${employee.org_name} ${employee.name}`"
        optionValue="id"
        placeholder="請選擇人員"
        :filter="true"
        filterPlaceholder="搜尋人員"
        class="mb-16 w-52 absolute top-36"
        :value="selectedMemberIds"
        @change="($event) => $emit('updateSelectedMemberIds', $event.value)"
      >
        <template #option="slotProps">
          <div>
            <span>{{ slotProps.option.org_name }}</span>
            <span>{{ slotProps.option.name }}</span>
          </div>
        </template>
      </MultiSelect>
    </div>
    <template #footer>
      <div class="flex justify-end items-center px-2 pb-2">
        <button
          @click="handleGoBack"
          style="border: 1px solid #1d2939"
          class="bg-white hover:bg-gray-200 px-6 py-3 text-explorerPrimary rounded-lg font-bold text-base"
        >
          <span>取消</span>
        </button>
        <button
          @click="handleEditMembers"
          class="bg-primary hover:bg-indigo-900 px-6 py-3 ml-6 text-white rounded-lg font-bold text-base"
        >
          <span>確定</span>
        </button>
      </div>
    </template>
  </Dialog>
</template>

  <script>
import axios from "axios";
import Dialog from "primevue/dialog";
import MultiSelect from "primevue/multiselect";

export default {
  name: "EditMembers",
  components: { Dialog, MultiSelect },
  props: {
    editType: { type: String, required: true },
    isShow: { type: Boolean, required: true },
    title: { type: String, required: true },
    selectedMemberIds: { type: Array, required: true },
  },
  data() {
    return {
      employees: [],
    };
  },
  methods: {
    async fetchEmployees() {
      try {
        const { data } = await axios("/api/demand/employees");
        this.employees = data;
      } catch (error) {
        this.$refs.toast.add({
          severity: "error",
          summary: error.message,
          life: 3000,
        });
      }
    },
    handleEditMembers() {
      this.$emit("onUpdate", this.selectedMemberIds);
    },
    handleGoBack() {
      this.computedEditDialogVisible = false;
      this.$emit("togglePermissionDialog", true);
    },
  },
  computed: {
    computedEditDialogVisible: {
      get() {
        return this.isShow;
      },
      set(value) {
        this.$emit("toggleEditDialog", value);
      },
    },
  },
  mounted() {
    this.fetchEmployees();
  },
};
</script>
<style scoped>
.p-dialog .p-dialog-footer button {
  margin: 0 0 0 24px !important;
}
</style>
