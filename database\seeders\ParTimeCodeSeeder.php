<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class ParTimeCodeSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        DB::table('codes')->insert([
            ['code_kind' => 'AK', 'code_parent' => '', 'code_id' => '0', 'sort_order' => '0', 'nm_zh_tw' => '08:00-08:30'],
            ['code_kind' => 'AK', 'code_parent' => '', 'code_id' => '1', 'sort_order' => '1', 'nm_zh_tw' => '08:30-09:00'],
            ['code_kind' => 'AK', 'code_parent' => '', 'code_id' => '2', 'sort_order' => '2', 'nm_zh_tw' => '09:00-09:30'],
            ['code_kind' => 'AK', 'code_parent' => '', 'code_id' => '3', 'sort_order' => '3', 'nm_zh_tw' => '09:30-10:00'],
            ['code_kind' => 'AK', 'code_parent' => '', 'code_id' => '4', 'sort_order' => '4', 'nm_zh_tw' => '10:00-10:30'],
            ['code_kind' => 'AK', 'code_parent' => '', 'code_id' => '5', 'sort_order' => '5', 'nm_zh_tw' => '10:30-11:00'],
            ['code_kind' => 'AK', 'code_parent' => '', 'code_id' => '6', 'sort_order' => '6', 'nm_zh_tw' => '11:00-11:30'],
            ['code_kind' => 'AK', 'code_parent' => '', 'code_id' => '7', 'sort_order' => '7', 'nm_zh_tw' => '11:30-12:00'],
            ['code_kind' => 'AK', 'code_parent' => '', 'code_id' => '8', 'sort_order' => '8', 'nm_zh_tw' => '12:00-12:30'],
            ['code_kind' => 'AK', 'code_parent' => '', 'code_id' => '9', 'sort_order' => '9', 'nm_zh_tw' => '12:30-13:00'],
            ['code_kind' => 'AK', 'code_parent' => '', 'code_id' => '10', 'sort_order' => '10', 'nm_zh_tw' => '13:00-13:30'],

            ['code_kind' => 'AK', 'code_parent' => '', 'code_id' => '11', 'sort_order' => '11', 'nm_zh_tw' => '13:30-14:00'],
            ['code_kind' => 'AK', 'code_parent' => '', 'code_id' => '12', 'sort_order' => '12', 'nm_zh_tw' => '14:00-14:30'],
            ['code_kind' => 'AK', 'code_parent' => '', 'code_id' => '13', 'sort_order' => '13', 'nm_zh_tw' => '14:30-15:00'],
            ['code_kind' => 'AK', 'code_parent' => '', 'code_id' => '14', 'sort_order' => '14', 'nm_zh_tw' => '15:00-15:30'],
            ['code_kind' => 'AK', 'code_parent' => '', 'code_id' => '15', 'sort_order' => '15', 'nm_zh_tw' => '15:30-16:00'],
            ['code_kind' => 'AK', 'code_parent' => '', 'code_id' => '16', 'sort_order' => '16', 'nm_zh_tw' => '16:00-16:30'],
            ['code_kind' => 'AK', 'code_parent' => '', 'code_id' => '17', 'sort_order' => '17', 'nm_zh_tw' => '16:30-17:00'],
            ['code_kind' => 'AK', 'code_parent' => '', 'code_id' => '18', 'sort_order' => '18', 'nm_zh_tw' => '17:00-17:30'],
            ['code_kind' => 'AK', 'code_parent' => '', 'code_id' => '19', 'sort_order' => '19', 'nm_zh_tw' => '17:30-18:00'],
            ['code_kind' => 'AK', 'code_parent' => '', 'code_id' => '20', 'sort_order' => '20', 'nm_zh_tw' => '18:00-18:30'],
            ['code_kind' => 'AK', 'code_parent' => '', 'code_id' => '21', 'sort_order' => '21', 'nm_zh_tw' => '18:30-19:00']
        ]);
    }
}
