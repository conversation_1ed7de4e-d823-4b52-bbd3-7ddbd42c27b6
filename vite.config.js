import { defineConfig } from 'vite';
import vue2 from "@vitejs/plugin-vue2";
import laravel from 'laravel-vite-plugin';
import path from 'path';

export default defineConfig({
    base: './',
    plugins: [
        vue2(),
        laravel({
            input: [
                // Main entries
                'resources/js/app.js',
                'resources/css/app.css',
                'resources/css/normalize.css',

                // Demand setting entries
                'resources/js/demand/setting/dem-setting/dem-setting.js',
                'resources/js/demand/setting/database-setting/database-setting.js',
                'resources/js/demand/setting/permission-setting/permission-setting.js',
                'resources/js/demand/setting/notify-setting/notify-setting.js',

                // Demand other entries
                'resources/js/demand/submit/submit.js',
                'resources/js/demand/signing/signing.js',
                'resources/js/demand/query/query.js',

                // Par entries
                'resources/js/par/setting/setting.js',
                'resources/js/par/reserve/reserve.js',
                'resources/js/par/my-reserves/my-reserves.js',
                'resources/js/par/permission-setting/permission-setting.js',

                // Explorer entries
                'resources/js/explorer/file-lib/file-lib.js',
                'resources/js/explorer/permission-settings/permission-settings.js',
            ],
            refresh: true,
        }),
    ],
    resolve: {
        alias: [
            {
                find: "@",
                replacement: path.resolve("resources/js"),
            },
            {
                find: "@images",
                replacement: path.resolve("resources/images"),
            },
            {
                find: 'vue',
                replacement: 'vue/dist/vue.esm.js'
            },
            {
                find: /^primevue\/((?!config|resources).*)*$/,
                replacement: "primevue/$1/$1.umd.js",
            },
        ],
        extensions: [".vue", ".js"],
    },
    optimizeDeps: {
        include: ['vue'],
    },
    build: {
        rollupOptions: {
            output: {
                chunkFileNames: 'assets/js/[name]-[hash].js',
                entryFileNames: 'assets/js/[name]-[hash].js',
                assetFileNames: 'assets/[ext]/[name]-[hash].[ext]'
            },
        },
        chunkSizeWarningLimit: 500, // 500KB
        minify: "terser", // 需先安裝 terser 下面的配置才會生效
        terserOptions: {
            // 清理 console 和 debugger
            compress: {
                drop_console: true,
                drop_debugger: true
            },
            format: {
                // 刪除所有註解
                comments: false,
            },
        }
    }
});
