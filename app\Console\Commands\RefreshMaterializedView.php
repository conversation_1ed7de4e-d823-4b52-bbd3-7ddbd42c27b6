<?php

namespace App\Console\Commands;

use App\Jobs\RefreshDemandQueryArchive;
use App\Jobs\RefreshDemandQueryRecent;
use App\Jobs\ReserveNotify;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;


class RefreshMaterializedView extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'refresh:materialized-view';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Refresh materialized view';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {

        Log::info('RefreshDemandQueryRecent start');
        RefreshDemandQueryRecent::dispatch();
        Log::info('RefreshDemandQueryRecent end');

        Log::info('RefreshDemandQueryArchive start');
        RefreshDemandQueryArchive::dispatch();
        Log::info('RefreshDemandQueryArchive end');
    }
}
