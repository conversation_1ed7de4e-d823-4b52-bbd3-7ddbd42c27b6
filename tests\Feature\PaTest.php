<?php

namespace Tests\Feature;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;
use Illuminate\Foundation\Testing\WithoutMiddleware;

class PaTest extends TestCase
{
    // use WithoutMiddleware;

    /**
     *  test.
     *
     * @return void
     */
    public function testFetchApparatus()
    {
        $response = $this->withSession([
            'CompanyId' => 1,
            'employee_id' => 583,
            'timezone' => 'Asia/Taipei',

        ])
            ->get('/api/par/apparatus');

        $response->assertStatus(200);
        // $response->assertSuccessful();
    }
}
