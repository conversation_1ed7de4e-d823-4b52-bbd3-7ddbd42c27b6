<?php

namespace App\Modules\par\Controllers;

use App\Http\Controllers\Controller;
use App\Jobs\CancelReserveNotify;
use App\Modules\Demand\Models\OrgUnitMember;
use App\Modules\par\Models\Apparatus;
use App\Modules\par\Models\Postulate;
use App\Modules\par\Models\Reserve;
use App\Modules\par\Services\PaService;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Session;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class PaController extends Controller
{
    protected $paService;
    protected $company_id;
    protected $user_id;
    protected $code;
    protected $timezone;
    public function __construct()
    {
        $this->paService = new PaService();
        $this->company_id = Session::get('CompanyId');
        $this->user_id = Session::get('employee_id');
        $this->timezone = Session::get('timezone');
        // $this->timezone = 'Asia/Taipei';
    }
    public function updateImage(Request $request)
    {
        $validator = $this->paService->validatorImage($request->file());

        if ($validator->fails())
            return $this->errorReturn($validator->errors()->first());

        try {
            $file = $this->paService->uploadImage($request->file()['upload']);
        } catch (\Exception $e) {
            Log::error("圖片上傳失敗");
            Log::error($request->file());
            Log::error($e);
            return $this->errorReturn('圖片上傳失敗');
        }
        return  $file;
    }
    public function fetchPostulate(Request $request)
    {
        // 取得現在時間區段的code id
        $timeId  = $this->paService->getNowTimePeriodId();
        $currentPage = $request->input('page') ?? 1;
        $paginate = $request->per ?? 10;
        $key = $request->key;
        $today = today(Session::get('timezone'))->toISOString();
        // $today = today('asia/taipei')->toISOString();

        // 要加現在時段的判斷
        $postulate = Postulate::with([
            'reserve' => function ($query) use ($timeId, $today) {
                $query->whereJsonContains('payload->time', [$timeId])
                    ->date($today)
                    ->notRelease();
            }, 'reserve.employee'
        ])
            ->company()
            ->when($key, function ($query) use ($key) {
                return $query->where('name', 'like', '%' . $key . '%');
            })
            ->orderby('id')
            ->paginate($paginate, ['*'], 'page', $currentPage);

        $orgUnitMember = OrgUnitMember::withTrashed()->with('orgUnit')->get();
        $list = $postulate->map(function ($item) use ($orgUnitMember) {
            $state =   $item->reserve->count() > 0 ? '預約中' : '可預約';
            $subscribers =  $item->reserve->map(function ($item) use ($orgUnitMember) {

                $time = PaService::idToTimePeriod($item->payload['time']);
                $emp = $item->employee;
                $orgUnit = $orgUnitMember->firstWhere('employee_id',  $emp->id) ?  $orgUnitMember->firstWhere('employee_id',  $emp->id)->orgUnit : null;

                return [
                    'reserveId' => $item->id,
                    'name' => $emp ? $emp->payload['name'] : '',
                    "org_unit" =>  $orgUnit ?  $orgUnit->payload['name'] : '',
                    'date' =>  carbon::parse($item->payload['date'])->setTimezone($this->timezone)->format('Y/m/d'),

                    'apparatus_type' => isset($item->payload['apparatus']) ? array_column($item->payload['apparatus'], 'type') : [],
                    // 'apparatus_id' => $item->payload['apparatus_id'],
                    'time' => $time,
                    'reason' => $item->payload['reason'],
                    'remark' => $item->payload['remark'],
                ];
            });

            return [
                "id" => $item->id,
                "image_url" => $item->payload['picture'][0]['file_show'],
                "name" => $item->name,
                "state" => $state,
                "subscribers" => $subscribers,
                'is_open' => $item->metadata['is_open'] ?? true,
            ];
        });

        return [
            'data' => $list,
            'current_page' => (int)$currentPage,
            'per_page' => (int)$paginate,
            'total' => $postulate->total(),
            'last_page' => $postulate->lastPage()
        ];
    }
    public function showPostulate(Request $request)
    {
        $id = $request->id;
        $postulate = Postulate::find($id);
        if (!$postulate)  return $this->errorReturn('無此筆資料');
        $detail = [
            'name' => $postulate->name,
            'people' => $postulate->payload['people'],
            'place' => $postulate->payload['place'],
            'images' => $postulate->payload['picture'],
            'remark' => $postulate->payload['remark'],
            'apparatus_type' => $postulate->payload['apparatus_type'],
            'notified' => $postulate->payload['notified'],
        ];
        return $detail;
    }
    public function createPostulate(Request $request)
    {
        $form = $request->all();
        $validator = $this->paService->validatorPostulate($form);

        if ($validator->fails())
            return $this->errorReturn($validator->errors()->first());

        try {
            $file = $this->paService->updateImageName($request->images, 'postulate');
        } catch (\Exception $e) {
            Log::error("圖片改名失敗");
            Log::error($request->file());
            Log::error($e);
            return $this->errorReturn('圖片上傳失敗');
        }
        $payload = [
            "people" => $form['people'],
            "place" =>  $form['place'],
            "remark" =>  $form['remark'],
            "picture" =>   $file,
            "apparatus_type" => $form['apparatus_type'],
            "notified" => $form['notified']
        ];

        Postulate::create([
            'company_id' =>  $this->company_id,
            'name' =>  $form['name'],
            'payload' => $payload,
            'metadata' => [],
            'created_by' => $this->user_id,
            'updated_by' => $this->user_id
        ]);

        return  ['state' => 1,];
    }
    public function updatePostulate(Request $request)
    {
        $form = $request->all();
        $id = intval($form['id']);
        $validator = $this->paService->validatorPostulate($form, true, $id);

        if ($validator->fails())
            return $this->errorReturn($validator->errors()->first());

        $postulate =  Postulate::find($id);

        try {
            $file = $this->paService->updateImageName($form['images'], 'postulate', $postulate);
        } catch (\Exception $e) {
            Log::error("圖片改名失敗");
            Log::error($request->file());
            Log::error($e);
            return $this->errorReturn('圖片上傳失敗');
        }

        $payload = [
            "people" => $form['people'],
            "place" =>  $form['place'],
            "remark" =>  $form['remark'],
            "picture" =>   $file,
            "apparatus_type" => $form['apparatus_type'],
            "notified" => $form['notified']
        ];
        $postulate->name =  $form['name'];
        $postulate->payload =  $payload;
        $postulate->updated_by =   $this->user_id;
        $update = $postulate->save();

        return  [
            'state' => $update,
        ];
    }

    public function deletePostulate(Request $request)
    {
        $id = $request->id;

        // 除了當下的預約以外，如果有尚未發生的預約，要刪除且通知
        $reserve = $this->paService->checkReserve('postulate', $id);
        try {
            DB::beginTransaction();
            if ($reserve['hasNotStarted']) {
                $this->paService->cancelReserveAndNotify($reserve['reserve'], 'postulate', false);
            }

            Postulate::destroy($id);
            DB::commit();
            return ['state' => 1];
        } catch (Exception $e) {
            Log::error($e);
            DB::rollBack();
            return $this->errorReturn('系統錯誤!');
        }
    }

    public function fetchApparatus(Request $request)
    {
        // 取得現在時間區段的code id
        $timeId  = $this->paService->getNowTimePeriodId();
        $userId=$this->user_id;
        $currentPage = $request->input('page') ?? 1;
        $paginate = $request->per ?? 10;
        $key = $request->key;
        // $today = now()->setTimezone($this->timezone)->format('Y-m-d');
        $today = today(Session::get('timezone'))->toISOString();
        // 要加現在時段的判斷
        $apparatus = Apparatus::with([
            'reserve' => function ($query) use ($timeId, $today) {
                $query->whereJsonContains('payload->time', [$timeId])
                    ->date($today)
                    ->notRelease();
            }, 'reserve.employee'
        ])->whereJsonContains('payload->notified',collect(intval($userId)))
            ->company()
            ->when($key, function ($query) use ($key) {
                return $query->where('name', 'like', '%' . $key . '%')
                    ->orwhere('payload->no', 'like', '%' . $key . '%');
            })
            ->orderby('id')
            ->paginate($paginate, ['*'], 'page', $currentPage);
        $orgUnitMember = OrgUnitMember::withTrashed()->with(['orgUnit', 'employee'])->get();
        $list = $apparatus->map(function ($item) use ($orgUnitMember) {

            $amount = $item->reserve->sum(function ($item) {
                return $item->payload['amount'];
            });
            $state =  $item->payload['amount'] > $amount ? '可預約' : '預約中';
            $subscribers =  $item->reserve->map(function ($item) use ($orgUnitMember) {
                $emp = $item->employee;
                $orgUnit = $orgUnitMember->firstWhere('employee_id',  $emp->id) ?  $orgUnitMember->firstWhere('employee_id',  $emp->id)->orgUnit : null;
                $time = PaService::idToTimePeriod($item->payload['time']);
                return [
                    'reserveId' => $item->id,
                    'name' =>  $item->employee->payload['name'],
                    "org_unit" =>  $orgUnit ?  $orgUnit->payload['name'] : '',
                    'date' =>  carbon::parse($item->payload['date'])->setTimezone($this->timezone)->format('Y/m/d'),
                    'amount' => $item->payload['amount'],
                    'time' => $time,
                    'reason' => $item->payload['reason'],
                    'remark' => $item->payload['remark']
                ];
            });
            //新增領取人資料
            $recipients = $item->metadata['recipients'] ?? [];
            if(!empty($recipient)){
                $recipients->map(function ($recipient) use ($orgUnitMember) {
                    $empName = $orgUnitMember->firstWhere('employee_id',  $recipient->employee_id) ?  $orgUnitMember->firstWhere('employee_id',  $recipient->employee_id)->employee->payload['name'] : null;
                    $orgUnitName = $orgUnitMember->firstWhere('employee_id',  $recipient->employee_id) ?  $orgUnitMember->firstWhere('employee_id',  $recipient->employee_id)->orgUnit->payload['name'] : null;
                    return [
                        'name' => $orgUnitName . ' ' . $empName,
                        'time' => $recipient->time,
                        'remark' => $recipient->remark,
                    ];
                });
            }

            return [
                "id" => $item->id,
                "image_url" => $item->payload['picture'][0]['file_show'],
                "name" => $item->name,
                // 剩餘數量
                "amount" => (int)$item->payload['amount'] - (int)$amount,
                "total_amount" => $item->payload['amount'],
                "no" => $item->payload['no'],
                "state" => $state,
                "subscribers" => $subscribers,
                "recipients" => $recipients,
                'is_open' => $item->metadata['is_open'] ?? true,
            ];
        });

        return [
            'data' => $list,
            'current_page' => (int)$currentPage,
            'per_page' => (int)$paginate,
            'total' => $apparatus->total(),
            'last_page' => $apparatus->lastPage()
        ];
    }
    public function showApparatus(Request $request)
    {
        $id = $request->id;
        $apparatus = Apparatus::find($id);

        if (!$apparatus)  return $this->errorReturn('無此筆資料');
        $detail = [
            'name' => $apparatus->name,
            'no' => $apparatus->payload['no'],
            'type' => $apparatus->payload['type'],
            'amount' => $apparatus->payload['amount'],
            'remark' => $apparatus->payload['remark'],
            'images' => $apparatus->payload['picture'],
            'org_unit_id' => $apparatus->payload['org_unit_id'],
            'notified' => $apparatus->payload['notified']  ?? null,
        ];
        return $detail;
    }
    public function createApparatus(Request $request)
    {
        $form = $request->all();

        $validator = $this->paService->validatorApparatus($form);

        if ($validator->fails())
            return $this->errorReturn($validator->errors()->first());

        try {
            $file = $this->paService->updateImageName($request->images, 'apparatus');
        } catch (Exception $e) {
            Log::error("圖片改名失敗");
            Log::error($request->file());
            Log::error($e);
            return $this->errorReturn('圖片上傳失敗');
        }
        $payload = [
            "type" => $form['type'],
            "no" =>  $form['no'],
            "amount" =>  $form['amount'],
            "remark" =>  $form['remark'],
            "picture" =>   $file,
            "org_unit_id" => $form['org_unit_id'],
            "notified" => $form['notified'] ?? []
        ];
        Apparatus::create([
            'company_id' =>  $this->company_id,
            'name' =>  $form['name'],
            'payload' => $payload,
            'metadata' => [],
            'created_by' => $this->user_id,
            'updated_by' => $this->user_id
        ]);

        return  ['state' => 1,];
    }

    public function updateApparatus(Request $request)
    {
        $form = $request->all();
        $id = intval($form['id']);
        $validator = $this->paService->validatorApparatus($form, true, $id);

        if ($validator->fails())
            return $this->errorReturn($validator->errors()->first());

        $apparatus =  Apparatus::find($id);
        if ($this->paService->checkApparatusReserve($apparatus, $form['amount']))
            return $this->errorReturn('設備預約中，無法減少數量');



        try {
            $file = $this->paService->updateImageName($form['images'], 'apparatus', $apparatus);
        } catch (\Exception $e) {
            Log::error("圖片改名失敗");
            Log::error($form['images']);
            Log::error($e);
            return $this->errorReturn('圖片上傳失敗');
        }

        $payload = [
            "type" => $form['type'],
            "no" =>  $form['no'],
            "amount" =>  $form['amount'],
            "remark" =>  $form['remark'],
            "picture" =>   $file,
            "org_unit_id" => $form['org_unit_id'],
            "notified" => $form['notified'] ?? []
        ];
        $apparatus->name =  $form['name'];
        $apparatus->payload =  $payload;
        $apparatus->updated_by =   $this->user_id;
        $update = $apparatus->save();

        return  ['state' => $update,];
    }


    public function deleteApparatus(Request $request)
    {
        $id = $request->id;

        // 除了當下的預約以外，如果有尚未發生的預約，要刪除且通知
        try {
            DB::beginTransaction();
            Apparatus::destroy($id);

            $reserve = $this->paService->checkReserve('apparatus', $id);
            if ($reserve['hasNotStarted']) {
                $this->paService->cancelReserveAndNotify($reserve['reserve'], 'apparatus', false);
            }

            DB::commit();
            return ['state' => 1];
        } catch (Exception $e) {
            Log::error($e);
            DB::rollBack();
            return $this->errorReturn('系統錯誤!');
        }
    }
    public function createRecipients(Request $request)
    {
        $form = $request->all();

        $validator = $this->paService->validatorRecipients($form);

        if ($validator->fails())
            return $this->errorReturn($validator->errors()->first());
        try {
            $apparatus = Apparatus::find($request->apparatus_id);
            $recipients = isset($apparatus->metadata['recipients']) ? collect($apparatus->metadata['recipients']) : collect([]);
            $recipients->push([
                'employee_id' => $request->employee_id,
                'time' => now()->toISOstring(),
                'remark' => $request->remark
            ]);
            $apparatus->forceFill(['metadata->recipients'=> $recipients])->save();
            return  ['state' => 1,];
        } catch (Exception $e) {
            Log::error($e);
            return  $this->errorReturn($e);
        }
    }
    public function deleteRecipients(Request $request)
    {
        $form = $request->all();

        $validator = $this->paService->validatorRecipients($form);

        if ($validator->fails())
            return $this->errorReturn($validator->errors()->first());
        try {

            $apparatus = Apparatus::find($request->apparatus_id);
            if(!$apparatus||!isset($apparatus->metadata['recipients']))
                return $this->errorReturn("找不到資料");
            $eid=$request->employee_id;
            $recipients = $apparatus->metadata['recipients'];
            $recipients=collect($recipients)->filter(function ($recipient)use($eid) {
                return $recipient['employee_id']!=$eid;
            });
            $apparatus->setAttribute('metadata->recipients',$recipients);
            $apparatus->save();
            return  ['state' => 1,];
        } catch (Exception $e) {
            Log::error($e);
            return  $this->errorReturn($e);
        }
    }
    public function close(Request $request)
    {
        if (
            empty($request->input('id')) ||
            empty($request->input('type')) ||
            ($request->input('open') == null && !is_bool($request->input('open')))
        )
            return $this->errorReturn('系統錯誤');

        $id = $request->input('id');
        $type = $request->input('type');
        $open = $request->input('open');
        $model = $this->paService->fetchModel($type);

        try {
            DB::beginTransaction();
            $model->find($request->input('id'))
                ->forceFill(['metadata->is_open' => $open])
                ->save();

            if (!$open) {
                // 除了當下的預約以外，如果有尚未發生的預約，要刪除且通知
                $reserve = $this->paService->checkReserve($type, $id);
                if ($reserve['hasNotStarted']) {
                    $this->paService->cancelReserveAndNotify($reserve['reserve'], $type, true);
                }
            }

            DB::commit();
            return ['state' => 1];
        } catch (Exception $e) {
            Log::error($e);
            DB::rollBack();
            return $this->errorReturn('系統錯誤!');
        }
    }
}
